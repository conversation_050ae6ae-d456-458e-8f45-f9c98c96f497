// Simple test script to validate team API endpoints
const testTeamAPI = async () => {
  const baseURL = 'http://localhost:4000/api';
  
  console.log('🧪 Testing Team Management API Endpoints...\n');

  // Test 1: Get Team Stats
  try {
    console.log('1. Testing GET /api/team/stats');
    const statsResponse = await fetch(`${baseURL}/team/stats`);
    console.log(`   Status: ${statsResponse.status}`);
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('   ✅ Team stats retrieved successfully');
      console.log(`   📊 Total members: ${stats.data?.totalMembers || 0}`);
    } else {
      console.log('   ❌ Failed to get team stats');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('');

  // Test 2: Get Team Roles
  try {
    console.log('2. Testing GET /api/team/roles');
    const rolesResponse = await fetch(`${baseURL}/team/roles`);
    console.log(`   Status: ${rolesResponse.status}`);
    if (rolesResponse.ok) {
      const roles = await rolesResponse.json();
      console.log('   ✅ Team roles retrieved successfully');
      console.log(`   👥 Available roles: ${roles.data?.length || 0}`);
    } else {
      console.log('   ❌ Failed to get team roles');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('');

  // Test 3: Get Team Members
  try {
    console.log('3. Testing GET /api/team/members');
    const membersResponse = await fetch(`${baseURL}/team/members?page=1&limit=10`);
    console.log(`   Status: ${membersResponse.status}`);
    if (membersResponse.ok) {
      const members = await membersResponse.json();
      console.log('   ✅ Team members retrieved successfully');
      console.log(`   👤 Total members: ${members.data?.total || 0}`);
    } else {
      console.log('   ❌ Failed to get team members');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('');

  // Test 4: Test Invitation Creation (without auth - should fail)
  try {
    console.log('4. Testing POST /api/team/invitations (without auth)');
    const inviteResponse = await fetch(`${baseURL}/team/invitations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        roleId: 'test-role-id',
        message: 'Welcome to the team!'
      })
    });
    console.log(`   Status: ${inviteResponse.status}`);
    if (inviteResponse.status === 401) {
      console.log('   ✅ Correctly rejected unauthorized request');
    } else {
      console.log('   ❌ Should have rejected unauthorized request');
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
  }

  console.log('\n🏁 Team API testing completed!');
};

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testTeamAPI();
}

module.exports = testTeamAPI;
