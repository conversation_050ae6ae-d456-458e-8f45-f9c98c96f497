# GlossaFlow Authentication System

This document describes the authentication system implemented in GlossaFlow using NextAuth.js v5 with Supabase integration.

## Overview

The authentication system provides:
- Email/password authentication via Supabase Auth
- OAuth authentication (Google, GitHub)
- Protected routes with middleware
- Session management with JWT
- User onboarding flow
- Automatic redirects based on authentication state

## Architecture

### Components

1. **NextAuth.js Configuration** (`src/lib/auth.ts`)
   - Configures authentication providers
   - Sets up Supabase adapter
   - Defines callbacks and pages

2. **Authentication Pages**
   - Sign In: `/auth/signin`
   - Sign Up: `/auth/signup`
   - Onboarding: `/onboarding`

3. **Protected Route Component** (`src/components/auth/ProtectedRoute.tsx`)
   - Wraps pages that require authentication
   - Handles loading states and redirects

4. **Middleware** (`src/middleware.ts`)
   - Protects routes at the edge
   - Handles automatic redirects

5. **Auth Provider** (`src/components/providers/AuthProvider.tsx`)
   - Manages session state in Redux
   - Provides session context to the app

## Authentication Flow

### Sign Up Flow
1. User visits `/auth/signup`
2. User fills out registration form
3. Account created in Supabase Auth
4. Email verification sent (if enabled)
5. User redirected to onboarding
6. Organization setup completed
7. User redirected to dashboard

### Sign In Flow
1. User visits `/auth/signin`
2. User enters credentials or uses OAuth
3. Authentication verified with Supabase
4. Session created with NextAuth.js
5. User redirected to dashboard

### OAuth Flow
1. User clicks OAuth provider button
2. Redirected to provider (Google/GitHub)
3. User authorizes application
4. Callback handled by NextAuth.js
5. User account created/linked in Supabase
6. Session created and user redirected

## Configuration

### Environment Variables

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

### Supabase Setup

1. **Enable Auth Providers** in Supabase Dashboard:
   - Email/Password
   - Google OAuth
   - GitHub OAuth

2. **Configure Redirect URLs**:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/github`
   - `https://your-project.supabase.co/auth/v1/callback`

3. **Set Site URL**: `http://localhost:3000`

## Usage Examples

### Protecting a Page

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <div>Protected content here</div>
    </ProtectedRoute>
  );
}
```

### Using Session Data

```tsx
import { useSession } from 'next-auth/react';

export function UserProfile() {
  const { data: session, status } = useSession();

  if (status === 'loading') return <div>Loading...</div>;
  if (!session) return <div>Not authenticated</div>;

  return <div>Hello {session.user?.name}</div>;
}
```

### API Route Protection

```tsx
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authConfig);

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Protected API logic here
}
```

### Sign Out

```tsx
import { signOut } from 'next-auth/react';

export function SignOutButton() {
  return (
    <button onClick={() => signOut({ callbackUrl: '/auth/signin' })}>
      Sign Out
    </button>
  );
}
```

## Security Features

### Row Level Security (RLS)
- All database tables have RLS enabled
- Users can only access data from their organizations
- Policies enforce role-based access control

### Session Security
- JWT tokens with secure secrets
- Automatic session refresh
- Secure cookie settings in production

### Route Protection
- Middleware-level protection
- Client-side route guards
- API endpoint authentication

## Testing

### Demo Accounts
For development and testing, use these demo accounts:

```
Email: <EMAIL>
Password: (set in Supabase Auth dashboard)
Role: Organization Admin

Email: <EMAIL>  
Password: (set in Supabase Auth dashboard)
Role: Translator
```

### Testing OAuth
1. Set up OAuth apps in Google/GitHub
2. Configure redirect URLs
3. Test sign-in flow
4. Verify user data synchronization

## Troubleshooting

### Common Issues

1. **OAuth Redirect Mismatch**
   - Verify redirect URLs in OAuth provider settings
   - Check NEXTAUTH_URL environment variable

2. **Session Not Persisting**
   - Verify NEXTAUTH_SECRET is set
   - Check cookie settings in production

3. **Supabase Connection Issues**
   - Verify Supabase URL and keys
   - Check RLS policies
   - Ensure user exists in auth.users table

4. **Middleware Redirect Loops**
   - Check middleware matcher configuration
   - Verify public route definitions

### Debug Mode

Enable debug logging in development:

```env
NEXTAUTH_DEBUG=true
```

This will log detailed authentication information to the console.

## Production Considerations

### Security Checklist
- [ ] Use HTTPS in production
- [ ] Set secure NEXTAUTH_SECRET
- [ ] Configure proper CORS settings
- [ ] Enable email verification
- [ ] Set up proper error handling
- [ ] Configure rate limiting
- [ ] Review RLS policies

### Performance
- Session data is cached in JWT
- Minimal database queries for auth
- Efficient middleware execution
- Optimized redirect handling

## API Reference

### Authentication Endpoints
- `GET /api/auth/session` - Get current session
- `POST /api/auth/signin` - Sign in user
- `POST /api/auth/signout` - Sign out user
- `GET /api/auth/providers` - Get available providers

### User Endpoints
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile

For more detailed API documentation, see the [API Documentation](./API.md).
