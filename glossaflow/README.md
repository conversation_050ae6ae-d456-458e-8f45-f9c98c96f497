# GlossaFlow - Professional Translation Management Platform

GlossaFlow is a professional-grade translation project management workspace designed for agencies, publishing houses, and professional translators managing complex localization projects.

## 🚀 Features

- **Professional Translation Editor**: Split-pane interface with terminology highlighting
- **Systematic Terminology Management**: Import/export, validation, consistency checking
- **Collaborative Review Workflows**: Multi-stage reviews with quality assurance
- **Real-time Collaboration**: Live editing with cursor positions and presence
- **Enterprise-grade Security**: RLS policies, authentication, rate limiting
- **Scalable Architecture**: Built for growth with modern tech stack

## 🛠 Technology Stack

- **Frontend**: Next.js 14+ (App Router), Tailwind CSS, Shadcn/ui
- **Backend**: Supabase (PostgreSQL, Storage, Auth, Realtime)
- **State Management**: Redux Toolkit with RTK Query
- **Authentication**: NextAuth.js v5 with Supabase adapter
- **Payments**: Stripe with webhooks
- **Caching**: Redis for performance optimization

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd glossaflow
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your actual values
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:4000](http://localhost:4000)

## 🔧 Environment Variables

Create a `.env.local` file with the following variables:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:4000
NEXTAUTH_SECRET=your-secret-key-here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/             # React components
│   ├── ui/                # Shadcn/ui components
│   ├── auth/              # Authentication components
│   ├── projects/          # Project management components
│   ├── translation/       # Translation editor components
│   ├── terminology/       # Terminology management components
│   ├── review/            # Review workflow components
│   └── layout/            # Layout components
├── store/                 # Redux store configuration
│   ├── api/               # RTK Query API slices
│   └── slices/            # Redux slices
├── lib/                   # Utility functions
├── hooks/                 # Custom React hooks
└── types/                 # TypeScript type definitions
```

## 🚧 Development Status

This project is currently in **Phase 1** of development. The following has been completed:

✅ **Phase 1: Foundation Setup**
- Next.js 14 project with App Router configuration
- Supabase integration setup
- Redux Toolkit with RTK Query configuration
- Shadcn/ui component library setup
- Basic layout and dashboard interface
- TypeScript type definitions
- Project structure and development environment

### Next Steps (Phase 2):
- [ ] Supabase database schema implementation
- [ ] NextAuth.js authentication system
- [ ] User registration and organization creation flows
- [ ] Basic project management features

## 📖 Documentation

For detailed development documentation, see:
- [Development Blueprint](./GLOSSAFLOW_BLUEPRINT.md) - Complete technical specifications
- [API Documentation](./docs/api.md) - API endpoint specifications
- [Database Schema](./docs/database.md) - Database design and relationships

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation for common solutions

---

**GlossaFlow** - Revolutionizing professional translation workflows with modern technology and user-centric design.
