# GlossaFlow Backend Infrastructure Setup Guide

This guide will walk you through setting up the complete backend infrastructure for GlossaFlow using command-line tools.

## Prerequisites

✅ **Already Available:**
- Supabase CLI installed and ready
- Google Cloud CLI installed and authenticated
- Google Cloud project: `gen-lang-client-**********`

## Step 1: Complete Supabase Authentication

**Current Status:** Supabase login is in progress and waiting for verification code.

1. **Complete the login process:**
   - The browser should have opened to: https://supabase.com/dashboard/cli/login?session_id=...
   - If not, open the URL manually from the terminal output
   - Complete the authentication in the browser
   - Copy the verification code from the browser
   - Enter it in the terminal when prompted

2. **Verify login:**
   ```bash
   supabase projects list
   ```

## Step 2: Create Supabase Project

Once logged in, create a new Supabase project:

```bash
# Create new project (replace with your preferred name and password)
supabase projects create "glossaflow-production" --db-password "YourSecurePassword123!" --region us-east-1

# Note the project reference ID from the output
```

## Step 3: Initialize and Link Local Supabase

```bash
# Initialize Supabase in the project directory
supabase init

# Link to your created project (replace PROJECT_REF with actual reference)
supabase link --project-ref YOUR_PROJECT_REF

# Deploy our migration files
supabase db push
```

## Step 4: Get Supabase Credentials

```bash
# Get project details
supabase projects api-keys --project-ref YOUR_PROJECT_REF

# This will show:
# - anon key (public)
# - service_role key (secret)
```

## Step 5: Set up Google Cloud OAuth

### 5.1 Enable Required APIs

```bash
# Enable OAuth2 API
gcloud services enable oauth2.googleapis.com
gcloud services enable iamcredentials.googleapis.com
```

### 5.2 Create OAuth Consent Screen

1. Go to: https://console.cloud.google.com/apis/credentials/consent?project=gen-lang-client-**********
2. Choose "External" user type
3. Fill in required fields:
   - App name: GlossaFlow
   - User support email: your email
   - Developer contact: your email
4. Add authorized domains: `localhost`
5. Save and continue through the steps

### 5.3 Create OAuth 2.0 Client

1. Go to: https://console.cloud.google.com/apis/credentials?project=gen-lang-client-**********
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Application type: "Web application"
4. Name: "GlossaFlow Web Client"
5. Authorized redirect URIs:
   - `http://localhost:4000/api/auth/callback/google`
   - `https://YOUR_PROJECT_REF.supabase.co/auth/v1/callback`
6. Save and copy the Client ID and Client Secret

## Step 6: Set up Google Cloud Storage

### 6.1 Create Storage Bucket

```bash
# Create bucket with unique name
BUCKET_NAME="glossaflow-storage-$(date +%s)"
gsutil mb -p gen-lang-client-********** -l us-east1 gs://$BUCKET_NAME

# Set public read access for uploaded files
gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME

# Configure CORS for web uploads
echo '[
  {
    "origin": ["http://localhost:4000"],
    "method": ["GET", "POST", "PUT", "DELETE"],
    "responseHeader": ["Content-Type"],
    "maxAgeSeconds": 3600
  }
]' > cors.json

gsutil cors set cors.json gs://$BUCKET_NAME
rm cors.json
```

### 6.2 Create Service Account

```bash
# Create service account
gcloud iam service-accounts create glossaflow-storage \
    --display-name="GlossaFlow Storage Service Account"

# Grant storage admin role
gcloud projects add-iam-policy-binding gen-lang-client-********** \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.admin"

# Create and download service account key
gcloud iam service-accounts keys create ./storage-service-account.json \
    --iam-account="<EMAIL>"
```

## Step 7: Configure Supabase Authentication

1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/YOUR_PROJECT_REF/auth/providers
2. Enable Google provider:
   - Client ID: (from Step 5.3)
   - Client Secret: (from Step 5.3)
3. Go to URL Configuration: https://supabase.com/dashboard/project/YOUR_PROJECT_REF/auth/url-configuration
4. Set:
   - Site URL: `http://localhost:4000`
   - Redirect URLs: `http://localhost:4000/**`

## Step 8: Update Environment Variables

Create/update your `.env.local` file with the real credentials:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:4000
NEXTAUTH_SECRET=YOUR_GENERATED_SECRET

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://YOUR_PROJECT_REF.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=YOUR_SERVICE_ROLE_KEY

# Google OAuth Configuration
GOOGLE_CLIENT_ID=YOUR_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_CLIENT_SECRET

# Google Cloud Storage Configuration
GCS_BUCKET_NAME=YOUR_BUCKET_NAME
GCS_PROJECT_ID=gen-lang-client-**********
GCS_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GCS_SERVICE_ACCOUNT_KEY_PATH=./storage-service-account.json

# GitHub OAuth (Optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

## Step 9: Generate NextAuth Secret

```bash
# Generate secure secret
openssl rand -base64 32
```

## Step 10: Test the Setup

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Test authentication:**
   - Visit: http://localhost:4000/auth/signin
   - Try Google OAuth login
   - Verify user creation in Supabase dashboard

3. **Test database connectivity:**
   - Check if demo data is visible in dashboard
   - Verify RLS policies are working

## Automated Setup Script

For convenience, you can also use the automated setup script:

```bash
./scripts/setup-backend.sh
```

This script will guide you through the process with prompts and automate where possible.

## Troubleshooting

### Common Issues:

1. **Supabase CLI not authenticated:**
   ```bash
   supabase logout
   supabase login
   ```

2. **Google Cloud permissions:**
   ```bash
   gcloud auth application-default login
   ```

3. **OAuth redirect mismatch:**
   - Verify redirect URIs in Google Cloud Console
   - Check Supabase auth configuration

4. **Database migration errors:**
   ```bash
   supabase db reset
   supabase db push
   ```

## Security Notes

- Keep `storage-service-account.json` secure and never commit to version control
- Use strong passwords for database
- Regularly rotate API keys and secrets
- Enable 2FA on all cloud accounts

## Next Steps

Once setup is complete:
1. Test all authentication flows
2. Verify file upload functionality
3. Test database operations
4. Deploy to production environment
