'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Zap, AlertCircle, Upload, FileText } from 'lucide-react';

const extractTermsSchema = z.object({
  projectId: z.string().min(1, 'Project is required'),
  sourceText: z.string().min(10, 'Source text must be at least 10 characters'),
  sourceLanguage: z.string().optional(),
  clearExisting: z.boolean().default(false),
});

type ExtractTermsFormData = z.infer<typeof extractTermsSchema>;

interface ExtractTermsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExtract: (data: ExtractTermsFormData) => void;
  isLoading?: boolean;
}

// Mock projects data - in real app, this would come from API
const mockProjects = [
  { id: '1', name: 'Medical Translation Project', sourceLanguage: 'English' },
  { id: '2', name: 'Technical Documentation', sourceLanguage: 'English' },
  { id: '3', name: 'Legal Contract Translation', sourceLanguage: 'English' },
];

const languages = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'ja', label: 'Japanese' },
  { value: 'ko', label: 'Korean' },
  { value: 'zh', label: 'Chinese' },
];

export function ExtractTermsDialog({
  open,
  onOpenChange,
  onExtract,
  isLoading = false,
}: ExtractTermsDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileContent, setFileContent] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<ExtractTermsFormData>({
    resolver: zodResolver(extractTermsSchema),
    defaultValues: {
      clearExisting: false,
    },
  });

  const projectId = watch('projectId');
  const sourceText = watch('sourceText');
  const clearExisting = watch('clearExisting');

  const handleFormSubmit = async (data: ExtractTermsFormData) => {
    try {
      await onExtract(data);
      reset();
      setSelectedFile(null);
      setFileContent('');
    } catch (error) {
      console.error('Failed to extract terms:', error);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedFile(null);
    setFileContent('');
    onOpenChange(false);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setFileContent(content);
      setValue('sourceText', content);
    };
    reader.readAsText(file);
  };

  const handleProjectChange = (value: string) => {
    setValue('projectId', value);
    const project = mockProjects.find(p => p.id === value);
    if (project) {
      const langCode = languages.find(l => l.label === project.sourceLanguage)?.value || 'en';
      setValue('sourceLanguage', langCode);
    }
  };

  const selectedProject = mockProjects.find(p => p.id === projectId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Extract Term Candidates
          </DialogTitle>
          <DialogDescription>
            Automatically extract terminology candidates from source text using AI-powered analysis.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Project Selection */}
          <div className="space-y-2">
            <Label htmlFor="projectId">Project *</Label>
            <Select onValueChange={handleProjectChange} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Select a project" />
              </SelectTrigger>
              <SelectContent>
                {mockProjects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    <div className="flex flex-col">
                      <span>{project.name}</span>
                      <span className="text-xs text-gray-500">{project.sourceLanguage}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.projectId && (
              <p className="text-sm text-red-600">{errors.projectId.message}</p>
            )}
          </div>

          {/* Source Language */}
          {selectedProject && (
            <div className="space-y-2">
              <Label htmlFor="sourceLanguage">Source Language</Label>
              <Select
                onValueChange={(value) => setValue('sourceLanguage', value)}
                defaultValue="en"
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((language) => (
                    <SelectItem key={language.value} value={language.value}>
                      {language.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* File Upload Option */}
          <div className="space-y-2">
            <Label>Source Text Input Method</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <div className="text-center">
                <Upload className="mx-auto h-8 w-8 text-gray-400" />
                <div className="mt-2">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                      Upload a text file
                    </span>
                    <input
                      id="file-upload"
                      type="file"
                      accept=".txt,.doc,.docx,.pdf"
                      onChange={handleFileUpload}
                      className="sr-only"
                      disabled={isLoading}
                    />
                  </label>
                  <span className="text-sm text-gray-500"> or paste text below</span>
                </div>
                {selectedFile && (
                  <div className="mt-2 flex items-center justify-center gap-2 text-sm text-green-600">
                    <FileText className="h-4 w-4" />
                    {selectedFile.name}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Source Text */}
          <div className="space-y-2">
            <Label htmlFor="sourceText">Source Text *</Label>
            <Textarea
              id="sourceText"
              placeholder="Paste or type the source text to extract terms from..."
              rows={8}
              {...register('sourceText')}
              disabled={isLoading}
              value={fileContent || sourceText}
              onChange={(e) => {
                if (!selectedFile) {
                  setValue('sourceText', e.target.value);
                }
              }}
            />
            {errors.sourceText && (
              <p className="text-sm text-red-600">{errors.sourceText.message}</p>
            )}
            {sourceText && (
              <p className="text-xs text-gray-500">
                {sourceText.length} characters, ~{Math.ceil(sourceText.split(' ').length)} words
              </p>
            )}
          </div>

          {/* Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="clearExisting"
                checked={clearExisting}
                onCheckedChange={(checked) => setValue('clearExisting', !!checked)}
                disabled={isLoading}
              />
              <Label htmlFor="clearExisting" className="text-sm">
                Clear existing pending candidates for this project
              </Label>
            </div>

            {clearExisting && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This will remove all pending term candidates for the selected project before extracting new ones.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !projectId || !sourceText}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Extracting...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Extract Terms
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
