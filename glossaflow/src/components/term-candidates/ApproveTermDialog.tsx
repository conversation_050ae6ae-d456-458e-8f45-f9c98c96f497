'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, TrendingUp, Target } from 'lucide-react';
import { type TermCandidate } from '@/lib/api/term-candidates';

const approveTermSchema = z.object({
  targetTerm: z.string().min(1, 'Target term is required'),
  targetLanguage: z.string().min(1, 'Target language is required'),
  category: z.string().min(1, 'Category is required'),
  context: z.string().optional(),
  usageNotes: z.string().optional(),
  addToGlossary: z.boolean().default(false),
});

type ApproveTermFormData = z.infer<typeof approveTermSchema>;

interface ApproveTermDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  candidate: TermCandidate | null;
  onApprove: (data: ApproveTermFormData) => void;
  isLoading?: boolean;
}

const languages = [
  'Japanese',
  'Spanish',
  'French',
  'German',
  'Korean',
  'Chinese (Simplified)',
  'Chinese (Traditional)',
  'Portuguese',
  'Italian',
  'Russian',
];

const categories = [
  'technical',
  'general',
  'character',
  'location',
  'concept',
  'business',
  'medical',
  'legal',
];

export function ApproveTermDialog({
  open,
  onOpenChange,
  candidate,
  onApprove,
  isLoading = false,
}: ApproveTermDialogProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<ApproveTermFormData>({
    resolver: zodResolver(approveTermSchema),
    defaultValues: {
      addToGlossary: false,
    },
  });

  const targetLanguage = watch('targetLanguage');
  const category = watch('category');
  const addToGlossary = watch('addToGlossary');

  const handleFormSubmit = async (data: ApproveTermFormData) => {
    try {
      await onApprove(data);
      reset();
    } catch (error) {
      console.error('Failed to approve term:', error);
    }
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  if (!candidate) return null;

  const getConfidenceBadge = (score: number) => {
    if (score >= 0.8) {
      return <Badge variant="secondary" className="bg-green-100 text-green-800">High Confidence</Badge>;
    } else if (score >= 0.5) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium Confidence</Badge>;
    } else {
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Low Confidence</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Check className="h-5 w-5 text-green-600" />
            Approve Term Candidate
          </DialogTitle>
          <DialogDescription>
            Review and approve this term candidate to add it to your terminology database.
          </DialogDescription>
        </DialogHeader>

        {/* Candidate Information */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900">Candidate Information</h3>
            {getConfidenceBadge(candidate.confidenceScore)}
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Source Term:</span>
              <div className="font-medium">{candidate.candidateTerm}</div>
            </div>
            <div>
              <span className="text-gray-600">Frequency:</span>
              <div className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4 text-blue-600" />
                <span className="font-medium">{candidate.frequency}</span>
              </div>
            </div>
            <div>
              <span className="text-gray-600">Confidence Score:</span>
              <div className="flex items-center gap-1">
                <Target className="h-4 w-4 text-purple-600" />
                <span className="font-medium">{(candidate.confidenceScore * 100).toFixed(0)}%</span>
              </div>
            </div>
            <div>
              <span className="text-gray-600">Project:</span>
              <div className="font-medium">{candidate.project?.name || 'Unknown'}</div>
            </div>
          </div>

          {candidate.contextSentence && (
            <div>
              <span className="text-gray-600 text-sm">Context:</span>
              <div className="text-sm bg-white p-2 rounded border italic">
                "{candidate.contextSentence}"
              </div>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Target Term */}
          <div className="space-y-2">
            <Label htmlFor="targetTerm">Target Term *</Label>
            <Input
              id="targetTerm"
              placeholder="Enter the translated term"
              {...register('targetTerm')}
              disabled={isLoading}
            />
            {errors.targetTerm && (
              <p className="text-sm text-red-600">{errors.targetTerm.message}</p>
            )}
          </div>

          {/* Target Language */}
          <div className="space-y-2">
            <Label htmlFor="targetLanguage">Target Language *</Label>
            <Select onValueChange={(value) => setValue('targetLanguage', value)} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Select target language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map((language) => (
                  <SelectItem key={language} value={language}>
                    {language}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.targetLanguage && (
              <p className="text-sm text-red-600">{errors.targetLanguage.message}</p>
            )}
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <Select 
              onValueChange={(value) => setValue('category', value)} 
              disabled={isLoading}
              defaultValue={candidate.assignedCategory || undefined}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((cat) => (
                  <SelectItem key={cat} value={cat}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.category && (
              <p className="text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          {/* Context */}
          <div className="space-y-2">
            <Label htmlFor="context">Context</Label>
            <Textarea
              id="context"
              placeholder="Additional context for this term (optional)"
              rows={3}
              {...register('context')}
              disabled={isLoading}
              defaultValue={candidate.contextSentence || ''}
            />
          </div>

          {/* Usage Notes */}
          <div className="space-y-2">
            <Label htmlFor="usageNotes">Usage Notes</Label>
            <Textarea
              id="usageNotes"
              placeholder="Special usage instructions or notes (optional)"
              rows={3}
              {...register('usageNotes')}
              disabled={isLoading}
            />
          </div>

          {/* Add to Glossary Option */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="addToGlossary"
              checked={addToGlossary}
              onCheckedChange={(checked) => setValue('addToGlossary', !!checked)}
              disabled={isLoading}
            />
            <Label htmlFor="addToGlossary" className="text-sm">
              Add to project glossary automatically
            </Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !targetLanguage || !category}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Approve & Add Term
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
