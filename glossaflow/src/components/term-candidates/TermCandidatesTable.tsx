'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  MoreHorizontal,
  Check,
  X,
  Trash2,
  Tag,
  TrendingUp,
  Clock,
  AlertCircle,
} from 'lucide-react';
import { type TermCandidate } from '@/lib/api/term-candidates';

interface TermCandidatesTableProps {
  candidates: TermCandidate[];
  onApprove: (candidate: TermCandidate) => void;
  onReject: (candidateId: string, reason?: string) => void;
  onDelete: (candidate: TermCandidate) => void;
  onAssignCategory: (candidateId: string, category: string) => void;
  isLoading?: boolean;
}

const categories = [
  'technical',
  'general',
  'character',
  'location',
  'concept',
  'business',
  'medical',
  'legal',
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'pending':
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    case 'approved':
      return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>;
    case 'rejected':
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

const getConfidenceBadge = (score: number) => {
  if (score >= 0.8) {
    return <Badge variant="secondary" className="bg-green-100 text-green-800">High</Badge>;
  } else if (score >= 0.5) {
    return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium</Badge>;
  } else {
    return <Badge variant="secondary" className="bg-red-100 text-red-800">Low</Badge>;
  }
};

const getFrequencyIcon = (frequency: number) => {
  if (frequency >= 10) {
    return <TrendingUp className="h-4 w-4 text-green-600" />;
  } else if (frequency >= 3) {
    return <TrendingUp className="h-4 w-4 text-yellow-600" />;
  } else {
    return <TrendingUp className="h-4 w-4 text-red-600" />;
  }
};

export function TermCandidatesTable({
  candidates,
  onApprove,
  onReject,
  onDelete,
  onAssignCategory,
  isLoading = false,
}: TermCandidatesTableProps) {
  const [rejectingId, setRejectingId] = useState<string | null>(null);

  const handleReject = async (candidateId: string) => {
    setRejectingId(candidateId);
    try {
      await onReject(candidateId);
    } finally {
      setRejectingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (candidates.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Term Candidates ({candidates.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Term</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Confidence</TableHead>
                <TableHead>Context</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Project</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-[50px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {candidates.map((candidate) => (
                <TableRow key={candidate.id}>
                  <TableCell>
                    <div className="font-medium">{candidate.candidateTerm}</div>
                    {candidate.extractionMetadata?.pattern_type && (
                      <div className="text-xs text-gray-500 mt-1">
                        {candidate.extractionMetadata.pattern_type}
                      </div>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getFrequencyIcon(candidate.frequency)}
                      <span className="font-medium">{candidate.frequency}</span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getConfidenceBadge(candidate.confidenceScore)}
                      <span className="text-sm text-gray-600">
                        {(candidate.confidenceScore * 100).toFixed(0)}%
                      </span>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    {candidate.contextSentence ? (
                      <div className="max-w-xs">
                        <p className="text-sm text-gray-600" title={candidate.contextSentence}>
                          {truncateText(candidate.contextSentence, 80)}
                        </p>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">No context</span>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {candidate.assignedCategory ? (
                      <Badge variant="outline">{candidate.assignedCategory}</Badge>
                    ) : (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Tag className="h-3 w-3 mr-1" />
                            Assign
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          {categories.map((category) => (
                            <DropdownMenuItem
                              key={category}
                              onClick={() => onAssignCategory(candidate.id, category)}
                            >
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {getStatusBadge(candidate.status)}
                  </TableCell>
                  
                  <TableCell>
                    {candidate.project ? (
                      <div className="text-sm">
                        <div className="font-medium">{candidate.project.name}</div>
                        <div className="text-gray-500">{candidate.project.source_language}</div>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">Unknown</span>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Clock className="h-3 w-3" />
                      {formatDate(candidate.createdAt)}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {candidate.status === 'pending' && (
                          <>
                            <DropdownMenuItem
                              onClick={() => onApprove(candidate)}
                              className="text-green-600"
                            >
                              <Check className="mr-2 h-4 w-4" />
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleReject(candidate.id)}
                              disabled={rejectingId === candidate.id}
                              className="text-red-600"
                            >
                              <X className="mr-2 h-4 w-4" />
                              Reject
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuItem
                          onClick={() => onDelete(candidate)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
