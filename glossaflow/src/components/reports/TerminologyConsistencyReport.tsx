'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  FileText,
  Download,
  RefreshCw,
  Target,
  Users,
  BookOpen
} from 'lucide-react';

interface ConsistencyReportProps {
  seriesId: string;
  className?: string;
}

interface ConsistencyData {
  overallScore: number;
  totalTerms: number;
  consistentTerms: number;
  inconsistentTerms: number;
  projectReports: ProjectReport[];
  topIssues: ConsistencyIssue[];
  recommendations: Recommendation[];
  trends: TrendData[];
}

interface ProjectReport {
  projectId: string;
  projectTitle: string;
  score: number;
  termCount: number;
  issueCount: number;
  lastUpdated: string;
}

interface ConsistencyIssue {
  type: 'missing_term' | 'different_translation' | 'inconsistent_usage';
  severity: 'high' | 'medium' | 'low';
  sourceTerm: string;
  expectedTerm: string;
  actualTerm?: string;
  affectedProjects: string[];
  frequency: number;
}

interface Recommendation {
  type: 'standardize_term' | 'add_missing_term' | 'review_translation';
  priority: 'high' | 'medium' | 'low';
  description: string;
  impact: number;
  effort: 'low' | 'medium' | 'high';
}

interface TrendData {
  date: string;
  score: number;
  termCount: number;
  issueCount: number;
}

export function TerminologyConsistencyReport({ seriesId, className = '' }: ConsistencyReportProps) {
  const [data, setData] = useState<ConsistencyData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchConsistencyData();
  }, [seriesId]);

  const fetchConsistencyData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Mock data for demonstration
      const mockData: ConsistencyData = {
        overallScore: 0.87,
        totalTerms: 245,
        consistentTerms: 213,
        inconsistentTerms: 32,
        projectReports: [
          {
            projectId: '1',
            projectTitle: 'Volume 1: The Beginning',
            score: 0.92,
            termCount: 89,
            issueCount: 3,
            lastUpdated: '2024-01-15',
          },
          {
            projectId: '2',
            projectTitle: 'Volume 2: The Journey',
            score: 0.85,
            termCount: 156,
            issueCount: 12,
            lastUpdated: '2024-01-14',
          },
        ],
        topIssues: [
          {
            type: 'different_translation',
            severity: 'high',
            sourceTerm: 'magic sword',
            expectedTerm: '魔法の剣',
            actualTerm: '魔剣',
            affectedProjects: ['Volume 2', 'Volume 3'],
            frequency: 15,
          },
          {
            type: 'missing_term',
            severity: 'medium',
            sourceTerm: 'ancient temple',
            expectedTerm: '古代神殿',
            affectedProjects: ['Volume 3'],
            frequency: 8,
          },
        ],
        recommendations: [
          {
            type: 'standardize_term',
            priority: 'high',
            description: 'Standardize "magic sword" translation across all volumes',
            impact: 15,
            effort: 'low',
          },
          {
            type: 'add_missing_term',
            priority: 'medium',
            description: 'Add missing character name translations to Volume 3',
            impact: 8,
            effort: 'medium',
          },
        ],
        trends: [
          { date: '2024-01-01', score: 0.82, termCount: 180, issueCount: 45 },
          { date: '2024-01-08', score: 0.85, termCount: 220, issueCount: 38 },
          { date: '2024-01-15', score: 0.87, termCount: 245, issueCount: 32 },
        ],
      };

      setData(mockData);
    } catch (err) {
      setError('Failed to fetch consistency data');
      console.error('Error fetching consistency data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Terminology Consistency Report</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Terminology Consistency Report</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Failed to load consistency report'}
            </AlertDescription>
          </Alert>
          <Button onClick={fetchConsistencyData} className="mt-4">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Terminology Consistency Report</h2>
          <p className="text-gray-600">Series-wide terminology analysis and recommendations</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={fetchConsistencyData}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Overall Score</p>
                <p className="text-2xl font-bold">{Math.round(data.overallScore * 100)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">Total Terms</p>
                <p className="text-2xl font-bold">{data.totalTerms}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">Consistent</p>
                <p className="text-2xl font-bold">{data.consistentTerms}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium">Issues</p>
                <p className="text-2xl font-bold">{data.inconsistentTerms}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Report Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="issues">Issues</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Consistency Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Overall Consistency</span>
                  <span>{Math.round(data.overallScore * 100)}%</span>
                </div>
                <Progress value={data.overallScore * 100} className="h-2" />
                <p className="text-xs text-gray-600">
                  {data.consistentTerms} of {data.totalTerms} terms are consistent across all projects
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-4">
          <div className="grid gap-4">
            {data.projectReports.map((project) => (
              <Card key={project.projectId}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{project.projectTitle}</h4>
                      <p className="text-sm text-gray-600">
                        {project.termCount} terms • {project.issueCount} issues
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">{Math.round(project.score * 100)}%</div>
                      <Progress value={project.score * 100} className="h-1 w-20" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="issues" className="space-y-4">
          <div className="space-y-4">
            {data.topIssues.map((issue, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant={getSeverityColor(issue.severity)}>
                          {issue.severity}
                        </Badge>
                        <Badge variant="outline">{issue.type.replace('_', ' ')}</Badge>
                      </div>
                      <h4 className="font-medium">"{issue.sourceTerm}"</h4>
                      <p className="text-sm text-gray-600">
                        Expected: {issue.expectedTerm}
                        {issue.actualTerm && ` • Found: ${issue.actualTerm}`}
                      </p>
                      <p className="text-xs text-gray-500">
                        Affects: {issue.affectedProjects.join(', ')} • {issue.frequency} instances
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Fix Issue
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="space-y-4">
            {data.recommendations.map((rec, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Badge className={getPriorityColor(rec.priority)}>
                          {rec.priority} priority
                        </Badge>
                        <Badge variant="outline">{rec.effort} effort</Badge>
                      </div>
                      <h4 className="font-medium">{rec.description}</h4>
                      <p className="text-sm text-gray-600">
                        Impact: {rec.impact} instances
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Apply Fix
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
