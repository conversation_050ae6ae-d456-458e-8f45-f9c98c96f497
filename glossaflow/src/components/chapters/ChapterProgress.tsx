'use client';

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  BookOpen, 
  CheckCircle, 
  Clock, 
  Users, 
  Target,
  TrendingUp,
  CreditCard
} from 'lucide-react';
import { useGetChapterProgressQuery } from '@/lib/api/chapters';
import type { Chapter } from '@/types';

interface ChapterProgressProps {
  chapter: Chapter;
  showDetailed?: boolean;
  className?: string;
}

export function ChapterProgress({ 
  chapter, 
  showDetailed = false,
  className = '' 
}: ChapterProgressProps) {
  const {
    data: progressResponse,
    isLoading,
    isError,
  } = useGetChapterProgressQuery(chapter.id);

  const progress = progressResponse?.data;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5" />
            <span>Chapter Progress</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-2 w-full" />
          </div>
          {showDetailed && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <Skeleton className="h-16" />
                <Skeleton className="h-16" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Skeleton className="h-16" />
                <Skeleton className="h-16" />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    );
  }

  if (isError || !progress) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Clock className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-600">Unable to load progress</p>
        </CardContent>
      </Card>
    );
  }

  const progressPercentage = progress.progressPercentage || chapter.progress_percentage;
  const completionRate = progress.totalSegments > 0 
    ? (progress.completedSegments / progress.totalSegments) * 100 
    : 0;

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    if (percentage >= 25) return 'bg-orange-500';
    return 'bg-gray-500';
  };

  const getStatusBadge = () => {
    const status = chapter.status;
    const config = {
      pending: { color: 'gray', label: 'Pending' },
      in_progress: { color: 'blue', label: 'In Progress' },
      translated: { color: 'green', label: 'Translated' },
      reviewed: { color: 'purple', label: 'Reviewed' },
      approved: { color: 'emerald', label: 'Approved' },
    } as const;

    const { color, label } = config[status] || config.pending;
    
    return (
      <Badge variant="outline" className={`text-${color}-600 border-${color}-200`}>
        {label}
      </Badge>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5" />
            <span>Chapter Progress</span>
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Completion</span>
            <span className="text-sm text-gray-600">
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <Progress 
            value={progressPercentage} 
            className="h-2"
          />
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{progress.completedSegments} of {progress.totalSegments} segments</span>
            <span>{chapter.source_word_count} words</span>
          </div>
        </div>

        {showDetailed && (
          <>
            {/* Detailed Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Translated</span>
                </div>
                <div className="mt-1">
                  <span className="text-lg font-bold text-blue-900">
                    {progress.completedSegments}
                  </span>
                  <span className="text-sm text-blue-600 ml-1">segments</span>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-900">Reviewed</span>
                </div>
                <div className="mt-1">
                  <span className="text-lg font-bold text-purple-900">
                    {progress.reviewedSegments}
                  </span>
                  <span className="text-sm text-purple-600 ml-1">segments</span>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-900">Approved</span>
                </div>
                <div className="mt-1">
                  <span className="text-lg font-bold text-green-900">
                    {progress.approvedSegments}
                  </span>
                  <span className="text-sm text-green-600 ml-1">segments</span>
                </div>
              </div>

              <div className="bg-orange-50 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-900">Cost</span>
                </div>
                <div className="mt-1">
                  <span className="text-lg font-bold text-orange-900">
                    {chapter.actual_cost.toFixed(1)}
                  </span>
                  <span className="text-sm text-orange-600 ml-1">credits</span>
                </div>
                {chapter.estimated_cost > 0 && (
                  <div className="text-xs text-orange-600">
                    of {chapter.estimated_cost.toFixed(1)} estimated
                  </div>
                )}
              </div>
            </div>

            {/* Progress Timeline */}
            {progress.lastActivity && (
              <div className="pt-4 border-t">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>Last activity: {new Date(progress.lastActivity).toLocaleDateString()}</span>
                </div>
                {progress.estimatedTimeRemaining && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                    <TrendingUp className="h-4 w-4" />
                    <span>Estimated completion: {progress.estimatedTimeRemaining}</span>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
