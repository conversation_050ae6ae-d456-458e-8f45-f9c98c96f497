'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  BookOpen, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Play,
  Edit,
  MoreHorizontal,
  CreditCard
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useGetChaptersQuery } from '@/lib/api/chapters';
import { StatusBadge } from '@/components/common/StatusBadge';
import type { Chapter, ChapterStatus } from '@/types';

interface ChapterListProps {
  projectId: string;
  selectedChapterId?: string;
  onChapterSelect: (chapter: Chapter) => void;
  onChapterEdit?: (chapter: Chapter) => void;
  onChapterDelete?: (chapter: Chapter) => void;
  showActions?: boolean;
}

const statusConfig = {
  pending: { color: 'gray', icon: Clock },
  in_progress: { color: 'blue', icon: Play },
  translated: { color: 'green', icon: CheckCircle },
  reviewed: { color: 'purple', icon: CheckCircle },
  approved: { color: 'emerald', icon: CheckCircle },
} as const;

export function ChapterList({
  projectId,
  selectedChapterId,
  onChapterSelect,
  onChapterEdit,
  onChapterDelete,
  showActions = true,
}: ChapterListProps) {
  const [sortBy, setSortBy] = useState<'chapter_number' | 'progress_percentage'>('chapter_number');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const {
    data: chaptersResponse,
    isLoading,
    isError,
    refetch,
  } = useGetChaptersQuery({
    projectId,
    sortBy,
    sortOrder,
    limit: 100, // Get all chapters for the project
  });

  const chapters = chaptersResponse?.data?.items || [];

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-6 w-16" />
                </div>
                <Skeleton className="h-4 w-full" />
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-2 w-32" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Failed to load chapters
          </h3>
          <p className="text-gray-600 mb-4">
            There was an error loading the chapters for this project.
          </p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (chapters.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No chapters found
          </h3>
          <p className="text-gray-600">
            This project doesn't have any chapters yet. Create your first chapter to get started.
          </p>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status: ChapterStatus) => {
    const config = statusConfig[status];
    const Icon = config.icon;
    return <Icon className="h-4 w-4" />;
  };

  const formatCost = (cost: number) => {
    return cost > 0 ? `${cost.toFixed(1)} credits` : 'Free';
  };

  return (
    <div className="space-y-4">
      {/* Sort Controls */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Chapters ({chapters.length})</h3>
        <div className="flex items-center space-x-2">
          <Button
            variant={sortBy === 'chapter_number' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSortBy('chapter_number')}
          >
            By Number
          </Button>
          <Button
            variant={sortBy === 'progress_percentage' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSortBy('progress_percentage')}
          >
            By Progress
          </Button>
        </div>
      </div>

      {/* Chapter Cards */}
      <div className="space-y-3">
        {chapters.map((chapter) => {
          const isSelected = selectedChapterId === chapter.id;
          const statusInfo = statusConfig[chapter.status];
          
          return (
            <Card 
              key={chapter.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => onChapterSelect(chapter)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Chapter Header */}
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-500">
                          Chapter {chapter.chapter_number}
                        </span>
                        <StatusBadge 
                          status={chapter.status}
                          variant={statusInfo.color}
                          icon={getStatusIcon(chapter.status)}
                        />
                      </div>
                      {chapter.estimated_cost > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <CreditCard className="mr-1 h-3 w-3" />
                          {formatCost(chapter.estimated_cost)}
                        </Badge>
                      )}
                    </div>

                    {/* Chapter Title */}
                    <h4 className="font-semibold text-gray-900 mb-1 truncate">
                      {chapter.title}
                    </h4>

                    {/* Chapter Description */}
                    {chapter.description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {chapter.description}
                      </p>
                    )}

                    {/* Progress and Stats */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{chapter.total_segments} segments</span>
                        <span>{chapter.source_word_count} words</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">
                          {Math.round(chapter.progress_percentage)}%
                        </span>
                        <Progress 
                          value={chapter.progress_percentage} 
                          className="w-20" 
                        />
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="ml-2"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          onClick={(e) => {
                            e.stopPropagation();
                            onChapterSelect(chapter);
                          }}
                        >
                          <Play className="mr-2 h-4 w-4" />
                          Open Chapter
                        </DropdownMenuItem>
                        {onChapterEdit && (
                          <DropdownMenuItem 
                            onClick={(e) => {
                              e.stopPropagation();
                              onChapterEdit(chapter);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Chapter
                          </DropdownMenuItem>
                        )}
                        {onChapterDelete && chapter.total_segments === 0 && (
                          <DropdownMenuItem 
                            onClick={(e) => {
                              e.stopPropagation();
                              onChapterDelete(chapter);
                            }}
                            className="text-red-600"
                          >
                            <AlertCircle className="mr-2 h-4 w-4" />
                            Delete Chapter
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
