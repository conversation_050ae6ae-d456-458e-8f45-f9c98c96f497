'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ChevronLeft, 
  ChevronRight, 
  BookOpen,
  CreditCard,
  Clock,
  CheckCircle
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useGetChaptersQuery } from '@/lib/api/chapters';
import type { Chapter } from '@/types';

interface ChapterNavigationProps {
  projectId: string;
  currentChapter: Chapter;
  onChapterChange: (chapter: Chapter) => void;
  showProgress?: boolean;
  compact?: boolean;
}

export function ChapterNavigation({
  projectId,
  currentChapter,
  onChapterChange,
  showProgress = true,
  compact = false,
}: ChapterNavigationProps) {
  const {
    data: chaptersResponse,
    isLoading,
  } = useGetChaptersQuery({
    projectId,
    sortBy: 'chapter_number',
    sortOrder: 'asc',
    limit: 100,
  });

  const chapters = chaptersResponse?.data?.items || [];
  const currentIndex = chapters.findIndex(c => c.id === currentChapter.id);
  const previousChapter = currentIndex > 0 ? chapters[currentIndex - 1] : null;
  const nextChapter = currentIndex < chapters.length - 1 ? chapters[currentIndex + 1] : null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-3 w-3" />;
      case 'in_progress':
        return <BookOpen className="h-3 w-3" />;
      case 'translated':
      case 'reviewed':
      case 'approved':
        return <CheckCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'gray';
      case 'in_progress':
        return 'blue';
      case 'translated':
        return 'green';
      case 'reviewed':
        return 'purple';
      case 'approved':
        return 'emerald';
      default:
        return 'gray';
    }
  };

  if (isLoading || chapters.length === 0) {
    return null;
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => previousChapter && onChapterChange(previousChapter)}
          disabled={!previousChapter}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <Select
          value={currentChapter.id}
          onValueChange={(chapterId) => {
            const chapter = chapters.find(c => c.id === chapterId);
            if (chapter) onChapterChange(chapter);
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue>
              Chapter {currentChapter.chapter_number}: {currentChapter.title}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {chapters.map((chapter) => (
              <SelectItem key={chapter.id} value={chapter.id}>
                <div className="flex items-center space-x-2">
                  <span>Chapter {chapter.chapter_number}</span>
                  <Badge variant="outline" className={`text-${getStatusColor(chapter.status)}-600`}>
                    {getStatusIcon(chapter.status)}
                  </Badge>
                  <span className="truncate">{chapter.title}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          size="sm"
          onClick={() => nextChapter && onChapterChange(nextChapter)}
          disabled={!nextChapter}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-white border rounded-lg p-4 space-y-4">
      {/* Current Chapter Info */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-gray-500" />
            <span className="font-medium">
              Chapter {currentChapter.chapter_number}
            </span>
          </div>
          <Badge variant="outline" className={`text-${getStatusColor(currentChapter.status)}-600`}>
            {getStatusIcon(currentChapter.status)}
            <span className="ml-1 capitalize">{currentChapter.status.replace('_', ' ')}</span>
          </Badge>
          {currentChapter.estimated_cost > 0 && (
            <Badge variant="outline">
              <CreditCard className="mr-1 h-3 w-3" />
              {currentChapter.estimated_cost.toFixed(1)} credits
            </Badge>
          )}
        </div>
        
        {showProgress && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {Math.round(currentChapter.progress_percentage)}%
            </span>
            <Progress value={currentChapter.progress_percentage} className="w-24" />
          </div>
        )}
      </div>

      <h3 className="text-lg font-semibold text-gray-900 truncate">
        {currentChapter.title}
      </h3>

      {currentChapter.description && (
        <p className="text-sm text-gray-600 line-clamp-2">
          {currentChapter.description}
        </p>
      )}

      {/* Navigation Controls */}
      <div className="flex items-center justify-between pt-2 border-t">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => previousChapter && onChapterChange(previousChapter)}
            disabled={!previousChapter}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          
          {previousChapter && (
            <div className="text-xs text-gray-500">
              Chapter {previousChapter.chapter_number}: {previousChapter.title}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {nextChapter && (
            <div className="text-xs text-gray-500 text-right">
              Chapter {nextChapter.chapter_number}: {nextChapter.title}
            </div>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => nextChapter && onChapterChange(nextChapter)}
            disabled={!nextChapter}
          >
            Next
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Chapter Selector */}
      <div className="pt-2 border-t">
        <label className="text-sm font-medium text-gray-700 mb-2 block">
          Jump to Chapter
        </label>
        <Select
          value={currentChapter.id}
          onValueChange={(chapterId) => {
            const chapter = chapters.find(c => c.id === chapterId);
            if (chapter) onChapterChange(chapter);
          }}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {chapters.map((chapter) => (
              <SelectItem key={chapter.id} value={chapter.id}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-2">
                    <span>Chapter {chapter.chapter_number}</span>
                    <Badge variant="outline" className={`text-${getStatusColor(chapter.status)}-600`}>
                      {getStatusIcon(chapter.status)}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span className="text-xs text-gray-500">
                      {Math.round(chapter.progress_percentage)}%
                    </span>
                    <Progress value={chapter.progress_percentage} className="w-16" />
                  </div>
                </div>
                <div className="text-sm text-gray-600 truncate mt-1">
                  {chapter.title}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
