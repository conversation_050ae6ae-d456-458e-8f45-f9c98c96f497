'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Lightbulb,
  Zap,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  FileText,
} from 'lucide-react';
import Link from 'next/link';
import {
  useGetExtractionStatusQuery,
  useExtractTermCandidatesMutation,
  useGetTermCandidateStatsQuery,
} from '@/lib/api/term-candidates';

interface TermExtractionWidgetProps {
  projectId: string;
  projectName: string;
  sourceLanguage?: string;
  onExtractComplete?: () => void;
}

export function TermExtractionWidget({
  projectId,
  projectName,
  sourceLanguage = 'en',
  onExtractComplete,
}: TermExtractionWidgetProps) {
  const [extractionText, setExtractionText] = useState('');
  const [showTextInput, setShowTextInput] = useState(false);

  // API queries
  const {
    data: statusResponse,
    isLoading: isLoadingStatus,
    refetch: refetchStatus,
  } = useGetExtractionStatusQuery(projectId);

  const {
    data: statsResponse,
    isLoading: isLoadingStats,
  } = useGetTermCandidateStatsQuery({ projectId });

  const [extractTermCandidates, { isLoading: isExtracting }] = useExtractTermCandidatesMutation();

  const status = statusResponse?.data;
  const stats = statsResponse?.data?.overview;

  const handleQuickExtraction = async () => {
    if (!extractionText.trim()) {
      setShowTextInput(true);
      return;
    }

    try {
      await extractTermCandidates({
        projectId,
        sourceText: extractionText,
        sourceLanguage,
        clearExisting: false,
      }).unwrap();

      setExtractionText('');
      setShowTextInput(false);
      refetchStatus();
      onExtractComplete?.();
    } catch (error) {
      console.error('Failed to extract terms:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-yellow-600" />
          AI-Powered Term Extraction
          <Badge variant="secondary" className="ml-2 text-xs">
            <Zap className="h-3 w-3 mr-1" />
            AI Enhanced
          </Badge>
        </CardTitle>
        <CardDescription>
          Automatically extract terminology candidates using advanced AI and linguistic analysis
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Overview */}
        {!isLoadingStatus && status && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {status.statistics.totalCandidates}
              </div>
              <div className="text-xs text-blue-800">Total</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {status.statistics.pendingCandidates}
              </div>
              <div className="text-xs text-yellow-800">Pending</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {status.statistics.approvedCandidates}
              </div>
              <div className="text-xs text-green-800">Approved</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {status.statistics.rejectedCandidates}
              </div>
              <div className="text-xs text-red-800">Rejected</div>
            </div>
          </div>
        )}

        {/* Last Extraction Info */}
        {status?.lastExtraction && (
          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
            <Clock className="h-4 w-4" />
            <span>Last extraction: {formatDate(status.lastExtraction.extractedAt)}</span>
          </div>
        )}

        {/* Quick Text Extraction */}
        {showTextInput && (
          <div className="space-y-3">
            <textarea
              value={extractionText}
              onChange={(e) => setExtractionText(e.target.value)}
              placeholder="Paste text to extract terms from..."
              className="w-full p-3 border rounded-lg resize-none"
              rows={4}
              disabled={isExtracting}
            />
            <div className="flex gap-2">
              <Button
                onClick={handleQuickExtraction}
                disabled={isExtracting || !extractionText.trim()}
                size="sm"
              >
                {isExtracting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Extracting...
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    Extract Terms
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowTextInput(false)}
                size="sm"
                disabled={isExtracting}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2">
          {!showTextInput && (
            <Button
              onClick={() => setShowTextInput(true)}
              variant="outline"
              size="sm"
              disabled={isExtracting}
            >
              <FileText className="mr-2 h-4 w-4" />
              Quick Extract
            </Button>
          )}
          
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/term-suggestions">
              <Lightbulb className="mr-2 h-4 w-4" />
              View All Suggestions
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {/* Pending Candidates Alert */}
        {stats && stats.pending > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You have {stats.pending} pending term candidates waiting for review.{' '}
              <Link 
                href="/dashboard/term-suggestions?status=pending" 
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Review them now
              </Link>
            </AlertDescription>
          </Alert>
        )}

        {/* Success State */}
        {stats && stats.approved > 0 && (
          <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-3 rounded-lg">
            <CheckCircle className="h-4 w-4" />
            <span>{stats.approved} terms have been approved and added to your terminology database</span>
          </div>
        )}

        {/* Empty State */}
        {!isLoadingStatus && (!status || status.statistics.totalCandidates === 0) && (
          <div className="text-center py-6">
            <Lightbulb className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600 mb-3">
              No term candidates extracted yet for this project
            </p>
            <Button
              onClick={() => setShowTextInput(true)}
              size="sm"
              disabled={isExtracting}
            >
              <Zap className="mr-2 h-4 w-4" />
              Start Extracting
            </Button>
          </div>
        )}

        {/* Loading State */}
        {(isLoadingStatus || isLoadingStats) && (
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
