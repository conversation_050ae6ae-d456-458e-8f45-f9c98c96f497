'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileUpload } from '@/components/common/FileUpload';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  X,
  Loader2 
} from 'lucide-react';

interface FileUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUploadComplete: (files: UploadedFile[]) => void;
  projectId?: string;
  acceptedFormats?: string;
  maxFileSize?: number;
  maxFiles?: number;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  url?: string;
}

const defaultAcceptedFormats = '.json,.xml,.xliff,.tmx,.csv,.txt,.docx,.html';

export function FileUploadDialog({
  open,
  onOpenChange,
  onUploadComplete,
  projectId,
  acceptedFormats = defaultAcceptedFormats,
  maxFileSize = 50, // 50MB
  maxFiles = 10,
}: FileUploadDialogProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleFilesSelected = (files: File[]) => {
    const newFiles: UploadedFile[] = files.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0,
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
    uploadFiles(files, newFiles);
  };

  const uploadFiles = async (files: File[], fileRecords: UploadedFile[]) => {
    setIsUploading(true);
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileRecord = fileRecords[i];
        
        // Simulate file upload with progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          
          setUploadedFiles(prev =>
            prev.map(f =>
              f.id === fileRecord.id
                ? { ...f, progress }
                : f
            )
          );
          
          setUploadProgress(((i * 100) + progress) / files.length);
        }

        // Simulate upload completion
        const success = Math.random() > 0.1; // 90% success rate
        
        setUploadedFiles(prev =>
          prev.map(f =>
            f.id === fileRecord.id
              ? {
                  ...f,
                  status: success ? 'success' : 'error',
                  progress: 100,
                  error: success ? undefined : 'Upload failed. Please try again.',
                  url: success ? `/api/files/${fileRecord.id}` : undefined,
                }
              : f
          )
        );
      }
    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const retryUpload = (fileId: string) => {
    // In a real implementation, this would retry the upload
    setUploadedFiles(prev =>
      prev.map(f =>
        f.id === fileId
          ? { ...f, status: 'uploading', progress: 0, error: undefined }
          : f
      )
    );
  };

  const handleComplete = () => {
    const successfulFiles = uploadedFiles.filter(f => f.status === 'success');
    onUploadComplete(successfulFiles);
    handleClose();
  };

  const handleClose = () => {
    setUploadedFiles([]);
    setUploadProgress(0);
    setIsUploading(false);
    onOpenChange(false);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const successfulFiles = uploadedFiles.filter(f => f.status === 'success');
  const failedFiles = uploadedFiles.filter(f => f.status === 'error');
  const uploadingFiles = uploadedFiles.filter(f => f.status === 'uploading');

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Upload className="mr-2 h-5 w-5" />
            Upload Translation Files
          </DialogTitle>
          <DialogDescription>
            Upload your source files for translation. Supported formats: JSON, XML, XLIFF, TMX, CSV, TXT, DOCX, HTML
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* File Upload Area */}
          {uploadedFiles.length === 0 && (
            <FileUpload
              accept={acceptedFormats}
              multiple={true}
              maxSize={maxFileSize}
              maxFiles={maxFiles}
              onFilesSelected={handleFilesSelected}
              disabled={isUploading}
              showPreview={false}
            />
          )}

          {/* Upload Progress */}
          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading files...</span>
                <span>{Math.round(uploadProgress)}%</span>
              </div>
              <Progress value={uploadProgress} />
            </div>
          )}

          {/* Uploaded Files List */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Files</h3>
                <div className="flex space-x-2">
                  {successfulFiles.length > 0 && (
                    <Badge variant="secondary" className="text-green-600">
                      {successfulFiles.length} successful
                    </Badge>
                  )}
                  {failedFiles.length > 0 && (
                    <Badge variant="secondary" className="text-red-600">
                      {failedFiles.length} failed
                    </Badge>
                  )}
                </div>
              </div>

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {uploadedFiles.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <FileText className="h-8 w-8 text-gray-400" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                        
                        {file.status === 'uploading' && (
                          <Progress value={file.progress} className="mt-1" />
                        )}
                        
                        {file.error && (
                          <p className="text-xs text-red-600 mt-1">{file.error}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {file.status === 'success' && (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      )}
                      {file.status === 'error' && (
                        <>
                          <AlertCircle className="h-5 w-5 text-red-600" />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => retryUpload(file.id)}
                          >
                            Retry
                          </Button>
                        </>
                      )}
                      {file.status === 'uploading' && (
                        <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        disabled={file.status === 'uploading'}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Add More Files */}
              {!isUploading && uploadedFiles.length < maxFiles && (
                <Button
                  variant="outline"
                  onClick={() => {
                    // Reset to show upload area again
                    // In a real implementation, you might want to keep existing files
                  }}
                  className="w-full"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Add More Files
                </Button>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleClose} disabled={isUploading}>
              Cancel
            </Button>
            {successfulFiles.length > 0 && (
              <Button onClick={handleComplete} disabled={isUploading}>
                Continue with {successfulFiles.length} file{successfulFiles.length !== 1 ? 's' : ''}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
