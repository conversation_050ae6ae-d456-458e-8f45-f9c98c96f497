'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  Download, 
  Calendar, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';
import { subscriptionPlans, formatPrice } from '@/lib/stripe/config';
import { StatusBadge } from '@/components/common/StatusBadge';

// Mock data - in real app, this would come from API
const mockBillingData = {
  currentPlan: {
    id: 'professional',
    name: 'Professional',
    price: 29,
    interval: 'month',
    status: 'active',
    nextBillingDate: '2024-02-15',
    cancelAtPeriodEnd: false,
  },
  usage: {
    words: {
      used: 45000,
      limit: 100000,
      overage: 0,
    },
    projects: {
      used: 8,
      limit: -1, // unlimited
    },
    teamMembers: {
      used: 3,
      limit: 5,
    },
    storage: {
      used: 2.5, // GB
      limit: 10, // GB
    },
  },
  invoices: [
    {
      id: 'inv_001',
      date: '2024-01-15',
      amount: 29,
      status: 'paid',
      description: 'Professional Plan - January 2024',
      downloadUrl: '/api/invoices/inv_001/download',
    },
    {
      id: 'inv_002',
      date: '2023-12-15',
      amount: 29,
      status: 'paid',
      description: 'Professional Plan - December 2023',
      downloadUrl: '/api/invoices/inv_002/download',
    },
    {
      id: 'inv_003',
      date: '2023-11-15',
      amount: 29,
      status: 'paid',
      description: 'Professional Plan - November 2023',
      downloadUrl: '/api/invoices/inv_003/download',
    },
  ],
  paymentMethods: [
    {
      id: 'pm_001',
      type: 'card',
      brand: 'visa',
      last4: '4242',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true,
    },
  ],
};

export function BillingDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const { currentPlan, usage, invoices, paymentMethods } = mockBillingData;

  const handleCancelSubscription = async () => {
    setIsLoading(true);
    // TODO: Implement subscription cancellation
    setTimeout(() => setIsLoading(false), 1000);
  };

  const handleUpdatePaymentMethod = async () => {
    setIsLoading(true);
    // TODO: Implement payment method update
    setTimeout(() => setIsLoading(false), 1000);
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Current Plan</CardTitle>
              <CardDescription>Manage your subscription and billing</CardDescription>
            </div>
            <StatusBadge 
              status={currentPlan.status as any} 
              text={currentPlan.status}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">Plan</p>
              <p className="text-lg font-semibold">{currentPlan.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Price</p>
              <p className="text-lg font-semibold">
                {formatPrice(currentPlan.price)}/{currentPlan.interval}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Next billing date</p>
              <p className="text-lg font-semibold">{currentPlan.nextBillingDate}</p>
            </div>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline">
              Change Plan
            </Button>
            <Button 
              variant="outline" 
              onClick={handleCancelSubscription}
              disabled={isLoading}
            >
              {currentPlan.cancelAtPeriodEnd ? 'Reactivate' : 'Cancel Subscription'}
            </Button>
          </div>

          {currentPlan.cancelAtPeriodEnd && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                Your subscription will be cancelled at the end of the current billing period ({currentPlan.nextBillingDate}).
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Overview</CardTitle>
          <CardDescription>Track your current usage against plan limits</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Words Usage */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Words Translated</span>
              <span className={`text-sm ${getUsageColor(getUsagePercentage(usage.words.used, usage.words.limit))}`}>
                {usage.words.used.toLocaleString()} / {usage.words.limit.toLocaleString()}
              </span>
            </div>
            <Progress value={getUsagePercentage(usage.words.used, usage.words.limit)} />
          </div>

          {/* Team Members Usage */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Team Members</span>
              <span className={`text-sm ${getUsageColor(getUsagePercentage(usage.teamMembers.used, usage.teamMembers.limit))}`}>
                {usage.teamMembers.used} / {usage.teamMembers.limit === -1 ? 'Unlimited' : usage.teamMembers.limit}
              </span>
            </div>
            {usage.teamMembers.limit !== -1 && (
              <Progress value={getUsagePercentage(usage.teamMembers.used, usage.teamMembers.limit)} />
            )}
          </div>

          {/* Storage Usage */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Storage</span>
              <span className={`text-sm ${getUsageColor(getUsagePercentage(usage.storage.used, usage.storage.limit))}`}>
                {usage.storage.used}GB / {usage.storage.limit}GB
              </span>
            </div>
            <Progress value={getUsagePercentage(usage.storage.used, usage.storage.limit)} />
          </div>

          {/* Projects */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Active Projects</span>
              <span className="text-sm text-gray-600">
                {usage.projects.used} {usage.projects.limit === -1 ? '(Unlimited)' : `/ ${usage.projects.limit}`}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="invoices" className="space-y-4">
        <TabsList>
          <TabsTrigger value="invoices">Billing History</TabsTrigger>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
        </TabsList>

        {/* Billing History */}
        <TabsContent value="invoices">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>View and download your past invoices</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {invoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <DollarSign className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium">{invoice.description}</p>
                        <p className="text-sm text-gray-600">{invoice.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-medium">{formatPrice(invoice.amount)}</p>
                        <StatusBadge 
                          status={invoice.status as any}
                          size="sm"
                        />
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Methods */}
        <TabsContent value="payment-methods">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Payment Methods</CardTitle>
                  <CardDescription>Manage your payment methods</CardDescription>
                </div>
                <Button onClick={handleUpdatePaymentMethod} disabled={isLoading}>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Add Payment Method
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentMethods.map((method) => (
                  <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <CreditCard className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium">
                          {method.brand.toUpperCase()} •••• {method.last4}
                        </p>
                        <p className="text-sm text-gray-600">
                          Expires {method.expiryMonth}/{method.expiryYear}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {method.isDefault && (
                        <Badge variant="outline">Default</Badge>
                      )}
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
