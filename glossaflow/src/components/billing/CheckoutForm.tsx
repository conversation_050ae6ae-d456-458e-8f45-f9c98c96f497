'use client';

import { useState } from 'react';
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import { subscriptionPlans, formatPrice, calculateAnnualPrice } from '@/lib/stripe/config';
import { AlertCircle, Shield, CreditCard } from 'lucide-react';

interface CheckoutFormProps {
  planId: string;
  interval: 'month' | 'year';
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function CheckoutForm({
  planId,
  interval,
  onSuccess,
  onError,
}: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const plan = subscriptionPlans[planId as keyof typeof subscriptionPlans];
  
  if (!plan) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>Invalid plan selected.</AlertDescription>
      </Alert>
    );
  }

  const price = interval === 'year' ? calculateAnnualPrice(plan.price) : plan.price;
  const displayPrice = interval === 'year' ? price / 12 : price;

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { error: submitError } = await elements.submit();
      if (submitError) {
        setError(submitError.message || 'An error occurred');
        setIsLoading(false);
        return;
      }

      // Create payment intent on the server
      const response = await fetch('/api/stripe/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          interval,
        }),
      });

      const { clientSecret, error: serverError } = await response.json();

      if (serverError) {
        setError(serverError);
        setIsLoading(false);
        return;
      }

      const { error: confirmError } = await stripe.confirmPayment({
        elements,
        clientSecret,
        confirmParams: {
          return_url: `${window.location.origin}/dashboard/billing/success`,
        },
      });

      if (confirmError) {
        setError(confirmError.message || 'Payment failed');
        onError?.(confirmError.message || 'Payment failed');
      } else {
        onSuccess?.();
      }
    } catch (err) {
      setError('An unexpected error occurred');
      onError?.('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto space-y-6">
      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Order Summary</span>
            <Badge variant="outline">{plan.name}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Plan</span>
            <span className="font-medium">{plan.name}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Billing</span>
            <span className="font-medium">
              {interval === 'year' ? 'Annual' : 'Monthly'}
            </span>
          </div>

          {interval === 'year' && (
            <div className="flex justify-between items-center text-green-600">
              <span className="text-sm">Annual discount (20%)</span>
              <span className="font-medium">
                -{formatPrice(plan.price * 12 * 0.2)}
              </span>
            </div>
          )}

          <Separator />

          <div className="flex justify-between items-center">
            <span className="font-medium">
              {interval === 'year' ? 'Total (billed annually)' : 'Monthly total'}
            </span>
            <span className="text-xl font-bold">
              {formatPrice(price)}
              {interval === 'month' && '/month'}
            </span>
          </div>

          {interval === 'year' && (
            <p className="text-sm text-gray-600 text-center">
              Equivalent to {formatPrice(displayPrice)}/month
            </p>
          )}
        </CardContent>
      </Card>

      {/* Payment Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="mr-2 h-5 w-5" />
            Payment Information
          </CardTitle>
          <CardDescription>
            Your payment information is secure and encrypted
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <PaymentElement />

            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="h-4 w-4" />
              <span>Secured by Stripe. Your payment information is encrypted and secure.</span>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={!stripe || !elements || isLoading}
            >
              {isLoading ? (
                <LoadingSpinner size="sm" />
              ) : (
                `Subscribe to ${plan.name} - ${formatPrice(price)}${interval === 'month' ? '/month' : ''}`
              )}
            </Button>

            <p className="text-xs text-gray-500 text-center">
              By subscribing, you agree to our Terms of Service and Privacy Policy.
              You can cancel your subscription at any time.
            </p>
          </form>
        </CardContent>
      </Card>

      {/* Features Reminder */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">What's included:</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-1 text-sm text-gray-600">
            {plan.features.slice(0, 4).map((feature, index) => (
              <li key={index} className="flex items-center">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                {feature}
              </li>
            ))}
            {plan.features.length > 4 && (
              <li className="text-blue-600">
                + {plan.features.length - 4} more features
              </li>
            )}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
