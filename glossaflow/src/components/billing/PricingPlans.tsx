'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Check, Star } from 'lucide-react';
import { subscriptionPlans, formatPrice, calculateAnnualPrice } from '@/lib/stripe/config';
import { cn } from '@/lib/utils';

interface PricingPlansProps {
  currentPlan?: string;
  onSelectPlan?: (planId: string, interval: 'month' | 'year') => void;
  showCurrentPlan?: boolean;
  className?: string;
}

export function PricingPlans({
  currentPlan,
  onSelectPlan,
  showCurrentPlan = true,
  className = '',
}: PricingPlansProps) {
  const [isAnnual, setIsAnnual] = useState(false);

  const handleSelectPlan = (planId: string) => {
    onSelectPlan?.(planId, isAnnual ? 'year' : 'month');
  };

  const getPrice = (plan: typeof subscriptionPlans.basic) => {
    if (plan.price === 0) return 0;
    return isAnnual ? calculateAnnualPrice(plan.price) / 12 : plan.price;
  };

  const getAnnualSavings = (plan: typeof subscriptionPlans.basic) => {
    if (plan.price === 0) return 0;
    const monthlyTotal = plan.price * 12;
    const annualPrice = calculateAnnualPrice(plan.price);
    return monthlyTotal - annualPrice;
  };

  return (
    <div className={cn('space-y-8', className)}>
      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <span className={cn('text-sm', !isAnnual && 'font-medium')}>Monthly</span>
        <Switch
          checked={isAnnual}
          onCheckedChange={setIsAnnual}
          aria-label="Toggle annual billing"
        />
        <span className={cn('text-sm', isAnnual && 'font-medium')}>
          Annual
          <Badge variant="secondary" className="ml-2">
            Save 20%
          </Badge>
        </span>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {Object.entries(subscriptionPlans).map(([planId, plan]) => {
          const isCurrentPlan = currentPlan === planId;
          const isPopular = planId === 'professional';
          const price = getPrice(plan);
          const annualSavings = getAnnualSavings(plan);

          return (
            <Card
              key={planId}
              className={cn(
                'relative transition-all duration-200',
                isPopular && 'border-blue-500 shadow-lg scale-105',
                isCurrentPlan && 'ring-2 ring-blue-500'
              )}
            >
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white">
                    <Star className="mr-1 h-3 w-3" />
                    Most Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <CardDescription className="text-sm">
                  {plan.description}
                </CardDescription>
                
                <div className="mt-4">
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold">
                      {formatPrice(price)}
                    </span>
                    {plan.price > 0 && (
                      <span className="text-gray-500 ml-1">
                        /{isAnnual ? 'month' : 'month'}
                      </span>
                    )}
                  </div>
                  
                  {isAnnual && plan.price > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        Billed annually ({formatPrice(calculateAnnualPrice(plan.price))})
                      </p>
                      <p className="text-sm text-green-600 font-medium">
                        Save {formatPrice(annualSavings)} per year
                      </p>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Features */}
                <div className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Limits */}
                <div className="pt-4 border-t space-y-2">
                  <h4 className="text-sm font-medium text-gray-900">Plan Limits</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Projects:</span>
                      <span>{plan.limits.projects === -1 ? 'Unlimited' : plan.limits.projects}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Words/month:</span>
                      <span>
                        {plan.limits.wordsPerMonth === -1 
                          ? 'Unlimited' 
                          : plan.limits.wordsPerMonth.toLocaleString()
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Team members:</span>
                      <span>
                        {plan.limits.teamMembers === -1 
                          ? 'Unlimited' 
                          : plan.limits.teamMembers
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Storage:</span>
                      <span>{plan.limits.storage}</span>
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <div className="pt-4">
                  {isCurrentPlan && showCurrentPlan ? (
                    <Button variant="outline" className="w-full" disabled>
                      Current Plan
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      variant={isPopular ? 'default' : 'outline'}
                      onClick={() => handleSelectPlan(planId)}
                    >
                      {plan.price === 0 ? 'Get Started' : 'Upgrade to ' + plan.name}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Additional Information */}
      <div className="text-center space-y-4">
        <p className="text-sm text-gray-600">
          All plans include a 14-day free trial. No credit card required.
        </p>
        <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
          <span>✓ Cancel anytime</span>
          <span>✓ 24/7 support</span>
          <span>✓ 99.9% uptime SLA</span>
        </div>
      </div>
    </div>
  );
}
