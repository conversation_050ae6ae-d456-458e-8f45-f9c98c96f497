import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  XCircle,
  Pause,
  Play
} from 'lucide-react';

export type StatusType = 
  | 'success' 
  | 'pending' 
  | 'warning' 
  | 'error' 
  | 'info' 
  | 'active' 
  | 'inactive'
  | 'draft'
  | 'in_progress'
  | 'review'
  | 'completed'
  | 'on_hold'
  | 'approved'
  | 'rejected';

interface StatusBadgeProps {
  status: StatusType;
  text?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const statusConfig = {
  success: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    defaultText: 'Success',
  },
  pending: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: Clock,
    defaultText: 'Pending',
  },
  warning: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: AlertCircle,
    defaultText: 'Warning',
  },
  error: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
    defaultText: 'Error',
  },
  info: {
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: AlertCircle,
    defaultText: 'Info',
  },
  active: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: Play,
    defaultText: 'Active',
  },
  inactive: {
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: Pause,
    defaultText: 'Inactive',
  },
  draft: {
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: Clock,
    defaultText: 'Draft',
  },
  in_progress: {
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Play,
    defaultText: 'In Progress',
  },
  review: {
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: AlertCircle,
    defaultText: 'In Review',
  },
  completed: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    defaultText: 'Completed',
  },
  on_hold: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: Pause,
    defaultText: 'On Hold',
  },
  approved: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    defaultText: 'Approved',
  },
  rejected: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
    defaultText: 'Rejected',
  },
};

const sizeClasses = {
  sm: 'text-xs px-2 py-1',
  md: 'text-sm px-2.5 py-1.5',
  lg: 'text-base px-3 py-2',
};

const iconSizes = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-5 w-5',
};

export function StatusBadge({
  status,
  text,
  showIcon = true,
  size = 'md',
  className = '',
}: StatusBadgeProps) {
  const config = statusConfig[status];
  const Icon = config.icon;
  const displayText = text || config.defaultText;

  return (
    <Badge
      variant="outline"
      className={cn(
        config.color,
        sizeClasses[size],
        'inline-flex items-center gap-1.5 font-medium border',
        className
      )}
    >
      {showIcon && (
        <Icon className={iconSizes[size]} />
      )}
      {displayText}
    </Badge>
  );
}

// Convenience components for common statuses
export function ProjectStatusBadge({ 
  status, 
  ...props 
}: Omit<StatusBadgeProps, 'status'> & { 
  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'on_hold' 
}) {
  return <StatusBadge status={status} {...props} />;
}

export function UserStatusBadge({ 
  status, 
  ...props 
}: Omit<StatusBadgeProps, 'status'> & { 
  status: 'active' | 'inactive' | 'pending' 
}) {
  return <StatusBadge status={status} {...props} />;
}

export function ApprovalStatusBadge({ 
  status, 
  ...props 
}: Omit<StatusBadgeProps, 'status'> & { 
  status: 'pending' | 'approved' | 'rejected' 
}) {
  return <StatusBadge status={status} {...props} />;
}
