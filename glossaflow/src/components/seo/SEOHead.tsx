import Head from 'next/head';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  noIndex?: boolean;
  noFollow?: boolean;
  structuredData?: object;
}

const defaultMeta = {
  title: 'GlossaFlow - Professional Translation Management Platform',
  description: 'Streamline your translation workflow with GlossaFlow. Manage projects, terminology, and collaborate with translators efficiently.',
  keywords: [
    'translation management',
    'localization',
    'terminology management',
    'translation workflow',
    'CAT tool',
    'translation memory',
    'project management',
    'multilingual',
  ],
  ogImage: '/images/og-image.png',
  ogType: 'website' as const,
  twitterCard: 'summary_large_image' as const,
};

export function SEOHead({
  title,
  description = defaultMeta.description,
  keywords = defaultMeta.keywords,
  canonicalUrl,
  ogImage = defaultMeta.ogImage,
  ogType = defaultMeta.ogType,
  twitterCard = defaultMeta.twitterCard,
  noIndex = false,
  noFollow = false,
  structuredData,
}: SEOHeadProps) {
  const fullTitle = title 
    ? `${title} | GlossaFlow`
    : defaultMeta.title;

  const robotsContent = [
    noIndex ? 'noindex' : 'index',
    noFollow ? 'nofollow' : 'follow',
  ].join(', ');

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="robots" content={robotsContent} />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:site_name" content="GlossaFlow" />
      {canonicalUrl && <meta property="og:url" content={canonicalUrl} />}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="theme-color" content="#2563eb" />
      <meta name="msapplication-TileColor" content="#2563eb" />
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}
    </Head>
  );
}

// Predefined structured data schemas
export const createOrganizationSchema = (organization: {
  name: string;
  url: string;
  logo?: string;
  description?: string;
}) => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: organization.name,
  url: organization.url,
  logo: organization.logo,
  description: organization.description,
});

export const createSoftwareApplicationSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'SoftwareApplication',
  name: 'GlossaFlow',
  applicationCategory: 'BusinessApplication',
  operatingSystem: 'Web Browser',
  description: 'Professional translation management platform for streamlined localization workflows',
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'USD',
    description: 'Free tier available',
  },
  featureList: [
    'Translation Project Management',
    'Terminology Management',
    'Translation Memory',
    'Collaborative Translation',
    'Quality Assurance',
    'Workflow Automation',
  ],
});

export const createBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: breadcrumbs.map((breadcrumb, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: breadcrumb.name,
    item: breadcrumb.url,
  })),
});
