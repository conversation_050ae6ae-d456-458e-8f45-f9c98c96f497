'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Check,
  X,
  Eye,
  Clock
} from 'lucide-react';
import { type TerminologyEntry } from '@/lib/api/terminology';

interface TerminologyTableProps {
  terminology: TerminologyEntry[];
  onEdit: (term: TerminologyEntry) => void;
  onDelete: (term: TerminologyEntry) => void;
  onApprove: (termId: string) => void;
  onReject: (termId: string) => void;
  isLoading?: boolean;
}

const statusColors = {
  approved: 'bg-green-100 text-green-800',
  pending: 'bg-yellow-100 text-yellow-800',
  rejected: 'bg-red-100 text-red-800',
};

const statusIcons = {
  approved: Check,
  pending: Clock,
  rejected: X,
};

const categoryColors = {
  technical: 'bg-blue-100 text-blue-800',
  general: 'bg-gray-100 text-gray-800',
  character: 'bg-purple-100 text-purple-800',
  location: 'bg-green-100 text-green-800',
  concept: 'bg-orange-100 text-orange-800',
};

export function TerminologyTable({
  terminology,
  onEdit,
  onDelete,
  onApprove,
  onReject,
  isLoading
}: TerminologyTableProps) {
  const [selectedTerms, setSelectedTerms] = useState<string[]>([]);

  if (terminology.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <div className="text-gray-400 mb-4">
            <Eye className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No terminology found</h3>
          <p className="text-gray-600">
            No terms match your current filters. Try adjusting your search criteria.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Terminology Entries ({terminology.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Source Term</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Target Term</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Language</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Usage</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Created By</th>
                <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {terminology.map((term) => {
                const StatusIcon = statusIcons[term.approvalStatus];
                return (
                  <tr key={term.id} className="border-b hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{term.sourceTerm}</div>
                        {term.context && (
                          <div className="text-sm text-gray-500 mt-1">{term.context}</div>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-900">{term.targetTerm}</div>
                      {term.usageNotes && (
                        <div className="text-sm text-gray-500 mt-1">{term.usageNotes}</div>
                      )}
                    </td>
                    <td className="py-4 px-4">
                      <span className="text-sm text-gray-900">{term.targetLanguage}</span>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={categoryColors[term.category as keyof typeof categoryColors]}>
                        {term.category}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={statusColors[term.approvalStatus]}>
                        <StatusIcon className="mr-1 h-3 w-3" />
                        {term.approvalStatus}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm">
                        <div className="font-medium">{term.frequency} times</div>
                        <div className="text-gray-500">Last: {term.lastUsed}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm">
                        <div className="font-medium">{term.createdBy}</div>
                        <div className="text-gray-500">{term.createdAt}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onEdit(term)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          {term.approvalStatus === 'pending' && (
                            <>
                              <DropdownMenuItem onClick={() => onApprove(term.id)}>
                                <Check className="mr-2 h-4 w-4" />
                                Approve
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onReject(term.id)}>
                                <X className="mr-2 h-4 w-4" />
                                Reject
                              </DropdownMenuItem>
                            </>
                          )}
                          <DropdownMenuItem
                            onClick={() => onDelete(term)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
