'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2 } from 'lucide-react';

const createTermSchema = z.object({
  sourceTerm: z.string().min(1, 'Source term is required'),
  targetTerm: z.string().min(1, 'Target term is required'),
  targetLanguage: z.string().min(1, 'Target language is required'),
  category: z.string().min(1, 'Category is required'),
  context: z.string().optional(),
  usageNotes: z.string().optional(),
});

type CreateTermFormData = z.infer<typeof createTermSchema>;

interface CreateTermDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateTermFormData) => void;
}

const languages = [
  'Japanese',
  'Spanish',
  'French',
  'German',
  'Korean',
  'Chinese (Simplified)',
  'Chinese (Traditional)',
  'Portuguese',
  'Italian',
  'Russian',
];

const categories = [
  { value: 'technical', label: 'Technical' },
  { value: 'general', label: 'General' },
  { value: 'character', label: 'Character' },
  { value: 'location', label: 'Location' },
  { value: 'concept', label: 'Concept' },
  { value: 'custom', label: 'Custom' },
];

export function CreateTermDialog({ open, onOpenChange, onSubmit }: CreateTermDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<CreateTermFormData>({
    resolver: zodResolver(createTermSchema),
  });

  const targetLanguage = watch('targetLanguage');
  const category = watch('category');

  const handleFormSubmit = async (data: CreateTermFormData) => {
    setIsLoading(true);
    try {
      await onSubmit(data);
      reset();
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Term</DialogTitle>
          <DialogDescription>
            Create a new terminology entry for your translation database.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sourceTerm">Source Term *</Label>
              <Input
                id="sourceTerm"
                placeholder="Enter source term"
                {...register('sourceTerm')}
                disabled={isLoading}
              />
              {errors.sourceTerm && (
                <p className="text-sm text-red-600">{errors.sourceTerm.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetTerm">Target Term *</Label>
              <Input
                id="targetTerm"
                placeholder="Enter target term"
                {...register('targetTerm')}
                disabled={isLoading}
              />
              {errors.targetTerm && (
                <p className="text-sm text-red-600">{errors.targetTerm.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetLanguage">Target Language *</Label>
              <Select
                value={targetLanguage}
                onValueChange={(value) => setValue('targetLanguage', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((language) => (
                    <SelectItem key={language} value={language}>
                      {language}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.targetLanguage && (
                <p className="text-sm text-red-600">{errors.targetLanguage.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={category}
                onValueChange={(value) => setValue('category', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((cat) => (
                    <SelectItem key={cat.value} value={cat.value}>
                      {cat.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-600">{errors.category.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="context">Context</Label>
            <Textarea
              id="context"
              placeholder="Provide context for when this term should be used"
              {...register('context')}
              disabled={isLoading}
              rows={3}
            />
            {errors.context && (
              <p className="text-sm text-red-600">{errors.context.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="usageNotes">Usage Notes</Label>
            <Textarea
              id="usageNotes"
              placeholder="Add any special usage notes or guidelines"
              {...register('usageNotes')}
              disabled={isLoading}
              rows={3}
            />
            {errors.usageNotes && (
              <p className="text-sm text-red-600">{errors.usageNotes.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Term
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
