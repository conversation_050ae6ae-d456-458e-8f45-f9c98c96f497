'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Lightbulb,
  Check,
  X,
  ArrowRight,
  TrendingUp,
  Clock,
  AlertCircle,
} from 'lucide-react';
import Link from 'next/link';
import { TermEditor } from './TermEditor';
import {
  useGetTermCandidatesQuery,
  useApproveTermCandidateMutation,
  useRejectTermCandidateMutation,
  type TermCandidate,
} from '@/lib/api/term-candidates';

interface TermCandidatesWidgetProps {
  projectId?: string;
  limit?: number;
  showProjectFilter?: boolean;
}

export function TermCandidatesWidget({
  projectId,
  limit = 5,
  showProjectFilter = false,
}: TermCandidatesWidgetProps) {
  const [showTermEditor, setShowTermEditor] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<TermCandidate | null>(null);

  // API queries
  const {
    data: candidatesResponse,
    isLoading,
    isError,
  } = useGetTermCandidatesQuery({
    status: 'pending',
    limit,
    sortBy: 'confidence_score',
    sortOrder: 'desc',
    ...(projectId && { projectId }),
  });

  const [approveTermCandidate] = useApproveTermCandidateMutation();
  const [rejectTermCandidate] = useRejectTermCandidateMutation();

  const candidates = candidatesResponse?.data?.items || [];
  const totalPending = candidatesResponse?.data?.pagination?.total || 0;

  const handleQuickApprove = (candidate: TermCandidate) => {
    setSelectedCandidate(candidate);
    setShowTermEditor(true);
  };

  const handleQuickReject = async (candidateId: string) => {
    try {
      await rejectTermCandidate({
        id: candidateId,
        data: { reason: 'Quick reject from terminology widget' },
      }).unwrap();
    } catch (error) {
      console.error('Failed to reject candidate:', error);
    }
  };

  const handleTermEditorSubmit = async (termData: any) => {
    if (!selectedCandidate) return;

    try {
      await approveTermCandidate({
        id: selectedCandidate.id,
        data: {
          targetTerm: termData.targetTerm,
          targetLanguage: termData.targetLanguage,
          category: termData.category,
          context: termData.context,
          usageNotes: termData.usageNotes,
          addToGlossary: false,
        },
      }).unwrap();

      setShowTermEditor(false);
      setSelectedCandidate(null);
    } catch (error) {
      console.error('Failed to approve candidate:', error);
    }
  };

  const getConfidenceBadge = (score: number) => {
    if (score >= 0.8) {
      return <Badge variant="secondary" className="bg-green-100 text-green-800">High</Badge>;
    } else if (score >= 0.5) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    } else {
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Low</Badge>;
    }
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-yellow-600" />
            Term Candidates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load term candidates. Please try again later.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-600" />
                Pending Term Candidates
                {totalPending > 0 && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    {totalPending}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                AI-extracted terminology candidates awaiting review
              </CardDescription>
            </div>
            {totalPending > limit && (
              <Button asChild variant="outline" size="sm">
                <Link href="/dashboard/term-suggestions">
                  View All
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="h-4 bg-gray-200 rounded animate-pulse flex-1" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
                  <div className="h-8 bg-gray-200 rounded animate-pulse w-20" />
                </div>
              ))}
            </div>
          ) : candidates.length === 0 ? (
            <div className="text-center py-6">
              <Lightbulb className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-3">
                No pending term candidates found
              </p>
              <Button asChild variant="outline" size="sm">
                <Link href="/dashboard/term-suggestions">
                  <Lightbulb className="mr-2 h-4 w-4" />
                  Extract Terms
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {candidates.map((candidate) => (
                <div
                  key={candidate.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-gray-900">
                        {candidate.candidateTerm}
                      </span>
                      {getConfidenceBadge(candidate.confidenceScore)}
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <TrendingUp className="h-3 w-3" />
                        {candidate.frequency}
                      </div>
                    </div>
                    {candidate.contextSentence && (
                      <p className="text-sm text-gray-600" title={candidate.contextSentence}>
                        {truncateText(candidate.contextSentence, 80)}
                      </p>
                    )}
                    {candidate.project && showProjectFilter && (
                      <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                        <Clock className="h-3 w-3" />
                        {candidate.project.name}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleQuickApprove(candidate)}
                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleQuickReject(candidate.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              
              {totalPending > candidates.length && (
                <div className="text-center pt-3 border-t">
                  <Button asChild variant="ghost" size="sm">
                    <Link href="/dashboard/term-suggestions">
                      View {totalPending - candidates.length} more candidates
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Term Editor Dialog */}
      <TermEditor
        open={showTermEditor}
        onOpenChange={setShowTermEditor}
        onSubmit={handleTermEditorSubmit}
        mode="from-candidate"
        termCandidate={selectedCandidate}
      />
    </>
  );
}
