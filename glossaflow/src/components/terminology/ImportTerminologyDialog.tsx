'use client';

import { useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  FileText, 
  Download, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ImportTerminologyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (data: any) => void;
}

const supportedFormats = [
  { value: 'csv', label: 'CSV', description: 'Comma-separated values' },
  { value: 'tmx', label: 'TMX', description: 'Translation Memory eXchange' },
  { value: 'excel', label: 'Excel', description: 'Microsoft Excel (.xlsx)' },
];

const fieldMappings = [
  { key: 'sourceTerm', label: 'Source Term', required: true },
  { key: 'targetTerm', label: 'Target Term', required: true },
  { key: 'targetLanguage', label: 'Target Language', required: true },
  { key: 'category', label: 'Category', required: false },
  { key: 'context', label: 'Context', required: false },
  { key: 'usageNotes', label: 'Usage Notes', required: false },
];

export function ImportTerminologyDialog({ open, onOpenChange, onImport }: ImportTerminologyDialogProps) {
  const [step, setStep] = useState(1);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [format, setFormat] = useState('');
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [overwriteExisting, setOverwriteExisting] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      // Auto-detect format based on file extension
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (extension === 'csv') setFormat('csv');
      else if (extension === 'tmx') setFormat('tmx');
      else if (extension === 'xlsx' || extension === 'xls') setFormat('excel');
    }
  };

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleImport = async () => {
    setIsProcessing(true);
    setProgress(0);

    // Simulate import process
    const intervals = [20, 40, 60, 80, 100];
    for (const targetProgress of intervals) {
      await new Promise(resolve => setTimeout(resolve, 500));
      setProgress(targetProgress);
    }

    // Mock results
    const mockResults = {
      imported: 45,
      errors: 2,
      duplicates: 3,
      total: 50,
    };

    setResults(mockResults);
    setIsProcessing(false);
    setStep(4);
  };

  const handleClose = () => {
    setStep(1);
    setSelectedFile(null);
    setFormat('');
    setMapping({});
    setOverwriteExisting(false);
    setIsProcessing(false);
    setProgress(0);
    setResults(null);
    onOpenChange(false);
  };

  const handleComplete = () => {
    onImport({
      file: selectedFile,
      format,
      mapping,
      overwriteExisting,
      results,
    });
    handleClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Import Terminology</DialogTitle>
          <DialogDescription>
            Import terminology entries from external files
          </DialogDescription>
        </DialogHeader>

        {/* Step 1: File Selection */}
        {step === 1 && (
          <div className="space-y-6">
            <div className="space-y-4">
              <Label>Select File Format</Label>
              <div className="grid grid-cols-1 gap-3">
                {supportedFormats.map((fmt) => (
                  <div
                    key={fmt.value}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      format === fmt.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setFormat(fmt.value)}
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="font-medium">{fmt.label}</div>
                        <div className="text-sm text-gray-500">{fmt.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <Label>Upload File</Label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <div className="text-sm text-gray-600">
                  {selectedFile ? (
                    <span className="font-medium">{selectedFile.name}</span>
                  ) : (
                    <>Click to upload or drag and drop</>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  className="hidden"
                  accept=".csv,.tmx,.xlsx,.xls"
                  onChange={handleFileSelect}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button onClick={handleNext} disabled={!selectedFile || !format}>
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Field Mapping */}
        {step === 2 && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Map Fields</h3>
              <p className="text-sm text-gray-600 mb-4">
                Map the columns in your file to the terminology fields.
              </p>
            </div>

            <div className="space-y-4">
              {fieldMappings.map((field) => (
                <div key={field.key} className="grid grid-cols-2 gap-4 items-center">
                  <Label className="flex items-center">
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  <Select
                    value={mapping[field.key] || ''}
                    onValueChange={(value) => setMapping({ ...mapping, [field.key]: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select column" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="column1">Column 1</SelectItem>
                      <SelectItem value="column2">Column 2</SelectItem>
                      <SelectItem value="column3">Column 3</SelectItem>
                      <SelectItem value="column4">Column 4</SelectItem>
                      <SelectItem value="column5">Column 5</SelectItem>
                      <SelectItem value="column6">Column 6</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="overwrite"
                checked={overwriteExisting}
                onCheckedChange={(checked) => setOverwriteExisting(checked as boolean)}
              />
              <Label htmlFor="overwrite" className="text-sm">
                Overwrite existing terms with the same source term
              </Label>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button onClick={handleNext}>
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Confirmation */}
        {step === 3 && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Confirm Import</h3>
              <p className="text-sm text-gray-600 mb-4">
                Review your import settings before proceeding.
              </p>
            </div>

            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between">
                <span className="text-sm font-medium">File:</span>
                <span className="text-sm">{selectedFile?.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Format:</span>
                <span className="text-sm">{format.toUpperCase()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Overwrite existing:</span>
                <span className="text-sm">{overwriteExisting ? 'Yes' : 'No'}</span>
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button onClick={handleImport} disabled={isProcessing}>
                {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Import
              </Button>
            </div>
          </div>
        )}

        {/* Step 4: Results */}
        {step === 4 && (
          <div className="space-y-6">
            {isProcessing ? (
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                <div>
                  <h3 className="text-lg font-medium mb-2">Importing terminology...</h3>
                  <Progress value={progress} className="w-full" />
                  <p className="text-sm text-gray-600 mt-2">{progress}% complete</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="text-lg font-medium">Import Complete</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-green-50 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{results?.imported}</div>
                    <div className="text-sm text-green-700">Imported</div>
                  </div>
                  <div className="p-4 bg-yellow-50 rounded-lg text-center">
                    <div className="text-2xl font-bold text-yellow-600">{results?.duplicates}</div>
                    <div className="text-sm text-yellow-700">Duplicates</div>
                  </div>
                </div>

                {results?.errors > 0 && (
                  <div className="p-4 bg-red-50 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                      <span className="text-sm text-red-700">
                        {results.errors} entries had errors and were skipped
                      </span>
                    </div>
                  </div>
                )}

                <div className="flex justify-end">
                  <Button onClick={handleComplete}>
                    Done
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
