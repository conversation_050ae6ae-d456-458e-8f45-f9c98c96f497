'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Edit, Plus, Lightbulb } from 'lucide-react';
import { type TerminologyEntry } from '@/lib/api/terminology';
import { type TermCandidate } from '@/lib/api/term-candidates';

const termEditorSchema = z.object({
  sourceTerm: z.string().min(1, 'Source term is required'),
  targetTerm: z.string().min(1, 'Target term is required'),
  targetLanguage: z.string().min(1, 'Target language is required'),
  category: z.string().min(1, 'Category is required'),
  context: z.string().optional(),
  usageNotes: z.string().optional(),
  projectId: z.string().optional(),
});

type TermEditorFormData = z.infer<typeof termEditorSchema>;

interface TermEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: TermEditorFormData) => void;
  isLoading?: boolean;
  mode: 'create' | 'edit' | 'from-candidate';
  existingTerm?: TerminologyEntry;
  termCandidate?: TermCandidate;
  projectId?: string;
}

const languages = [
  'Japanese',
  'Spanish',
  'French',
  'German',
  'Korean',
  'Chinese (Simplified)',
  'Chinese (Traditional)',
  'Portuguese',
  'Italian',
  'Russian',
];

const categories = [
  'technical',
  'general',
  'character',
  'location',
  'concept',
  'business',
  'medical',
  'legal',
];

export function TermEditor({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  mode,
  existingTerm,
  termCandidate,
  projectId,
}: TermEditorProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<TermEditorFormData>({
    resolver: zodResolver(termEditorSchema),
  });

  const targetLanguage = watch('targetLanguage');
  const category = watch('category');

  // Pre-populate form based on mode
  useEffect(() => {
    if (mode === 'edit' && existingTerm) {
      setValue('sourceTerm', existingTerm.sourceTerm);
      setValue('targetTerm', existingTerm.targetTerm);
      setValue('targetLanguage', existingTerm.targetLanguage);
      setValue('category', existingTerm.category);
      setValue('context', existingTerm.context || '');
      setValue('usageNotes', existingTerm.usageNotes || '');
      setValue('projectId', existingTerm.projectId || '');
    } else if (mode === 'from-candidate' && termCandidate) {
      setValue('sourceTerm', termCandidate.candidateTerm);
      setValue('targetTerm', ''); // User needs to provide translation
      setValue('targetLanguage', ''); // User needs to select language
      setValue('category', termCandidate.assignedCategory || '');
      setValue('context', termCandidate.contextSentence || '');
      setValue('usageNotes', '');
      setValue('projectId', termCandidate.projectId);
    } else if (mode === 'create') {
      setValue('projectId', projectId || '');
    }
  }, [mode, existingTerm, termCandidate, projectId, setValue]);

  const handleFormSubmit = async (data: TermEditorFormData) => {
    try {
      await onSubmit(data);
      reset();
    } catch (error) {
      console.error('Failed to save term:', error);
    }
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  const getDialogTitle = () => {
    switch (mode) {
      case 'edit':
        return 'Edit Terminology Entry';
      case 'from-candidate':
        return 'Add Term from Candidate';
      default:
        return 'Create Terminology Entry';
    }
  };

  const getDialogDescription = () => {
    switch (mode) {
      case 'edit':
        return 'Update the terminology entry details.';
      case 'from-candidate':
        return 'Complete the terminology entry based on the extracted candidate.';
      default:
        return 'Create a new terminology entry for your project.';
    }
  };

  const getSubmitButtonText = () => {
    if (isLoading) {
      return mode === 'edit' ? 'Updating...' : 'Creating...';
    }
    return mode === 'edit' ? 'Update Term' : 'Create Term';
  };

  const getSubmitButtonIcon = () => {
    if (isLoading) {
      return <Loader2 className="mr-2 h-4 w-4 animate-spin" />;
    }
    switch (mode) {
      case 'edit':
        return <Edit className="mr-2 h-4 w-4" />;
      case 'from-candidate':
        return <Lightbulb className="mr-2 h-4 w-4" />;
      default:
        return <Plus className="mr-2 h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getSubmitButtonIcon()}
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>
            {getDialogDescription()}
          </DialogDescription>
        </DialogHeader>

        {/* Candidate Information (when mode is from-candidate) */}
        {mode === 'from-candidate' && termCandidate && (
          <div className="bg-blue-50 rounded-lg p-4 space-y-2">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">From Term Candidate</span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {(termCandidate.confidenceScore * 100).toFixed(0)}% confidence
              </Badge>
            </div>
            <div className="text-sm text-blue-800">
              <span className="font-medium">Frequency:</span> {termCandidate.frequency} occurrences
            </div>
            {termCandidate.extractionMetadata?.pattern_type && (
              <div className="text-sm text-blue-800">
                <span className="font-medium">Pattern:</span> {termCandidate.extractionMetadata.pattern_type}
              </div>
            )}
          </div>
        )}

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Source Term */}
            <div className="space-y-2">
              <Label htmlFor="sourceTerm">Source Term *</Label>
              <Input
                id="sourceTerm"
                placeholder="Enter source term"
                {...register('sourceTerm')}
                disabled={isLoading || mode === 'from-candidate'}
              />
              {errors.sourceTerm && (
                <p className="text-sm text-red-600">{errors.sourceTerm.message}</p>
              )}
            </div>

            {/* Target Term */}
            <div className="space-y-2">
              <Label htmlFor="targetTerm">Target Term *</Label>
              <Input
                id="targetTerm"
                placeholder="Enter target term"
                {...register('targetTerm')}
                disabled={isLoading}
              />
              {errors.targetTerm && (
                <p className="text-sm text-red-600">{errors.targetTerm.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Target Language */}
            <div className="space-y-2">
              <Label htmlFor="targetLanguage">Target Language *</Label>
              <Select onValueChange={(value) => setValue('targetLanguage', value)} disabled={isLoading}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((language) => (
                    <SelectItem key={language} value={language}>
                      {language}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.targetLanguage && (
                <p className="text-sm text-red-600">{errors.targetLanguage.message}</p>
              )}
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select onValueChange={(value) => setValue('category', value)} disabled={isLoading}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat.charAt(0).toUpperCase() + cat.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-600">{errors.category.message}</p>
              )}
            </div>
          </div>

          {/* Context */}
          <div className="space-y-2">
            <Label htmlFor="context">Context</Label>
            <Textarea
              id="context"
              placeholder="Provide context for this term (optional)"
              rows={3}
              {...register('context')}
              disabled={isLoading}
            />
          </div>

          {/* Usage Notes */}
          <div className="space-y-2">
            <Label htmlFor="usageNotes">Usage Notes</Label>
            <Textarea
              id="usageNotes"
              placeholder="Special usage instructions or notes (optional)"
              rows={3}
              {...register('usageNotes')}
              disabled={isLoading}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !targetLanguage || !category}>
              {getSubmitButtonIcon()}
              {getSubmitButtonText()}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
