'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  CheckCircle, 
  AlertTriangle, 
  BookOpen, 
  Users, 
  MapPin, 
  Lightbulb,
  Plus,
  X,
  ArrowRight,
  ArrowLeft,
  FileText,
  Target
} from 'lucide-react';

interface PreTranslationSetupProps {
  projectId: string;
  projectTitle: string;
  sourceLanguage: string;
  targetLanguage: string;
  onSetupComplete?: (setupData: any) => void;
  className?: string;
}

interface TerminologyEntry {
  sourceTerm: string;
  targetTerm: string;
  category: string;
  context?: string;
  usageNotes?: string;
}

interface SetupStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
}

export function PreTranslationSetup({
  projectId,
  projectTitle,
  sourceLanguage,
  targetLanguage,
  onSetupComplete,
  className = '',
}: PreTranslationSetupProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [terminologyEntries, setTerminologyEntries] = useState<TerminologyEntry[]>([]);
  const [newEntry, setNewEntry] = useState<TerminologyEntry>({
    sourceTerm: '',
    targetTerm: '',
    category: 'character',
    context: '',
    usageNotes: '',
  });
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [guidelines, setGuidelines] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps: SetupStep[] = [
    {
      id: 'overview',
      title: 'Project Overview',
      description: 'Review project details and setup requirements',
      completed: true,
      required: true,
    },
    {
      id: 'template',
      title: 'Choose Template',
      description: 'Select a terminology template or start from scratch',
      completed: false,
      required: false,
    },
    {
      id: 'terminology',
      title: 'Define Terminology',
      description: 'Add key terms that will be used throughout the project',
      completed: false,
      required: true,
    },
    {
      id: 'guidelines',
      title: 'Translation Guidelines',
      description: 'Set project-specific translation rules and preferences',
      completed: false,
      required: false,
    },
    {
      id: 'review',
      title: 'Review & Complete',
      description: 'Review your setup and begin translation work',
      completed: false,
      required: true,
    },
  ];

  const templates = [
    {
      id: 'fantasy',
      name: 'Fantasy Novel',
      description: 'Character names, magical terms, locations',
      termCount: 45,
      categories: ['character', 'location', 'concept', 'item'],
    },
    {
      id: 'modern',
      name: 'Modern Fiction',
      description: 'Contemporary settings and terminology',
      termCount: 25,
      categories: ['character', 'location', 'concept'],
    },
    {
      id: 'technical',
      name: 'Technical Documentation',
      description: 'Technical terms and procedures',
      termCount: 60,
      categories: ['technical', 'concept'],
    },
    {
      id: 'custom',
      name: 'Start from Scratch',
      description: 'Create your own terminology from the beginning',
      termCount: 0,
      categories: [],
    },
  ];

  const categories = [
    { value: 'character', label: 'Character', icon: Users, color: 'bg-purple-100 text-purple-800' },
    { value: 'location', label: 'Location', icon: MapPin, color: 'bg-green-100 text-green-800' },
    { value: 'concept', label: 'Concept', icon: Lightbulb, color: 'bg-orange-100 text-orange-800' },
    { value: 'item', label: 'Item', icon: BookOpen, color: 'bg-blue-100 text-blue-800' },
    { value: 'technical', label: 'Technical', icon: Target, color: 'bg-gray-100 text-gray-800' },
  ];

  const addTerminologyEntry = () => {
    if (newEntry.sourceTerm && newEntry.targetTerm) {
      setTerminologyEntries(prev => [...prev, { ...newEntry }]);
      setNewEntry({
        sourceTerm: '',
        targetTerm: '',
        category: 'character',
        context: '',
        usageNotes: '',
      });
    }
  };

  const removeTerminologyEntry = (index: number) => {
    setTerminologyEntries(prev => prev.filter((_, i) => i !== index));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleComplete = async () => {
    setIsSubmitting(true);

    try {
      const setupData = {
        projectId,
        template: selectedTemplate,
        terminology: terminologyEntries,
        guidelines,
        completedAt: new Date().toISOString(),
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (onSetupComplete) {
        onSetupComplete(setupData);
      }
    } catch (error) {
      console.error('Failed to complete setup:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const progress = ((currentStep + 1) / steps.length) * 100;
  const currentStepData = steps[currentStep];

  const renderStepContent = () => {
    switch (currentStepData.id) {
      case 'overview':
        return (
          <div className="space-y-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> Before beginning translation work, you must define key terminology 
                to ensure consistency throughout your project. This setup process will guide you through 
                creating a comprehensive terminology database.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Project Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Title:</span>
                    <span className="font-medium">{projectTitle}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Source Language:</span>
                    <span className="font-medium">{sourceLanguage}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Target Language:</span>
                    <span className="font-medium">{targetLanguage}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Setup Requirements</h4>
                <div className="space-y-2">
                  {steps.filter(step => step.required).map(step => (
                    <div key={step.id} className="flex items-center space-x-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>{step.title}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'template':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Choose a Starting Template</h4>
              <p className="text-sm text-gray-600 mb-4">
                Templates provide pre-defined terminology categories and examples to help you get started quickly.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {templates.map(template => (
                <Card 
                  key={template.id}
                  className={`cursor-pointer transition-colors ${
                    selectedTemplate === template.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h5 className="font-medium">{template.name}</h5>
                        <Badge variant="outline">{template.termCount} terms</Badge>
                      </div>
                      <p className="text-sm text-gray-600">{template.description}</p>
                      {template.categories.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {template.categories.map(cat => {
                            const category = categories.find(c => c.value === cat);
                            return category ? (
                              <Badge key={cat} className={`text-xs ${category.color}`}>
                                {category.label}
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 'terminology':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Define Key Terminology</h4>
              <p className="text-sm text-gray-600 mb-4">
                Add important terms that will appear throughout your project. Focus on character names, 
                locations, and domain-specific vocabulary.
              </p>
            </div>

            {/* Add New Term Form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Add New Term</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Source Term</label>
                    <Input
                      value={newEntry.sourceTerm}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, sourceTerm: e.target.value }))}
                      placeholder="Enter source term..."
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Target Translation</label>
                    <Input
                      value={newEntry.targetTerm}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, targetTerm: e.target.value }))}
                      placeholder="Enter translation..."
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Category</label>
                  <Select
                    value={newEntry.category}
                    onValueChange={(value) => setNewEntry(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Context (Optional)</label>
                  <Input
                    value={newEntry.context}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, context: e.target.value }))}
                    placeholder="Where this term is used..."
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Usage Notes (Optional)</label>
                  <Textarea
                    value={newEntry.usageNotes}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, usageNotes: e.target.value }))}
                    placeholder="Special usage instructions..."
                    rows={2}
                  />
                </div>

                <Button onClick={addTerminologyEntry} className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Term
                </Button>
              </CardContent>
            </Card>

            {/* Terms List */}
            {terminologyEntries.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Added Terms ({terminologyEntries.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {terminologyEntries.map((entry, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{entry.sourceTerm}</span>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                            <span className="text-blue-600">{entry.targetTerm}</span>
                            <Badge className={categories.find(c => c.value === entry.category)?.color}>
                              {categories.find(c => c.value === entry.category)?.label}
                            </Badge>
                          </div>
                          {entry.context && (
                            <p className="text-sm text-gray-600">{entry.context}</p>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTerminologyEntry(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {terminologyEntries.length === 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You must add at least one terminology entry before proceeding to translation.
                </AlertDescription>
              </Alert>
            )}
          </div>
        );

      case 'guidelines':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Translation Guidelines</h4>
              <p className="text-sm text-gray-600 mb-4">
                Define project-specific translation rules and preferences that will guide the translation process.
              </p>
            </div>

            <div>
              <label className="text-sm font-medium">Project Guidelines</label>
              <Textarea
                value={guidelines}
                onChange={(e) => setGuidelines(e.target.value)}
                placeholder="Enter translation guidelines, style preferences, and any special instructions..."
                rows={8}
                className="mt-1"
              />
            </div>

            <Alert>
              <Lightbulb className="h-4 w-4" />
              <AlertDescription>
                <strong>Tip:</strong> Include information about tone, formality level, cultural adaptations, 
                and any specific requirements for this project.
              </AlertDescription>
            </Alert>
          </div>
        );

      case 'review':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Review Your Setup</h4>
              <p className="text-sm text-gray-600 mb-4">
                Review your terminology and guidelines before completing the setup process.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Terminology Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Terms:</span>
                      <span className="font-medium">{terminologyEntries.length}</span>
                    </div>
                    {categories.map(category => {
                      const count = terminologyEntries.filter(t => t.category === category.value).length;
                      return count > 0 ? (
                        <div key={category.value} className="flex justify-between">
                          <span>{category.label}:</span>
                          <span className="font-medium">{count}</span>
                        </div>
                      ) : null;
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Setup Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {steps.map(step => (
                      <div key={step.id} className="flex items-center justify-between">
                        <span className="text-sm">{step.title}</span>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your project is ready for translation! Click "Complete Setup" to begin translation work.
              </AlertDescription>
            </Alert>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`max-w-4xl mx-auto space-y-6 ${className}`}>
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Pre-Translation Setup</CardTitle>
            <Badge variant="outline">
              Step {currentStep + 1} of {steps.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>{currentStepData.title}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
            <p className="text-sm text-gray-600">{currentStepData.description}</p>
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle>{currentStepData.title}</CardTitle>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        {currentStep === steps.length - 1 ? (
          <Button
            onClick={handleComplete}
            disabled={isSubmitting || terminologyEntries.length === 0}
          >
            {isSubmitting ? (
              <>
                <CheckCircle className="mr-2 h-4 w-4 animate-spin" />
                Completing Setup...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Complete Setup
              </>
            )}
          </Button>
        ) : (
          <Button
            onClick={handleNext}
            disabled={currentStepData.id === 'terminology' && terminologyEntries.length === 0}
          >
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
