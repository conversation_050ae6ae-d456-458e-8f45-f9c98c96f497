'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info,
  Lightbulb,
  Target,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import { TerminologyValidator, ValidationResult, ValidationIssue } from '@/lib/services/terminology-validator';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';

interface TerminologyValidationPanelProps {
  projectId: string;
  sourceText: string;
  targetText: string;
  targetLanguage: string;
  onApplySuggestion?: (sourceTerm: string, targetTerm: string) => void;
  className?: string;
}

export function TerminologyValidationPanel({
  projectId,
  sourceText,
  targetText,
  targetLanguage,
  onApplySuggestion,
  className = '',
}: TerminologyValidationPanelProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Fetch project terminology
  const {
    data: terminologyResponse,
    isLoading: isLoadingTerminology,
  } = useGetTerminologyForProjectQuery({
    projectId,
    targetLanguage,
    approvalStatus: 'approved',
  });

  const terminology = terminologyResponse?.data || [];

  // Validate translation when inputs change
  useEffect(() => {
    if (sourceText && targetText && terminology.length > 0) {
      validateTranslation();
    }
  }, [sourceText, targetText, terminology]);

  const validateTranslation = async () => {
    if (!sourceText || !targetText || terminology.length === 0) return;

    setIsValidating(true);
    
    try {
      // Convert terminology to validator format
      const validatorTerminology = terminology.map(term => ({
        id: term.id,
        sourceTerm: term.sourceTerm,
        targetTerm: term.targetTerm,
        category: term.category,
        context: term.context,
        usageNotes: term.usageNotes,
        frequency: term.frequency || 0,
        approvalStatus: 'approved' as const,
      }));

      const validator = new TerminologyValidator(validatorTerminology);
      const result = validator.validateTranslation(sourceText, targetText, targetLanguage);
      
      setValidationResult(result);
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const handleApplySuggestion = (sourceTerm: string, targetTerm: string) => {
    if (onApplySuggestion) {
      onApplySuggestion(sourceTerm, targetTerm);
    }
  };

  const getSeverityIcon = (severity: ValidationIssue['severity']) => {
    switch (severity) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: ValidationIssue['severity']) => {
    switch (severity) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'secondary';
      case 'info':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (isLoadingTerminology) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="mr-2 h-4 w-4" />
            Terminology Validation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!sourceText || !targetText) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="mr-2 h-4 w-4" />
            Terminology Validation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Enter source and target text to validate terminology usage.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Target className="mr-2 h-4 w-4" />
            Terminology Validation
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={validateTranslation}
            disabled={isValidating}
          >
            {isValidating ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {validationResult ? (
          <>
            {/* Validation Score */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Consistency Score</span>
                <span className="text-sm text-gray-600">
                  {Math.round(validationResult.score * 100)}%
                </span>
              </div>
              <Progress value={validationResult.score * 100} className="h-2" />
            </div>

            {/* Overall Status */}
            <Alert className={validationResult.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              {validationResult.isValid ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={validationResult.isValid ? 'text-green-800' : 'text-red-800'}>
                {validationResult.isValid 
                  ? 'Translation passes terminology validation'
                  : `${validationResult.issues.filter(i => i.severity === 'error').length} terminology error(s) found`
                }
              </AlertDescription>
            </Alert>

            {/* Issues */}
            {validationResult.issues.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Issues ({validationResult.issues.length})
                </h4>
                <div className="space-y-2">
                  {validationResult.issues.map((issue, index) => (
                    <div key={index} className="p-3 border rounded-lg bg-gray-50">
                      <div className="flex items-start space-x-2">
                        {getSeverityIcon(issue.severity)}
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium">{issue.message}</p>
                          {issue.expectedTerm && (
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="text-xs">
                                Expected: {issue.expectedTerm}
                              </Badge>
                              {issue.actualTerm && (
                                <Badge variant={getSeverityColor(issue.severity)} className="text-xs">
                                  Found: {issue.actualTerm}
                                </Badge>
                              )}
                            </div>
                          )}
                          {issue.suggestions.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {issue.suggestions.map((suggestion, suggestionIndex) => (
                                <Button
                                  key={suggestionIndex}
                                  variant="outline"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => handleApplySuggestion(issue.sourceTerm, suggestion)}
                                >
                                  Apply: {suggestion}
                                </Button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Suggestions */}
            {validationResult.suggestions.length > 0 && (
              <div className="space-y-3">
                <Separator />
                <h4 className="text-sm font-medium flex items-center">
                  <Lightbulb className="mr-2 h-4 w-4" />
                  Suggestions ({validationResult.suggestions.length})
                </h4>
                <div className="space-y-2">
                  {validationResult.suggestions.map((suggestion, index) => (
                    <div key={index} className="p-3 border rounded-lg bg-blue-50">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{suggestion.message}</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {Math.round(suggestion.confidence * 100)}% confidence
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {suggestion.type.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApplySuggestion(suggestion.sourceTerm, suggestion.targetTerm)}
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Terminology Usage */}
            {validationResult.terminologyUsage.length > 0 && (
              <div className="space-y-3">
                <Separator />
                <h4 className="text-sm font-medium flex items-center">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Terminology Usage ({validationResult.terminologyUsage.length})
                </h4>
                <div className="space-y-2">
                  {validationResult.terminologyUsage.map((usage, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded bg-gray-50">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{usage.sourceTerm}</span>
                        <span className="text-sm text-gray-500">→</span>
                        <span className="text-sm">{usage.targetTerm}</span>
                        {usage.isCorrect ? (
                          <CheckCircle className="h-3 w-3 text-green-500" />
                        ) : (
                          <XCircle className="h-3 w-3 text-red-500" />
                        )}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {usage.frequency}x
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        ) : (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Click the refresh button to validate terminology usage.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
