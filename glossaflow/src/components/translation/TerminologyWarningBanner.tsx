'use client';

import { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  X, 
  CheckCircle, 
  ArrowRight,
  Lightbulb
} from 'lucide-react';
import { TerminologyValidator, ValidationResult } from '@/lib/services/terminology-validator';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';

interface TerminologyWarningBannerProps {
  projectId: string;
  sourceText: string;
  targetText: string;
  targetLanguage: string;
  onApplyFix?: (sourceTerm: string, targetTerm: string) => void;
  onDismiss?: () => void;
  className?: string;
}

export function TerminologyWarningBanner({
  projectId,
  sourceText,
  targetText,
  targetLanguage,
  onApplyFix,
  onDismiss,
  className = '',
}: TerminologyWarningBannerProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isDismissed, setIsDismissed] = useState(false);
  const [appliedFixes, setAppliedFixes] = useState<Set<string>>(new Set());

  // Fetch project terminology
  const {
    data: terminologyResponse,
    isLoading,
  } = useGetTerminologyForProjectQuery({
    projectId,
    targetLanguage,
    approvalStatus: 'approved',
  });

  const terminology = terminologyResponse?.data || [];

  // Validate translation when inputs change
  useEffect(() => {
    if (sourceText && targetText && terminology.length > 0 && !isDismissed) {
      validateTranslation();
    }
  }, [sourceText, targetText, terminology, isDismissed]);

  // Reset dismissal when source text changes significantly
  useEffect(() => {
    setIsDismissed(false);
    setAppliedFixes(new Set());
  }, [sourceText]);

  const validateTranslation = async () => {
    if (!sourceText || !targetText || terminology.length === 0) return;

    try {
      // Convert terminology to validator format
      const validatorTerminology = terminology.map(term => ({
        id: term.id,
        sourceTerm: term.sourceTerm,
        targetTerm: term.targetTerm,
        category: term.category,
        context: term.context,
        usageNotes: term.usageNotes,
        frequency: term.frequency || 0,
        approvalStatus: 'approved' as const,
      }));

      const validator = new TerminologyValidator(validatorTerminology);
      const result = validator.validateTranslation(sourceText, targetText, targetLanguage);
      
      setValidationResult(result);
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const handleApplyFix = (sourceTerm: string, targetTerm: string) => {
    if (onApplyFix) {
      onApplyFix(sourceTerm, targetTerm);
      setAppliedFixes(prev => new Set([...prev, sourceTerm]));
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  // Don't show if loading, dismissed, or no issues
  if (isLoading || isDismissed || !validationResult || validationResult.issues.length === 0) {
    return null;
  }

  // Filter out issues that have been fixed
  const activeIssues = validationResult.issues.filter(
    issue => !appliedFixes.has(issue.sourceTerm)
  );

  if (activeIssues.length === 0) {
    return null;
  }

  // Prioritize errors over warnings
  const criticalIssues = activeIssues.filter(issue => issue.severity === 'error');
  const warningIssues = activeIssues.filter(issue => issue.severity === 'warning');
  const displayIssues = criticalIssues.length > 0 ? criticalIssues : warningIssues.slice(0, 3);

  const severityColor = criticalIssues.length > 0 ? 'destructive' : 'default';
  const severityIcon = criticalIssues.length > 0 ? AlertTriangle : Lightbulb;
  const SeverityIcon = severityIcon;

  return (
    <Alert className={`border-l-4 ${criticalIssues.length > 0 ? 'border-l-red-500 bg-red-50' : 'border-l-yellow-500 bg-yellow-50'} ${className}`}>
      <SeverityIcon className={`h-4 w-4 ${criticalIssues.length > 0 ? 'text-red-600' : 'text-yellow-600'}`} />
      <div className="flex-1">
        <AlertDescription>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className={`font-medium ${criticalIssues.length > 0 ? 'text-red-800' : 'text-yellow-800'}`}>
                  {criticalIssues.length > 0 ? 'Terminology Errors' : 'Terminology Suggestions'}
                </span>
                <Badge variant={severityColor} className="text-xs">
                  {activeIssues.length} issue{activeIssues.length !== 1 ? 's' : ''}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Score: {Math.round(validationResult.score * 100)}%
                </Badge>
              </div>

              <div className="space-y-2">
                {displayIssues.map((issue, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {issue.message}
                      </p>
                      {issue.expectedTerm && (
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs text-gray-600">Expected:</span>
                          <Badge variant="outline" className="text-xs">
                            {issue.expectedTerm}
                          </Badge>
                          {issue.actualTerm && (
                            <>
                              <ArrowRight className="h-3 w-3 text-gray-400" />
                              <Badge variant="secondary" className="text-xs">
                                Found: {issue.actualTerm}
                              </Badge>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {issue.suggestions.length > 0 && (
                      <div className="flex space-x-1 ml-2">
                        {issue.suggestions.slice(0, 2).map((suggestion, suggestionIndex) => (
                          <Button
                            key={suggestionIndex}
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => handleApplyFix(issue.sourceTerm, suggestion)}
                          >
                            Apply: {suggestion}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}

                {activeIssues.length > displayIssues.length && (
                  <p className="text-xs text-gray-600">
                    +{activeIssues.length - displayIssues.length} more issue{activeIssues.length - displayIssues.length !== 1 ? 's' : ''}
                  </p>
                )}
              </div>

              {validationResult.suggestions.length > 0 && (
                <div className="mt-3 pt-2 border-t">
                  <p className="text-xs font-medium text-gray-700 mb-1">Quick Actions:</p>
                  <div className="flex flex-wrap gap-1">
                    {validationResult.suggestions.slice(0, 3).map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        className="h-6 px-2 text-xs"
                        onClick={() => handleApplyFix(suggestion.sourceTerm, suggestion.targetTerm)}
                      >
                        {suggestion.action}: {suggestion.targetTerm}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 ml-2"
              onClick={handleDismiss}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </AlertDescription>
      </div>
    </Alert>
  );
}
