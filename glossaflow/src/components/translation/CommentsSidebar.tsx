'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  MessageSquare, 
  Send, 
  MoreHorizontal,
  Reply,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface Comment {
  id: string;
  author: {
    name: string;
    avatar?: string;
    role: string;
  };
  content: string;
  type: 'comment' | 'issue' | 'suggestion';
  status?: 'open' | 'resolved';
  createdAt: string;
  replies?: Comment[];
}

interface CommentsSidebarProps {
  segmentId: string;
  comments: Comment[];
  onAddComment: (comment: { content: string; type: string }) => void;
}

// Mock comments data
const mockComments: Comment[] = [
  {
    id: '1',
    author: {
      name: '<PERSON>',
      avatar: '',
      role: 'Translator',
    },
    content: 'I think "モバイルアプリケーション" might be too formal here. Should we consider using "アプリ" instead?',
    type: 'suggestion',
    status: 'open',
    createdAt: '2024-01-15T10:30:00Z',
    replies: [
      {
        id: '1-1',
        author: {
          name: 'Mike Johnson',
          avatar: '',
          role: 'Reviewer',
        },
        content: 'Good point. Let\'s use "アプリ" for consistency with the rest of the project.',
        type: 'comment',
        createdAt: '2024-01-15T11:15:00Z',
      },
    ],
  },
  {
    id: '2',
    author: {
      name: 'Emma Davis',
      avatar: '',
      role: 'Project Manager',
    },
    content: 'Please ensure this matches the terminology we agreed on in the style guide.',
    type: 'issue',
    status: 'resolved',
    createdAt: '2024-01-15T09:45:00Z',
  },
];

const commentTypeColors = {
  comment: 'bg-blue-100 text-blue-800',
  issue: 'bg-red-100 text-red-800',
  suggestion: 'bg-yellow-100 text-yellow-800',
};

const commentTypeIcons = {
  comment: MessageSquare,
  issue: AlertCircle,
  suggestion: CheckCircle,
};

export function CommentsSidebar({ segmentId, comments = mockComments, onAddComment }: CommentsSidebarProps) {
  const [newComment, setNewComment] = useState('');
  const [commentType, setCommentType] = useState<'comment' | 'issue' | 'suggestion'>('comment');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  const handleSubmit = () => {
    if (newComment.trim()) {
      onAddComment({
        content: newComment,
        type: commentType,
      });
      setNewComment('');
      setReplyingTo(null);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="p-4 border-b">
        <h3 className="font-medium flex items-center">
          <MessageSquare className="mr-2 h-4 w-4" />
          Comments ({comments.length})
        </h3>
      </div>

      <div className="flex-1 overflow-y-auto">
        {comments.length === 0 ? (
          <div className="p-4 text-center">
            <MessageSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No comments yet</p>
            <p className="text-xs text-gray-400">Start a discussion about this segment</p>
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {comments.map((comment) => {
              const TypeIcon = commentTypeIcons[comment.type];
              return (
                <div key={comment.id} className="space-y-3">
                  <div className="border rounded-lg p-3">
                    <div className="flex items-start space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={comment.author.avatar} />
                        <AvatarFallback>
                          {comment.author.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium">{comment.author.name}</span>
                          <Badge variant="outline" size="sm">
                            {comment.author.role}
                          </Badge>
                          <Badge 
                            size="sm" 
                            className={commentTypeColors[comment.type]}
                          >
                            <TypeIcon className="mr-1 h-3 w-3" />
                            {comment.type}
                          </Badge>
                          {comment.status && (
                            <Badge 
                              variant={comment.status === 'resolved' ? 'default' : 'secondary'}
                              size="sm"
                            >
                              {comment.status}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 mb-2">{comment.content}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {formatTime(comment.createdAt)}
                          </span>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setReplyingTo(comment.id)}
                            >
                              <Reply className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="ml-6 space-y-2">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="border rounded-lg p-3 bg-gray-50">
                          <div className="flex items-start space-x-3">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={reply.author.avatar} />
                              <AvatarFallback className="text-xs">
                                {reply.author.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="text-sm font-medium">{reply.author.name}</span>
                                <Badge variant="outline" size="sm">
                                  {reply.author.role}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-700 mb-1">{reply.content}</p>
                              <span className="text-xs text-gray-500">
                                {formatTime(reply.createdAt)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Reply Form */}
                  {replyingTo === comment.id && (
                    <div className="ml-6">
                      <div className="border rounded-lg p-3 bg-gray-50">
                        <Textarea
                          placeholder="Write a reply..."
                          value={newComment}
                          onChange={(e) => setNewComment(e.target.value)}
                          className="mb-2"
                          rows={2}
                        />
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setReplyingTo(null)}
                          >
                            Cancel
                          </Button>
                          <Button size="sm" onClick={handleSubmit}>
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Add Comment Form */}
      <div className="p-4 border-t bg-gray-50">
        <div className="space-y-3">
          <div className="flex space-x-1">
            {(['comment', 'issue', 'suggestion'] as const).map((type) => {
              const Icon = commentTypeIcons[type];
              return (
                <Button
                  key={type}
                  variant={commentType === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setCommentType(type)}
                >
                  <Icon className="mr-1 h-3 w-3" />
                  {type}
                </Button>
              );
            })}
          </div>
          
          <Textarea
            placeholder={`Add a ${commentType}...`}
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            rows={3}
          />
          
          <Button 
            onClick={handleSubmit} 
            disabled={!newComment.trim()}
            className="w-full"
          >
            <Send className="mr-2 h-4 w-4" />
            Add {commentType}
          </Button>
        </div>
      </div>
    </div>
  );
}
