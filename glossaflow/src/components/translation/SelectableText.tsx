'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Flag, 
  Copy,
  X
} from 'lucide-react';

interface SelectableTextProps {
  text: string;
  onTextSelect?: (selectedText: string, startIndex: number, endIndex: number) => void;
  onQuickAdd?: (selectedText: string) => void;
  onMarkForReview?: (selectedText: string) => void;
  className?: string;
  children?: React.ReactNode;
}

interface Selection {
  text: string;
  startIndex: number;
  endIndex: number;
  rect: DOMRect;
}

export function SelectableText({
  text,
  onTextSelect,
  onQuickAdd,
  onMarkForReview,
  className = '',
  children,
}: SelectableTextProps) {
  const [selection, setSelection] = useState<Selection | null>(null);
  const [showPopover, setShowPopover] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleSelectionChange = () => {
      const windowSelection = window.getSelection();
      if (!windowSelection || windowSelection.rangeCount === 0) {
        setSelection(null);
        setShowPopover(false);
        return;
      }

      const range = windowSelection.getRangeAt(0);
      const selectedText = range.toString().trim();

      if (!selectedText || selectedText.length < 2) {
        setSelection(null);
        setShowPopover(false);
        return;
      }

      // Check if selection is within our text element
      if (!textRef.current || !textRef.current.contains(range.commonAncestorContainer)) {
        setSelection(null);
        setShowPopover(false);
        return;
      }

      // Calculate position relative to the text element
      const textElement = textRef.current;
      const textContent = textElement.textContent || '';
      const startIndex = textContent.indexOf(selectedText);
      const endIndex = startIndex + selectedText.length;

      if (startIndex === -1) {
        setSelection(null);
        setShowPopover(false);
        return;
      }

      const rect = range.getBoundingClientRect();
      
      const newSelection: Selection = {
        text: selectedText,
        startIndex,
        endIndex,
        rect,
      };

      setSelection(newSelection);
      setShowPopover(true);

      if (onTextSelect) {
        onTextSelect(selectedText, startIndex, endIndex);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current && 
        !popoverRef.current.contains(event.target as Node) &&
        textRef.current &&
        !textRef.current.contains(event.target as Node)
      ) {
        setSelection(null);
        setShowPopover(false);
        window.getSelection()?.removeAllRanges();
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onTextSelect]);

  const handleQuickAdd = () => {
    if (selection && onQuickAdd) {
      onQuickAdd(selection.text);
      setShowPopover(false);
      window.getSelection()?.removeAllRanges();
    }
  };

  const handleMarkForReview = () => {
    if (selection && onMarkForReview) {
      onMarkForReview(selection.text);
      setShowPopover(false);
      window.getSelection()?.removeAllRanges();
    }
  };

  const handleCopy = () => {
    if (selection) {
      navigator.clipboard.writeText(selection.text);
      setShowPopover(false);
      window.getSelection()?.removeAllRanges();
    }
  };

  const handleClose = () => {
    setSelection(null);
    setShowPopover(false);
    window.getSelection()?.removeAllRanges();
  };

  return (
    <div className={`relative ${className}`}>
      <div
        ref={textRef}
        className="select-text cursor-text"
        style={{ userSelect: 'text' }}
      >
        {children || (
          <p className="text-gray-900 leading-relaxed whitespace-pre-wrap">
            {text}
          </p>
        )}
      </div>

      {/* Selection Popover */}
      {showPopover && selection && (
        <div
          ref={popoverRef}
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-2"
          style={{
            top: selection.rect.bottom + window.scrollY + 8,
            left: Math.max(
              8,
              Math.min(
                selection.rect.left + window.scrollX,
                window.innerWidth - 280
              )
            ),
            maxWidth: '280px',
          }}
        >
          <Card className="border-0 shadow-none">
            <CardContent className="p-3">
              <div className="space-y-3">
                {/* Selected Text Display */}
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-xs max-w-[200px] truncate">
                    "{selection.text}"
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={handleClose}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>

                {/* Quick Actions */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleQuickAdd}
                    className="h-7 px-2 text-xs"
                  >
                    <Plus className="mr-1 h-3 w-3" />
                    Add Term
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkForReview}
                    className="h-7 px-2 text-xs"
                  >
                    <Flag className="mr-1 h-3 w-3" />
                    Review
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopy}
                    className="h-7 px-2 text-xs"
                  >
                    <Copy className="mr-1 h-3 w-3" />
                    Copy
                  </Button>
                </div>

                <div className="text-xs text-gray-500">
                  Select text to add terminology or mark for review
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
