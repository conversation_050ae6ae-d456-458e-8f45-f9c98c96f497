'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useGetTranslationSuggestionMutation,
  aiTranslationUtils,
  type TranslationSuggestion,
} from '@/lib/api/ai-translation';
import {
  Sparkles,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Lightbulb,
} from 'lucide-react';

interface AITranslationSuggestionsProps {
  sourceText: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  domain?: string;
  projectId?: string;
  onSuggestionSelect: (suggestion: string) => void;
  onSuggestionApply?: (suggestion: TranslationSuggestion) => void;
}

export function AITranslationSuggestions({
  sourceText,
  sourceLanguage,
  targetLanguage,
  context,
  domain,
  projectId,
  onSuggestionSelect,
  onSuggestionApply,
}: AITranslationSuggestionsProps) {
  const [suggestion, setSuggestion] = useState<TranslationSuggestion | null>(null);
  const [showAlternatives, setShowAlternatives] = useState(false);
  const [lastSourceText, setLastSourceText] = useState('');

  const [getTranslationSuggestion, { 
    isLoading, 
    error, 
    isError 
  }] = useGetTranslationSuggestionMutation();

  // Auto-fetch suggestions when source text changes
  useEffect(() => {
    if (sourceText && sourceText.trim().length > 0 && sourceText !== lastSourceText) {
      handleGetSuggestion();
      setLastSourceText(sourceText);
    }
  }, [sourceText, sourceLanguage, targetLanguage]);

  const handleGetSuggestion = async () => {
    if (!sourceText.trim()) return;

    try {
      const result = await getTranslationSuggestion({
        sourceText: sourceText.trim(),
        sourceLanguage,
        targetLanguage,
        context,
        domain,
        projectId,
      }).unwrap();

      if (result.success && aiTranslationUtils.isValidSuggestion(result.data)) {
        setSuggestion(result.data);
        setShowAlternatives(false);
      }
    } catch (error) {
      console.error('Failed to get AI suggestion:', error);
    }
  };

  const handleApplySuggestion = (text: string) => {
    onSuggestionSelect(text);
    if (suggestion && onSuggestionApply) {
      onSuggestionApply({
        ...suggestion,
        targetText: text,
      });
    }
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  if (!sourceText.trim()) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-gray-500">
          <Sparkles className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p>Enter source text to get AI translation suggestions</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm font-medium">
            <Sparkles className="h-4 w-4 text-blue-600" />
            AI Translation Suggestions
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={handleGetSuggestion}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Loading State */}
        {isLoading && (
          <div className="space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-8 w-1/2" />
          </div>
        )}

        {/* Error State */}
        {isError && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to get AI suggestions. Please try again or translate manually.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Suggestion */}
        {suggestion && !isLoading && (
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Badge className={getConfidenceColor(suggestion.confidence)}>
                    {aiTranslationUtils.formatConfidence(suggestion.confidence)} confidence
                  </Badge>
                  {suggestion.confidence >= 0.8 && (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyToClipboard(suggestion.targetText)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <p className="text-gray-900 mb-3 font-medium">
                {suggestion.targetText}
              </p>
              
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => handleApplySuggestion(suggestion.targetText)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Apply
                </Button>
                
                {suggestion.alternatives && suggestion.alternatives.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAlternatives(!showAlternatives)}
                  >
                    {showAlternatives ? 'Hide' : 'Show'} Alternatives ({suggestion.alternatives.length})
                  </Button>
                )}
              </div>
            </div>

            {/* Explanation */}
            {suggestion.explanation && (
              <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
                <div className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-amber-800">{suggestion.explanation}</p>
                </div>
              </div>
            )}

            {/* Alternative Suggestions */}
            {showAlternatives && suggestion.alternatives && suggestion.alternatives.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Alternative Translations:</h4>
                {suggestion.alternatives.map((alternative, index) => (
                  <div
                    key={index}
                    className="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <p className="text-gray-900">{alternative}</p>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyToClipboard(alternative)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApplySuggestion(alternative)}
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Terminology Matches */}
            {suggestion.terminology && suggestion.terminology.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Key Terminology:</h4>
                <div className="flex flex-wrap gap-2">
                  {suggestion.terminology.map((term, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs"
                      title={term.definition}
                    >
                      {term.sourceTerm} → {term.targetTerm}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
