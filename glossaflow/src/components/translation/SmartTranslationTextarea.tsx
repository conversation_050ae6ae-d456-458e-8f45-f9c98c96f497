'use client';

import { useState, useRef, useEffect, forwardRef } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { TerminologyAutocomplete } from './TerminologyAutocomplete';

interface SmartTranslationTextareaProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  className?: string;
  projectId?: string;
  targetLanguage?: string;
  disabled?: boolean;
}

export const SmartTranslationTextarea = forwardRef<
  HTMLTextAreaElement,
  SmartTranslationTextareaProps
>(({
  value,
  onChange,
  onKeyDown,
  placeholder,
  className,
  projectId,
  targetLanguage,
  disabled,
  ...props
}, ref) => {
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [autocompletePosition, setAutocompletePosition] = useState({ top: 0, left: 0 });
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Merge refs
  useEffect(() => {
    if (ref) {
      if (typeof ref === 'function') {
        ref(textareaRef.current);
      } else {
        ref.current = textareaRef.current;
      }
    }
  }, [ref]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const newCursorPosition = e.target.selectionStart;
    
    onChange(newValue);
    setCursorPosition(newCursorPosition);

    // Show autocomplete if we have a project ID and are typing
    if (projectId && newValue.length > 0) {
      updateAutocompletePosition(newCursorPosition);
      setShowAutocomplete(true);
    } else {
      setShowAutocomplete(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Update cursor position on key navigation
    setTimeout(() => {
      if (textareaRef.current) {
        setCursorPosition(textareaRef.current.selectionStart);
      }
    }, 0);

    // Hide autocomplete on certain keys
    if (['Escape', 'Enter'].includes(e.key) && !e.shiftKey) {
      setShowAutocomplete(false);
    }

    onKeyDown?.(e);
  };

  const handleClick = (e: React.MouseEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    setCursorPosition(target.selectionStart);
    
    if (projectId && value.length > 0) {
      updateAutocompletePosition(target.selectionStart);
      setShowAutocomplete(true);
    }
  };

  const updateAutocompletePosition = (cursorPos: number) => {
    if (!textareaRef.current || !containerRef.current) return;

    const textarea = textareaRef.current;
    const container = containerRef.current;
    
    // Create a temporary div to measure text
    const tempDiv = document.createElement('div');
    const computedStyle = window.getComputedStyle(textarea);
    
    // Copy textarea styles to temp div
    tempDiv.style.position = 'absolute';
    tempDiv.style.visibility = 'hidden';
    tempDiv.style.whiteSpace = 'pre-wrap';
    tempDiv.style.wordWrap = 'break-word';
    tempDiv.style.font = computedStyle.font;
    tempDiv.style.padding = computedStyle.padding;
    tempDiv.style.border = computedStyle.border;
    tempDiv.style.width = computedStyle.width;
    tempDiv.style.lineHeight = computedStyle.lineHeight;
    
    document.body.appendChild(tempDiv);
    
    // Get text up to cursor position
    const textBeforeCursor = value.substring(0, cursorPos);
    tempDiv.textContent = textBeforeCursor;
    
    // Add a span to measure the exact cursor position
    const span = document.createElement('span');
    span.textContent = '|';
    tempDiv.appendChild(span);
    
    const spanRect = span.getBoundingClientRect();
    const textareaRect = textarea.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    
    // Calculate position relative to container
    const top = spanRect.top - containerRect.top + 20; // 20px below cursor
    const left = spanRect.left - containerRect.left;
    
    setAutocompletePosition({ top, left });
    
    document.body.removeChild(tempDiv);
  };

  const handleSuggestionSelect = (suggestion: string, startPos: number, endPos: number) => {
    const newValue = value.substring(0, startPos) + suggestion + value.substring(endPos);
    onChange(newValue);
    setShowAutocomplete(false);
    
    // Focus back to textarea and set cursor position
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        const newCursorPos = startPos + suggestion.length;
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
        setCursorPosition(newCursorPos);
      }
    }, 0);
  };

  const handleCloseAutocomplete = () => {
    setShowAutocomplete(false);
  };

  // Hide autocomplete when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowAutocomplete(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Hide autocomplete when textarea loses focus (with delay for autocomplete clicks)
  const handleBlur = () => {
    setTimeout(() => {
      setShowAutocomplete(false);
    }, 150);
  };

  return (
    <div ref={containerRef} className="relative">
      <Textarea
        ref={textareaRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onClick={handleClick}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={className}
        disabled={disabled}
        {...props}
      />
      
      {showAutocomplete && projectId && (
        <div
          style={{
            position: 'absolute',
            top: autocompletePosition.top,
            left: autocompletePosition.left,
            zIndex: 50,
          }}
        >
          <TerminologyAutocomplete
            projectId={projectId}
            targetLanguage={targetLanguage}
            inputValue={value}
            cursorPosition={cursorPosition}
            onSuggestionSelect={handleSuggestionSelect}
            onClose={handleCloseAutocomplete}
          />
        </div>
      )}
    </div>
  );
});

SmartTranslationTextarea.displayName = 'SmartTranslationTextarea';
