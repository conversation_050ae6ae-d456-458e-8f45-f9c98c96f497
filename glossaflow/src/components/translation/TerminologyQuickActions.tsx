'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Plus, 
  Edit, 
  Flag, 
  Save, 
  X, 
  Lightbulb,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { useCreateTerminologyEntryMutation, useUpdateTerminologyEntryMutation } from '@/lib/api/terminology';

interface TerminologyQuickActionsProps {
  projectId: string;
  selectedText?: string;
  suggestedTranslation?: string;
  targetLanguage: string;
  onTermAdded?: (term: any) => void;
  onTermUpdated?: (term: any) => void;
  className?: string;
}

interface NewTermData {
  sourceTerm: string;
  targetTerm: string;
  category: string;
  context: string;
  usageNotes: string;
}

export function TerminologyQuickActions({
  projectId,
  selectedText = '',
  suggestedTranslation = '',
  targetLanguage,
  onTermAdded,
  onTermUpdated,
  className = '',
}: TerminologyQuickActionsProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newTermData, setNewTermData] = useState<NewTermData>({
    sourceTerm: selectedText,
    targetTerm: suggestedTranslation,
    category: 'general',
    context: '',
    usageNotes: '',
  });

  const [createTerminology, { isLoading: isCreating }] = useCreateTerminologyEntryMutation();
  const [updateTerminology, { isLoading: isUpdating }] = useUpdateTerminologyEntryMutation();

  const handleAddTerm = async () => {
    if (!newTermData.sourceTerm || !newTermData.targetTerm) {
      return;
    }

    try {
      const result = await createTerminology({
        source_term: newTermData.sourceTerm,
        target_term: newTermData.targetTerm,
        target_language: targetLanguage,
        category: newTermData.category,
        context: newTermData.context || undefined,
        usage_notes: newTermData.usageNotes || undefined,
        project_id: projectId,
        approval_status: 'pending',
      }).unwrap();

      if (result.success && onTermAdded) {
        onTermAdded(result.data);
      }

      setIsAddDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Failed to add terminology:', error);
    }
  };

  const handleMarkForReview = async () => {
    if (!selectedText) return;

    try {
      const result = await createTerminology({
        source_term: selectedText,
        target_term: suggestedTranslation || '[NEEDS TRANSLATION]',
        target_language: targetLanguage,
        category: 'general',
        context: 'Marked for review during translation',
        usage_notes: 'Requires terminology review and approval',
        project_id: projectId,
        approval_status: 'pending',
      }).unwrap();

      if (result.success && onTermAdded) {
        onTermAdded(result.data);
      }
    } catch (error) {
      console.error('Failed to mark term for review:', error);
    }
  };

  const resetForm = () => {
    setNewTermData({
      sourceTerm: selectedText,
      targetTerm: suggestedTranslation,
      category: 'general',
      context: '',
      usageNotes: '',
    });
  };

  const categories = [
    { value: 'character', label: 'Character' },
    { value: 'location', label: 'Location' },
    { value: 'concept', label: 'Concept' },
    { value: 'technical', label: 'Technical' },
    { value: 'general', label: 'General' },
  ];

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Add New Term */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setNewTermData(prev => ({
                ...prev,
                sourceTerm: selectedText,
                targetTerm: suggestedTranslation,
              }));
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Term
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Terminology</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="sourceTerm">Source Term</Label>
              <Input
                id="sourceTerm"
                value={newTermData.sourceTerm}
                onChange={(e) => setNewTermData(prev => ({ ...prev, sourceTerm: e.target.value }))}
                placeholder="Enter source term..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetTerm">Target Term ({targetLanguage})</Label>
              <Input
                id="targetTerm"
                value={newTermData.targetTerm}
                onChange={(e) => setNewTermData(prev => ({ ...prev, targetTerm: e.target.value }))}
                placeholder="Enter translation..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={newTermData.category}
                onValueChange={(value) => setNewTermData(prev => ({ ...prev, category: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="context">Context (Optional)</Label>
              <Input
                id="context"
                value={newTermData.context}
                onChange={(e) => setNewTermData(prev => ({ ...prev, context: e.target.value }))}
                placeholder="Where this term is used..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="usageNotes">Usage Notes (Optional)</Label>
              <Textarea
                id="usageNotes"
                value={newTermData.usageNotes}
                onChange={(e) => setNewTermData(prev => ({ ...prev, usageNotes: e.target.value }))}
                placeholder="Special usage instructions..."
                rows={3}
              />
            </div>

            <Alert>
              <Lightbulb className="h-4 w-4" />
              <AlertDescription>
                New terms will be marked as "pending" and require approval before being used in translations.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddTerm}
                disabled={isCreating || !newTermData.sourceTerm || !newTermData.targetTerm}
              >
                {isCreating ? (
                  <>
                    <Save className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Add Term
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Mark for Review */}
      {selectedText && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleMarkForReview}
        >
          <Flag className="mr-2 h-4 w-4" />
          Mark for Review
        </Button>
      )}

      {/* Quick Add from Selection */}
      {selectedText && suggestedTranslation && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setNewTermData({
              sourceTerm: selectedText,
              targetTerm: suggestedTranslation,
              category: 'general',
              context: '',
              usageNotes: '',
            });
            setIsAddDialogOpen(true);
          }}
        >
          <CheckCircle className="mr-2 h-4 w-4" />
          Quick Add
        </Button>
      )}

      {/* Status Indicators */}
      <div className="flex items-center space-x-1">
        <Badge variant="outline" className="text-xs">
          {targetLanguage}
        </Badge>
        {selectedText && (
          <Badge variant="secondary" className="text-xs">
            Selected: {selectedText.slice(0, 20)}{selectedText.length > 20 ? '...' : ''}
          </Badge>
        )}
      </div>
    </div>
  );
}
