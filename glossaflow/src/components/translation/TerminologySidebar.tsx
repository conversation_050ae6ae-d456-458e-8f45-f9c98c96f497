'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Plus,
  BookOpen,
  Filter,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';

interface TerminologySidebarProps {
  projectId: string;
  sourceText: string;
  targetLanguage?: string;
  onTermSelect: (term: any) => void;
  onAddTerm?: () => void;
}

// Category colors mapping
const categoryColors = {
  technical: 'bg-blue-100 text-blue-800',
  general: 'bg-gray-100 text-gray-800',
  character: 'bg-purple-100 text-purple-800',
  location: 'bg-green-100 text-green-800',
  concept: 'bg-orange-100 text-orange-800',
};

export function TerminologySidebar({
  projectId,
  sourceText,
  targetLanguage,
  onTermSelect,
  onAddTerm
}: TerminologySidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [debouncedSearch, setDebouncedSearch] = useState('');

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch project terminology with real-time filtering
  const {
    data: terminologyResponse,
    isLoading,
    error,
    refetch
  } = useGetTerminologyForProjectQuery({
    projectId,
    sourceText: sourceText || undefined,
    targetLanguage: targetLanguage || undefined,
    category: selectedCategory !== 'all' ? selectedCategory : undefined,
    search: debouncedSearch || undefined,
    approvalStatus: 'approved', // Only show approved terms
  });

  const terminology = terminologyResponse?.data || [];
  const highlightedTerms = terminology.filter(term => term.highlighted);
  const otherTerms = terminology.filter(term => !term.highlighted);

  // Handle loading and error states
  if (error) {
    return (
      <div className="h-full flex flex-col bg-white">
        <div className="p-4 border-b">
          <h3 className="font-medium flex items-center">
            <BookOpen className="mr-2 h-4 w-4" />
            Terminology
          </h3>
        </div>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
            <p className="text-sm text-red-600">Failed to load terminology</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="mt-2"
            >
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium flex items-center">
            <BookOpen className="mr-2 h-4 w-4" />
            Terminology
            {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
          </h3>
          <Button
            size="sm"
            variant="outline"
            onClick={onAddTerm}
            disabled={isLoading}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search terms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-1">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
            disabled={isLoading}
          >
            All
          </Button>
          <Button
            variant={selectedCategory === 'character' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('character')}
            disabled={isLoading}
          >
            Character
          </Button>
          <Button
            variant={selectedCategory === 'location' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('location')}
            disabled={isLoading}
          >
            Location
          </Button>
          <Button
            variant={selectedCategory === 'concept' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('concept')}
            disabled={isLoading}
          >
            Concept
          </Button>
          <Button
            variant={selectedCategory === 'technical' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('technical')}
            disabled={isLoading}
          >
            Technical
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Highlighted Terms */}
        {highlightedTerms.length > 0 && (
          <div className="p-4 border-b bg-yellow-50">
            <h4 className="text-sm font-medium text-yellow-800 mb-3">
              Found in Current Segment
            </h4>
            <div className="space-y-2">
              {highlightedTerms.map((term) => (
                <div
                  key={term.id}
                  className="p-3 bg-white border border-yellow-200 rounded-lg cursor-pointer hover:border-yellow-300 transition-colors"
                  onClick={() => onTermSelect(term)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{term.sourceTerm}</div>
                      <div className="text-sm text-gray-600">{term.targetTerm}</div>
                    </div>
                    <Badge 
                      size="sm" 
                      className={categoryColors[term.category as keyof typeof categoryColors]}
                    >
                      {term.category}
                    </Badge>
                  </div>
                  {term.context && (
                    <div className="text-xs text-gray-500">{term.context}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* All Terms */}
        <div className="p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            All Terms ({otherTerms.length})
          </h4>
          <div className="space-y-2">
            {otherTerms.map((term) => (
              <div
                key={term.id}
                className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onTermSelect(term)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{term.sourceTerm}</div>
                    <div className="text-sm text-gray-600">{term.targetTerm}</div>
                  </div>
                  <Badge 
                    size="sm" 
                    className={categoryColors[term.category as keyof typeof categoryColors]}
                  >
                    {term.category}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  {term.context && (
                    <div className="text-xs text-gray-500">{term.context}</div>
                  )}
                  <div className="text-xs text-gray-400">
                    Used {term.frequency} times
                  </div>
                </div>
              </div>
            ))}
          </div>

          {terminology.length === 0 && !isLoading && (
            <div className="text-center py-8">
              <BookOpen className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">
                {debouncedSearch || selectedCategory !== 'all'
                  ? 'No terms found'
                  : 'No terminology entries yet'
                }
              </p>
              <p className="text-xs text-gray-400">
                {debouncedSearch || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Add terminology entries to get started'
                }
              </p>
            </div>
          )}

          {isLoading && (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
              <p className="text-sm text-gray-500">Loading terminology...</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-t bg-gray-50">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={onAddTerm}
          disabled={isLoading}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add New Term
        </Button>

        {terminologyResponse?.meta && (
          <div className="mt-2 text-xs text-gray-500 text-center">
            {terminologyResponse.meta.totalEntries} total terms
            {terminologyResponse.meta.highlightedEntries > 0 && (
              <span className="text-yellow-600">
                {' '}• {terminologyResponse.meta.highlightedEntries} highlighted
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
