'use client';

import { useState, useEffect, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';
import { Check, Copy, ExternalLink } from 'lucide-react';

interface TerminologyHighlighterProps {
  text: string;
  projectId: string;
  targetLanguage?: string;
  onTermClick?: (term: any) => void;
  onApplyTerm?: (sourceTerm: string, targetTerm: string) => void;
  className?: string;
}

interface HighlightedSegment {
  text: string;
  isTerminology: boolean;
  term?: any;
  startIndex: number;
  endIndex: number;
}

export function TerminologyHighlighter({
  text,
  projectId,
  targetLanguage,
  onTermClick,
  onApplyTerm,
  className = '',
}: TerminologyHighlighterProps) {
  const [appliedTerms, setAppliedTerms] = useState<Set<string>>(new Set());

  // Fetch project terminology
  const {
    data: terminologyResponse,
    isLoading,
  } = useGetTerminologyForProjectQuery({
    projectId,
    sourceText: text,
    targetLanguage,
    approvalStatus: 'approved',
  });

  const terminology = terminologyResponse?.data || [];

  // Create highlighted segments
  const highlightedSegments = useMemo(() => {
    if (!text || terminology.length === 0) {
      return [{ text, isTerminology: false, startIndex: 0, endIndex: text.length }];
    }

    const segments: HighlightedSegment[] = [];
    let currentIndex = 0;
    const textLower = text.toLowerCase();

    // Sort terminology by length (longest first) to avoid partial matches
    const sortedTerminology = [...terminology].sort((a, b) => 
      b.sourceTerm.length - a.sourceTerm.length
    );

    // Track which parts of the text have been matched
    const matchedRanges: Array<{ start: number; end: number; term: any }> = [];

    // Find all terminology matches
    sortedTerminology.forEach(term => {
      const termLower = term.sourceTerm.toLowerCase();
      let searchIndex = 0;

      while (searchIndex < text.length) {
        const matchIndex = textLower.indexOf(termLower, searchIndex);
        if (matchIndex === -1) break;

        const matchEnd = matchIndex + term.sourceTerm.length;

        // Check if this range overlaps with existing matches
        const hasOverlap = matchedRanges.some(range => 
          (matchIndex < range.end && matchEnd > range.start)
        );

        if (!hasOverlap) {
          // Check word boundaries to avoid partial matches
          const beforeChar = matchIndex > 0 ? text[matchIndex - 1] : ' ';
          const afterChar = matchEnd < text.length ? text[matchEnd] : ' ';
          const isWordBoundary = /\W/.test(beforeChar) && /\W/.test(afterChar);

          if (isWordBoundary || matchIndex === 0 || matchEnd === text.length) {
            matchedRanges.push({
              start: matchIndex,
              end: matchEnd,
              term
            });
          }
        }

        searchIndex = matchIndex + 1;
      }
    });

    // Sort matches by start position
    matchedRanges.sort((a, b) => a.start - b.start);

    // Create segments
    matchedRanges.forEach(match => {
      // Add text before the match
      if (currentIndex < match.start) {
        segments.push({
          text: text.slice(currentIndex, match.start),
          isTerminology: false,
          startIndex: currentIndex,
          endIndex: match.start
        });
      }

      // Add the terminology match
      segments.push({
        text: text.slice(match.start, match.end),
        isTerminology: true,
        term: match.term,
        startIndex: match.start,
        endIndex: match.end
      });

      currentIndex = match.end;
    });

    // Add remaining text
    if (currentIndex < text.length) {
      segments.push({
        text: text.slice(currentIndex),
        isTerminology: false,
        startIndex: currentIndex,
        endIndex: text.length
      });
    }

    return segments.length > 0 ? segments : [{ 
      text, 
      isTerminology: false, 
      startIndex: 0, 
      endIndex: text.length 
    }];
  }, [text, terminology]);

  const handleApplyTerm = (term: any) => {
    if (onApplyTerm) {
      onApplyTerm(term.sourceTerm, term.targetTerm);
      setAppliedTerms(prev => new Set([...prev, term.sourceTerm]));
    }
  };

  const handleCopyTerm = (targetTerm: string) => {
    navigator.clipboard.writeText(targetTerm);
  };

  if (isLoading) {
    return (
      <div className={`p-4 bg-gray-50 rounded-lg ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`p-4 bg-gray-50 rounded-lg leading-relaxed ${className}`}>
        {highlightedSegments.map((segment, index) => {
          if (!segment.isTerminology) {
            return (
              <span key={index} className="text-gray-900">
                {segment.text}
              </span>
            );
          }

          const isApplied = appliedTerms.has(segment.term.sourceTerm);
          const categoryColors = {
            character: 'bg-purple-100 text-purple-800 border-purple-200',
            location: 'bg-green-100 text-green-800 border-green-200',
            concept: 'bg-orange-100 text-orange-800 border-orange-200',
            technical: 'bg-blue-100 text-blue-800 border-blue-200',
            general: 'bg-gray-100 text-gray-800 border-gray-200',
          };

          const colorClass = categoryColors[segment.term.category as keyof typeof categoryColors] || 
                            categoryColors.general;

          return (
            <Tooltip key={index}>
              <TooltipTrigger asChild>
                <span
                  className={`
                    inline-block px-2 py-1 rounded-md border-2 cursor-pointer
                    transition-all duration-200 hover:shadow-md
                    ${colorClass}
                    ${isApplied ? 'ring-2 ring-green-400' : ''}
                  `}
                  onClick={() => onTermClick?.(segment.term)}
                >
                  {segment.text}
                  {isApplied && (
                    <Check className="inline ml-1 h-3 w-3 text-green-600" />
                  )}
                </span>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-sm">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {segment.term.category}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {segment.term.targetLanguage}
                    </Badge>
                  </div>
                  
                  <div>
                    <p className="font-medium text-sm">{segment.term.sourceTerm}</p>
                    <p className="text-sm text-gray-600">→ {segment.term.targetTerm}</p>
                  </div>

                  {segment.term.context && (
                    <p className="text-xs text-gray-500">{segment.term.context}</p>
                  )}

                  {segment.term.usageNotes && (
                    <p className="text-xs text-blue-600">{segment.term.usageNotes}</p>
                  )}

                  <div className="flex items-center space-x-2 pt-2 border-t">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleApplyTerm(segment.term);
                      }}
                      disabled={isApplied}
                      className="h-6 px-2 text-xs"
                    >
                      {isApplied ? (
                        <>
                          <Check className="mr-1 h-3 w-3" />
                          Applied
                        </>
                      ) : (
                        'Apply Term'
                      )}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCopyTerm(segment.term.targetTerm);
                      }}
                      className="h-6 px-2 text-xs"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>

                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        onTermClick?.(segment.term);
                      }}
                      className="h-6 px-2 text-xs"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>

                  <div className="text-xs text-gray-400">
                    Used {segment.term.frequency} times
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </div>
    </TooltipProvider>
  );
}
