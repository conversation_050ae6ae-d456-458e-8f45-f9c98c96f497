'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';
import { Check, ArrowRight, Zap } from 'lucide-react';

interface TerminologyAutocompleteProps {
  projectId: string;
  targetLanguage?: string;
  inputValue: string;
  cursorPosition: number;
  onSuggestionSelect: (suggestion: string, startPos: number, endPos: number) => void;
  onClose: () => void;
  className?: string;
}

interface AutocompleteSuggestion {
  term: any;
  matchType: 'exact' | 'fuzzy' | 'context';
  confidence: number;
  replacementStart: number;
  replacementEnd: number;
  preview: string;
}

// Simple fuzzy matching function
function fuzzyMatch(pattern: string, text: string): { score: number; matches: number[] } {
  const patternLower = pattern.toLowerCase();
  const textLower = text.toLowerCase();
  
  if (textLower.includes(patternLower)) {
    return { score: 0.9, matches: [] };
  }

  let score = 0;
  let patternIndex = 0;
  const matches: number[] = [];

  for (let i = 0; i < textLower.length && patternIndex < patternLower.length; i++) {
    if (textLower[i] === patternLower[patternIndex]) {
      matches.push(i);
      score += 1 / textLower.length;
      patternIndex++;
    }
  }

  return patternIndex === patternLower.length ? { score, matches } : { score: 0, matches: [] };
}

export function TerminologyAutocomplete({
  projectId,
  targetLanguage,
  inputValue,
  cursorPosition,
  onSuggestionSelect,
  onClose,
  className = '',
}: TerminologyAutocompleteProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Fetch project terminology
  const {
    data: terminologyResponse,
    isLoading,
  } = useGetTerminologyForProjectQuery({
    projectId,
    targetLanguage,
    approvalStatus: 'approved',
  });

  const terminology = terminologyResponse?.data || [];

  // Extract current word being typed
  const currentWord = useMemo(() => {
    const beforeCursor = inputValue.slice(0, cursorPosition);
    const afterCursor = inputValue.slice(cursorPosition);
    
    // Find word boundaries
    const wordStart = Math.max(
      beforeCursor.lastIndexOf(' '),
      beforeCursor.lastIndexOf('\n'),
      beforeCursor.lastIndexOf('\t')
    ) + 1;
    
    const wordEndInAfter = Math.min(
      afterCursor.indexOf(' ') === -1 ? afterCursor.length : afterCursor.indexOf(' '),
      afterCursor.indexOf('\n') === -1 ? afterCursor.length : afterCursor.indexOf('\n'),
      afterCursor.indexOf('\t') === -1 ? afterCursor.length : afterCursor.indexOf('\t')
    );
    
    const wordEnd = cursorPosition + wordEndInAfter;
    
    return {
      text: inputValue.slice(wordStart, wordEnd),
      start: wordStart,
      end: wordEnd
    };
  }, [inputValue, cursorPosition]);

  // Generate suggestions
  const suggestions = useMemo(() => {
    if (!currentWord.text || currentWord.text.length < 2 || terminology.length === 0) {
      return [];
    }

    const results: AutocompleteSuggestion[] = [];

    terminology.forEach(term => {
      // Check source term matches
      const sourceFuzzy = fuzzyMatch(currentWord.text, term.sourceTerm);
      if (sourceFuzzy.score > 0.3) {
        results.push({
          term,
          matchType: sourceFuzzy.score > 0.8 ? 'exact' : 'fuzzy',
          confidence: sourceFuzzy.score,
          replacementStart: currentWord.start,
          replacementEnd: currentWord.end,
          preview: term.targetTerm
        });
      }

      // Check target term matches (for reverse lookup)
      const targetFuzzy = fuzzyMatch(currentWord.text, term.targetTerm);
      if (targetFuzzy.score > 0.3) {
        results.push({
          term,
          matchType: targetFuzzy.score > 0.8 ? 'exact' : 'fuzzy',
          confidence: targetFuzzy.score * 0.9, // Slightly lower confidence for target matches
          replacementStart: currentWord.start,
          replacementEnd: currentWord.end,
          preview: term.targetTerm
        });
      }
    });

    // Sort by confidence and remove duplicates
    const uniqueResults = results.filter((result, index, self) => 
      index === self.findIndex(r => r.term.id === result.term.id)
    );

    return uniqueResults
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 8); // Limit to 8 suggestions
  }, [currentWord, terminology]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (suggestions.length === 0) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => (prev + 1) % suggestions.length);
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);
          break;
        case 'Enter':
        case 'Tab':
          e.preventDefault();
          if (suggestions[selectedIndex]) {
            const suggestion = suggestions[selectedIndex];
            onSuggestionSelect(
              suggestion.preview,
              suggestion.replacementStart,
              suggestion.replacementEnd
            );
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [suggestions, selectedIndex, onSuggestionSelect, onClose]);

  // Reset selected index when suggestions change
  useEffect(() => {
    setSelectedIndex(0);
  }, [suggestions]);

  if (isLoading || suggestions.length === 0) {
    return null;
  }

  const categoryColors = {
    character: 'bg-purple-100 text-purple-800',
    location: 'bg-green-100 text-green-800',
    concept: 'bg-orange-100 text-orange-800',
    technical: 'bg-blue-100 text-blue-800',
    general: 'bg-gray-100 text-gray-800',
  };

  return (
    <Card 
      ref={containerRef}
      className={`absolute z-50 w-80 max-h-64 overflow-y-auto shadow-lg border ${className}`}
    >
      <CardContent className="p-2">
        <div className="flex items-center justify-between mb-2 px-2 py-1">
          <div className="flex items-center space-x-1">
            <Zap className="h-3 w-3 text-blue-500" />
            <span className="text-xs font-medium text-gray-600">Terminology</span>
          </div>
          <span className="text-xs text-gray-400">
            {suggestions.length} suggestion{suggestions.length !== 1 ? 's' : ''}
          </span>
        </div>

        <div className="space-y-1">
          {suggestions.map((suggestion, index) => {
            const isSelected = index === selectedIndex;
            const colorClass = categoryColors[suggestion.term.category as keyof typeof categoryColors] || 
                              categoryColors.general;

            return (
              <div
                key={`${suggestion.term.id}-${index}`}
                className={`
                  p-2 rounded-md cursor-pointer transition-colors
                  ${isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'}
                `}
                onClick={() => {
                  onSuggestionSelect(
                    suggestion.preview,
                    suggestion.replacementStart,
                    suggestion.replacementEnd
                  );
                }}
              >
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <Badge size="sm" className={colorClass}>
                      {suggestion.term.category}
                    </Badge>
                    <Badge 
                      size="sm" 
                      variant={suggestion.matchType === 'exact' ? 'default' : 'outline'}
                      className="text-xs"
                    >
                      {suggestion.matchType}
                    </Badge>
                  </div>
                  {isSelected && (
                    <ArrowRight className="h-3 w-3 text-blue-500" />
                  )}
                </div>

                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">
                      {suggestion.term.sourceTerm}
                    </span>
                    <ArrowRight className="h-3 w-3 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">
                      {suggestion.term.targetTerm}
                    </span>
                  </div>

                  {suggestion.term.context && (
                    <p className="text-xs text-gray-500 truncate">
                      {suggestion.term.context}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">
                      Used {suggestion.term.frequency} times
                    </span>
                    <div className="flex items-center space-x-1">
                      <div 
                        className="w-12 bg-gray-200 rounded-full h-1"
                      >
                        <div 
                          className="bg-blue-500 h-1 rounded-full transition-all"
                          style={{ width: `${suggestion.confidence * 100}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-400">
                        {Math.round(suggestion.confidence * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-2 pt-2 border-t text-xs text-gray-400 px-2">
          <div className="flex items-center justify-between">
            <span>↑↓ Navigate • Enter/Tab Apply • Esc Close</span>
            <Badge variant="outline" size="sm" className="text-xs">
              {selectedIndex + 1}/{suggestions.length}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
