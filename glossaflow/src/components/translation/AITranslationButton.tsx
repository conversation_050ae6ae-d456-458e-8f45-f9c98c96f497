'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>ader2, 
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  CreditCard,
  Zap
} from 'lucide-react';
import { useCheckCreditSufficiencyQuery, useDeductCreditsMutation } from '@/lib/api/credits';
import { useGetTranslationSuggestionMutation } from '@/lib/api/ai-translation';
import type { TranslationSegment } from '@/types';

interface AITranslationButtonProps {
  segment: TranslationSegment;
  sourceLanguage: string;
  targetLanguage: string;
  projectId: string;
  chapterId?: string;
  currentBalance: number;
  onTranslationGenerated: (translation: string) => void;
  onCreditUpdate: () => void;
  disabled?: boolean;
  className?: string;
}

export function AITranslationButton({
  segment,
  sourceLanguage,
  targetLanguage,
  projectId,
  chapterId,
  currentBalance,
  onTranslationGenerated,
  onCreditUpdate,
  disabled = false,
  className = '',
}: AITranslationButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Estimate cost (roughly 0.01 credits per word)
  const wordCount = segment.source_text.split(' ').length;
  const estimatedCost = Math.max(0.1, wordCount * 0.01); // Minimum 0.1 credits

  // Check credit sufficiency
  const {
    data: creditCheckResponse,
    isLoading: isCheckingCredits,
  } = useCheckCreditSufficiencyQuery({
    requiredAmount: estimatedCost,
  });

  const creditCheck = creditCheckResponse?.data;
  const hasSufficientCredits = creditCheck?.hasSufficientCredits ?? false;

  // Mutations
  const [getTranslationSuggestion] = useGetTranslationSuggestionMutation();
  const [deductCredits] = useDeductCreditsMutation();

  const handleAITranslation = async () => {
    if (!hasSufficientCredits) {
      setError('Insufficient credits for AI translation');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setSuccess(false);

    try {
      // First, deduct credits
      await deductCredits({
        amount: estimatedCost,
        projectId,
        chapterId,
        segmentId: segment.id,
        description: `AI translation for segment ${segment.segment_number}`,
        metadata: {
          sourceLanguage,
          targetLanguage,
          wordCount,
          segmentId: segment.id,
        },
      }).unwrap();

      // Then get AI translation
      const translationResult = await getTranslationSuggestion({
        sourceText: segment.source_text,
        sourceLanguage,
        targetLanguage,
        context: `Translation segment ${segment.segment_number}`,
        domain: 'general',
        projectId,
      }).unwrap();

      const translation = translationResult.data.targetText;
      onTranslationGenerated(translation);
      onCreditUpdate();
      setSuccess(true);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);

    } catch (error: any) {
      console.error('AI translation failed:', error);
      setError(error.data?.error || 'Failed to generate AI translation');
    } finally {
      setIsGenerating(false);
    }
  };

  const formatCredits = (amount: number) => {
    return amount.toFixed(1);
  };

  const getButtonVariant = () => {
    if (!hasSufficientCredits) return 'outline';
    if (success) return 'default';
    return 'default';
  };

  const getButtonIcon = () => {
    if (isGenerating) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (success) return <CheckCircle className="h-4 w-4" />;
    if (!hasSufficientCredits) return <AlertTriangle className="h-4 w-4" />;
    return <Sparkles className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isGenerating) return 'Generating...';
    if (success) return 'Generated!';
    if (!hasSufficientCredits) return 'Insufficient Credits';
    return 'AI Translate';
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* AI Translation Button */}
      <div className="flex items-center space-x-2">
        <Button
          onClick={handleAITranslation}
          disabled={disabled || isGenerating || !hasSufficientCredits || isCheckingCredits}
          variant={getButtonVariant()}
          size="sm"
          className="flex items-center space-x-2"
        >
          {getButtonIcon()}
          <span>{getButtonText()}</span>
        </Button>

        {/* Cost Badge */}
        <Badge variant="outline" className="text-xs">
          <CreditCard className="mr-1 h-3 w-3" />
          {formatCredits(estimatedCost)} credits
        </Badge>

        {/* Word Count */}
        <Badge variant="secondary" className="text-xs">
          {wordCount} words
        </Badge>
      </div>

      {/* Credit Status */}
      {!hasSufficientCredits && !isCheckingCredits && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="flex items-center justify-between">
              <span className="text-sm">
                Need {formatCredits(estimatedCost)} credits. Current balance: {formatCredits(currentBalance)}
              </span>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4" />
              <span className="text-sm">
                AI translation generated successfully! {formatCredits(estimatedCost)} credits used.
              </span>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Message */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <span className="text-sm">{error}</span>
          </AlertDescription>
        </Alert>
      )}

      {/* Credit Check Loading */}
      {isCheckingCredits && (
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Checking credit balance...</span>
        </div>
      )}
    </div>
  );
}
