'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  CreditCard, 
  Star, 
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { useGetCreditPricingQuery, usePurchaseCreditsMutation } from '@/lib/api/credits';

interface CreditTopUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance: number;
  organizationId?: string;
  onSuccess?: () => void;
}

export function CreditTopUpModal({
  isOpen,
  onClose,
  currentBalance,
  organizationId,
  onSuccess,
}: CreditTopUpModalProps) {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    data: pricingResponse,
    isLoading: isPricingLoading,
    isError: isPricingError,
  } = useGetCreditPricingQuery();

  const [purchaseCredits] = usePurchaseCreditsMutation();

  const pricing = pricingResponse?.data;
  const packages = pricing?.packages || [];

  const handlePurchase = async () => {
    if (!selectedPackage) return;

    const packageData = packages.find(p => p.id === selectedPackage);
    if (!packageData) return;

    setIsProcessing(true);
    try {
      const result = await purchaseCredits({
        amount: packageData.credits,
        returnUrl: window.location.href,
      }).unwrap();

      // If payment requires additional steps (like 3D Secure), handle them
      if (result.data.clientSecret) {
        // In a real implementation, you would integrate with Stripe Elements here
        // For now, we'll simulate success
        console.log('Payment requires additional authentication:', result.data.clientSecret);
      }

      onSuccess();
    } catch (error) {
      console.error('Purchase failed:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsProcessing(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const formatCredits = (credits: number) => {
    return credits.toLocaleString();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Top Up Credits</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Balance */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-blue-900">Current Balance</h3>
                <p className="text-sm text-blue-700">Available for AI translations</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-900">
                  {currentBalance.toFixed(1)}
                </div>
                <div className="text-sm text-blue-700">credits</div>
              </div>
            </div>
          </div>

          {/* Credit Packages */}
          {isPricingLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <Skeleton className="h-6 w-24 mb-2" />
                    <Skeleton className="h-8 w-16 mb-4" />
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4 mb-4" />
                    <Skeleton className="h-10 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : isPricingError ? (
            <div className="text-center py-8">
              <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Unable to load pricing
              </h3>
              <p className="text-gray-600">
                Please try again later or contact support.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {packages.map((pkg) => (
                <Card 
                  key={pkg.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedPackage === pkg.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : ''
                  } ${pkg.popular ? 'border-blue-500' : ''}`}
                  onClick={() => setSelectedPackage(pkg.id)}
                >
                  <CardContent className="p-6">
                    <div className="text-center">
                      {/* Package Header */}
                      <div className="flex items-center justify-center mb-2">
                        <h3 className="font-semibold text-lg">{pkg.name}</h3>
                        {pkg.popular && (
                          <Badge className="ml-2 bg-blue-500">
                            <Star className="mr-1 h-3 w-3" />
                            Popular
                          </Badge>
                        )}
                      </div>

                      {/* Credits */}
                      <div className="mb-4">
                        <div className="text-3xl font-bold text-gray-900">
                          {formatCredits(pkg.credits)}
                        </div>
                        <div className="text-sm text-gray-600">credits</div>
                        {pkg.bonus && pkg.bonus > 0 && (
                          <div className="text-sm text-green-600 font-medium">
                            +{formatCredits(pkg.bonus)} bonus credits
                          </div>
                        )}
                      </div>

                      {/* Price */}
                      <div className="mb-4">
                        <div className="text-2xl font-bold text-blue-600">
                          {formatPrice(pkg.price)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {formatPrice(pkg.pricePerCredit)} per credit
                        </div>
                      </div>

                      {/* Features */}
                      <div className="space-y-2 mb-6 text-sm text-gray-600">
                        <div className="flex items-center justify-center space-x-1">
                          <Check className="h-4 w-4 text-green-500" />
                          <span>AI Translation</span>
                        </div>
                        <div className="flex items-center justify-center space-x-1">
                          <Check className="h-4 w-4 text-green-500" />
                          <span>No Expiration</span>
                        </div>
                        <div className="flex items-center justify-center space-x-1">
                          <Check className="h-4 w-4 text-green-500" />
                          <span>Team Sharing</span>
                        </div>
                      </div>

                      {/* Selection Indicator */}
                      {selectedPackage === pkg.id && (
                        <div className="flex items-center justify-center text-blue-600 mb-4">
                          <Check className="h-5 w-5 mr-2" />
                          <span className="font-medium">Selected</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* AI Translation Cost Info */}
          {pricing?.aiTranslationCost && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">AI Translation Costs</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Per Word:</span>
                  <div className="font-semibold">
                    {pricing.aiTranslationCost.perWord.toFixed(3)} credits
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Per Character:</span>
                  <div className="font-semibold">
                    {pricing.aiTranslationCost.perCharacter.toFixed(4)} credits
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Minimum Cost:</span>
                  <div className="font-semibold">
                    {pricing.aiTranslationCost.minimumCost.toFixed(1)} credits
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handlePurchase}
              disabled={!selectedPackage || isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Purchase Credits
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
