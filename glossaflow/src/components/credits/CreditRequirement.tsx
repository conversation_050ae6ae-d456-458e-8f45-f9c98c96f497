'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  AlertTriangle,
  CheckCircle,
  Zap,
  Info,
  Plus
} from 'lucide-react';
import { useCheckCreditSufficiencyQuery, useEstimateTranslationCostMutation } from '@/lib/api/credits';
import { CreditTopUpModal } from './CreditTopUpModal';
import type { Chapter, TranslationSegment } from '@/types';

interface CreditRequirementProps {
  chapter?: Chapter;
  segment?: TranslationSegment;
  sourceText?: string;
  sourceLanguage?: string;
  targetLanguage?: string;
  currentBalance: number;
  onCreditUpdate?: () => void;
  showTopUp?: boolean;
  className?: string;
}

export function CreditRequirement({
  chapter,
  segment,
  sourceText,
  sourceLanguage = 'en',
  targetLanguage = 'ja',
  currentBalance,
  onCreditUpdate,
  showTopUp = true,
  className = '',
}: CreditRequirementProps) {
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  const [estimatedCost, setEstimatedCost] = useState<number | null>(null);

  // Estimate translation cost
  const [estimateTranslationCost, { isLoading: isEstimating }] = useEstimateTranslationCostMutation();

  // Check credit sufficiency
  const requiredAmount = estimatedCost || chapter?.estimated_cost || 0;
  const {
    data: creditCheckResponse,
    isLoading: isCheckingCredits,
  } = useCheckCreditSufficiencyQuery({
    requiredAmount,
  }, {
    skip: requiredAmount === 0,
  });

  const creditCheck = creditCheckResponse?.data;
  const hasSufficientCredits = creditCheck?.hasSufficientCredits ?? true;
  const shortfall = creditCheck?.shortfall || 0;

  // Estimate cost for segment if sourceText is provided
  const handleEstimateCost = async () => {
    if (!sourceText) return;

    try {
      const result = await estimateTranslationCost({
        sourceText,
        sourceLanguage,
        targetLanguage,
      }).unwrap();
      
      setEstimatedCost(result.data.estimatedCost);
    } catch (error) {
      console.error('Failed to estimate cost:', error);
    }
  };

  // Auto-estimate cost when sourceText changes
  React.useEffect(() => {
    if (sourceText && !estimatedCost) {
      handleEstimateCost();
    }
  }, [sourceText]);

  const formatCredits = (amount: number) => {
    return amount.toFixed(1);
  };

  const getRequirementStatus = () => {
    if (requiredAmount === 0) {
      return {
        type: 'free',
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        message: 'No credits required',
      };
    }

    if (hasSufficientCredits) {
      return {
        type: 'sufficient',
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        message: 'Sufficient credits available',
      };
    }

    return {
      type: 'insufficient',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      message: 'Insufficient credits',
    };
  };

  const status = getRequirementStatus();
  const StatusIcon = status.icon;

  if (requiredAmount === 0 && !sourceText) {
    return null; // Don't show anything if no cost and no text to estimate
  }

  return (
    <>
      <Card className={`${className} ${status.bgColor} ${status.borderColor} border`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-sm">
            <CreditCard className="h-4 w-4" />
            <span>Credit Requirement</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* Cost Display */}
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2">
                  <StatusIcon className={`h-4 w-4 ${status.color}`} />
                  <span className="text-sm font-medium">
                    {isEstimating ? 'Estimating...' : `${formatCredits(requiredAmount)} credits`}
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {chapter ? `Chapter ${chapter.chapter_number}` : 'AI Translation'}
                </p>
              </div>
              
              {requiredAmount > 0 && (
                <Badge 
                  variant={hasSufficientCredits ? 'default' : 'destructive'}
                  className="text-xs"
                >
                  {status.message}
                </Badge>
              )}
            </div>

            {/* Balance vs Requirement */}
            {requiredAmount > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Current balance</span>
                  <span className="font-medium">{formatCredits(currentBalance)}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Required</span>
                  <span className="font-medium">{formatCredits(requiredAmount)}</span>
                </div>
                {!hasSufficientCredits && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-red-600">Shortfall</span>
                    <span className="font-medium text-red-600">
                      {formatCredits(shortfall)}
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* Insufficient Credits Warning */}
            {!hasSufficientCredits && requiredAmount > 0 && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      You need {formatCredits(shortfall)} more credits to proceed.
                    </span>
                    {showTopUp && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="ml-2 border-red-300 text-red-700 hover:bg-red-100"
                        onClick={() => setShowTopUpModal(true)}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Top Up
                      </Button>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Cost Breakdown for Segments */}
            {sourceText && estimatedCost && (
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center space-x-1 text-xs text-gray-500 mb-2">
                  <Info className="h-3 w-3" />
                  <span>Cost breakdown</span>
                </div>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span>Words: {sourceText.split(' ').length}</span>
                    <span>~{formatCredits(estimatedCost)} credits</span>
                  </div>
                  <div className="flex justify-between text-gray-500">
                    <span>Rate: ~$0.01 per word</span>
                    <span>AI Translation</span>
                  </div>
                </div>
              </div>
            )}

            {/* Free Translation Notice */}
            {requiredAmount === 0 && (
              <div className="flex items-center space-x-2 text-sm text-green-700">
                <Zap className="h-4 w-4" />
                <span>Free manual translation</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Credit Top-Up Modal */}
      <CreditTopUpModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        currentBalance={currentBalance}
        onSuccess={() => {
          setShowTopUpModal(false);
          onCreditUpdate?.();
        }}
      />
    </>
  );
}
