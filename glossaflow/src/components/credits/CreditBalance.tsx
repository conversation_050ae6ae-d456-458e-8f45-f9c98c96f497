'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  CreditCard, 
  Plus, 
  AlertTriangle, 
  TrendingUp,
  TrendingDown,
  DollarSign
} from 'lucide-react';
import { useGetCreditsQuery } from '@/lib/api/credits';
import { CreditTopUpModal } from './CreditTopUpModal';

interface CreditBalanceProps {
  organizationId?: string;
  showTopUp?: boolean;
  showUsageStats?: boolean;
  compact?: boolean;
  className?: string;
}

export function CreditBalance({
  organizationId,
  showTopUp = true,
  showUsageStats = true,
  compact = false,
  className = '',
}: CreditBalanceProps) {
  const [showTopUpModal, setShowTopUpModal] = useState(false);

  const {
    data: creditsResponse,
    isLoading,
    isError,
    refetch,
  } = useGetCreditsQuery(organizationId);

  const creditsData = creditsResponse?.data;
  const credits = creditsData?.credits;
  const usage = creditsData?.usage;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Credit Balance</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
          {!compact && showUsageStats && (
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-16" />
              <Skeleton className="h-16" />
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  if (isError || !credits) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="mx-auto h-8 w-8 text-red-500 mb-2" />
          <p className="text-sm text-gray-600 mb-4">Unable to load credit balance</p>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  const balance = credits.balance || 0;
  const isLowBalance = balance < 10; // Consider balance low if less than 10 credits
  const isCriticalBalance = balance < 5; // Critical if less than 5 credits

  const getBalanceColor = () => {
    if (isCriticalBalance) return 'text-red-600';
    if (isLowBalance) return 'text-orange-600';
    return 'text-green-600';
  };

  const getBalanceBadge = () => {
    if (isCriticalBalance) {
      return (
        <Badge variant="destructive" className="ml-2">
          <AlertTriangle className="mr-1 h-3 w-3" />
          Critical
        </Badge>
      );
    }
    if (isLowBalance) {
      return (
        <Badge variant="outline" className="ml-2 text-orange-600 border-orange-200">
          <AlertTriangle className="mr-1 h-3 w-3" />
          Low
        </Badge>
      );
    }
    return null;
  };

  const formatCredits = (amount: number) => {
    return amount.toFixed(1);
  };

  const usageChange = usage && usage.lastMonth > 0 
    ? ((usage.thisMonth - usage.lastMonth) / usage.lastMonth) * 100 
    : 0;

  if (compact) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center space-x-2">
          <CreditCard className="h-4 w-4 text-gray-500" />
          <span className={`font-semibold ${getBalanceColor()}`}>
            {formatCredits(balance)} credits
          </span>
          {getBalanceBadge()}
        </div>
        {showTopUp && (
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => setShowTopUpModal(true)}
          >
            <Plus className="mr-1 h-3 w-3" />
            Top Up
          </Button>
        )}
        {showTopUpModal && (
          <CreditTopUpModal
            isOpen={showTopUpModal}
            onClose={() => setShowTopUpModal(false)}
            currentBalance={balance}
            onSuccess={() => {
              setShowTopUpModal(false);
              refetch();
            }}
          />
        )}
      </div>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>Credit Balance</span>
            </div>
            {showTopUp && (
              <Button 
                size="sm" 
                onClick={() => setShowTopUpModal(true)}
                variant={isCriticalBalance ? "default" : "outline"}
              >
                <Plus className="mr-2 h-4 w-4" />
                Top Up
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Balance */}
          <div className="text-center">
            <div className="flex items-center justify-center">
              <span className={`text-3xl font-bold ${getBalanceColor()}`}>
                {formatCredits(balance)}
              </span>
              <span className="text-lg text-gray-600 ml-2">credits</span>
              {getBalanceBadge()}
            </div>
            {isCriticalBalance && (
              <p className="text-sm text-red-600 mt-2">
                Your credit balance is critically low. Top up now to continue using AI translation.
              </p>
            )}
            {isLowBalance && !isCriticalBalance && (
              <p className="text-sm text-orange-600 mt-2">
                Your credit balance is running low. Consider topping up soon.
              </p>
            )}
          </div>

          {/* Usage Statistics */}
          {showUsageStats && usage && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1">
                  <span className="text-lg font-semibold">
                    {formatCredits(usage.thisMonth)}
                  </span>
                  {usageChange !== 0 && (
                    <div className={`flex items-center ${usageChange > 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {usageChange > 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      <span className="text-xs ml-1">
                        {Math.abs(usageChange).toFixed(0)}%
                      </span>
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-600">This Month</p>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold">
                  {formatCredits(usage.lastMonth)}
                </div>
                <p className="text-xs text-gray-600">Last Month</p>
              </div>
            </div>
          )}

          {/* Total Statistics */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t text-sm">
            <div>
              <span className="text-gray-600">Total Purchased:</span>
              <div className="font-semibold">{formatCredits(credits.total_purchased)}</div>
            </div>
            <div>
              <span className="text-gray-600">Total Spent:</span>
              <div className="font-semibold">{formatCredits(credits.total_spent)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {showTopUpModal && (
        <CreditTopUpModal
          isOpen={showTopUpModal}
          onClose={() => setShowTopUpModal(false)}
          currentBalance={balance}
          onSuccess={() => {
            setShowTopUpModal(false);
            refetch();
          }}
        />
      )}
    </>
  );
}
