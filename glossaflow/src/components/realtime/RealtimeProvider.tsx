'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { getSupabaseRealtimeService, RealtimeEventHandlers } from '@/lib/supabase/realtime';
import { RealtimeNotifications } from './RealtimeNotifications';

interface RealtimeContextType {
  isConnected: boolean;
  subscribeToProject: (projectId: string, handlers: RealtimeEventHandlers) => void;
  unsubscribeFromProject: (projectId: string) => void;
  subscribeToSegment: (segmentId: string, handlers: RealtimeEventHandlers) => void;
  unsubscribeFromSegment: (segmentId: string) => void;
  sendTypingIndicator: (projectId: string, segmentId: string, isTyping: boolean) => void;
  updatePresence: (projectId: string, updates: any) => void;
  getPresenceState: (projectId: string) => any;
}

const RealtimeContext = createContext<RealtimeContextType | null>(null);

export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};

interface RealtimeProviderProps {
  children: React.ReactNode;
}

export function RealtimeProvider({ children }: RealtimeProviderProps) {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [realtimeService] = useState(() => getSupabaseRealtimeService());

  useEffect(() => {
    if (session?.user) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
      // Clean up all subscriptions when user logs out
      realtimeService.unsubscribeAll();
    }
  }, [session, realtimeService]);

  const subscribeToProject = (projectId: string, handlers: RealtimeEventHandlers) => {
    if (!session?.user) return;
    realtimeService.subscribeToProject(projectId, handlers);
  };

  const unsubscribeFromProject = (projectId: string) => {
    realtimeService.unsubscribeFromProject(projectId);
  };

  const subscribeToSegment = (segmentId: string, handlers: RealtimeEventHandlers) => {
    if (!session?.user) return;
    realtimeService.subscribeToSegment(segmentId, handlers);
  };

  const unsubscribeFromSegment = (segmentId: string) => {
    realtimeService.unsubscribeFromSegment(segmentId);
  };

  const sendTypingIndicator = (projectId: string, segmentId: string, isTyping: boolean) => {
    if (!session?.user) return;
    realtimeService.sendTypingIndicator(projectId, segmentId, session.user.id!, isTyping);
  };

  const updatePresence = (projectId: string, updates: any) => {
    if (!session?.user) return;
    realtimeService.updatePresence(projectId, updates);
  };

  const getPresenceState = (projectId: string) => {
    return realtimeService.getPresenceState(projectId);
  };

  const contextValue: RealtimeContextType = {
    isConnected,
    subscribeToProject,
    unsubscribeFromProject,
    subscribeToSegment,
    unsubscribeFromSegment,
    sendTypingIndicator,
    updatePresence,
    getPresenceState,
  };

  return (
    <RealtimeContext.Provider value={contextValue}>
      {children}
      {/* Global notifications component */}
      {session?.user && (
        <RealtimeNotifications
          userId={session.user.id!}
          showToasts={true}
        />
      )}
    </RealtimeContext.Provider>
  );
}
