'use client';

import { useState, useEffect } from 'react';
import { Toast, ToastProvider, ToastViewport } from '@/components/ui/toast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getRealtimeService } from '@/lib/realtime/websocket';
import { 
  Bell, 
  MessageSquare, 
  FileText, 
  Users, 
  CheckCircle,
  AlertCircle,
  Info,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Notification {
  id: string;
  type: 'comment' | 'project_update' | 'assignment' | 'completion' | 'mention' | 'system';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  actionUrl?: string;
  user?: {
    id: string;
    name: string;
    avatar?: string;
  };
  metadata?: Record<string, any>;
}

interface RealtimeNotificationsProps {
  userId: string;
  maxVisible?: number;
  autoHideDelay?: number;
  showToasts?: boolean;
  className?: string;
}

const notificationIcons = {
  comment: MessageSquare,
  project_update: FileText,
  assignment: Users,
  completion: CheckCircle,
  mention: Bell,
  system: Info,
};

const notificationColors = {
  comment: 'text-blue-600',
  project_update: 'text-green-600',
  assignment: 'text-purple-600',
  completion: 'text-green-600',
  mention: 'text-orange-600',
  system: 'text-gray-600',
};

export function RealtimeNotifications({
  userId,
  maxVisible = 5,
  autoHideDelay = 5000,
  showToasts = true,
  className = '',
}: RealtimeNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [toasts, setToasts] = useState<Notification[]>([]);
  const realtimeService = getRealtimeService();

  useEffect(() => {
    // Set up realtime event handlers
    realtimeService.setHandlers({
      onNotification: (data) => {
        if (data.userId === userId || data.broadcast) {
          const notification: Notification = {
            id: data.id || Math.random().toString(36).substr(2, 9),
            type: data.type,
            title: data.title,
            message: data.message,
            timestamp: Date.now(),
            read: false,
            actionUrl: data.actionUrl,
            user: data.user,
            metadata: data.metadata,
          };

          // Add to notifications list
          setNotifications(prev => [notification, ...prev].slice(0, 50)); // Keep last 50

          // Show toast if enabled
          if (showToasts) {
            setToasts(prev => [...prev, notification]);
            
            // Auto-hide toast
            setTimeout(() => {
              setToasts(prev => prev.filter(t => t.id !== notification.id));
            }, autoHideDelay);
          }

          // Play notification sound (optional)
          if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(notification.title, {
              body: notification.message,
              icon: '/favicon.ico',
              tag: notification.id,
            });
          }
        }
      },
    });

    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, [userId, showToasts, autoHideDelay, realtimeService]);

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  };

  const dismissToast = (notificationId: string) => {
    setToasts(prev => prev.filter(t => t.id !== notificationId));
  };

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <>
      {/* Toast Notifications */}
      {showToasts && (
        <ToastProvider>
          {toasts.map((notification) => {
            const Icon = notificationIcons[notification.type];
            return (
              <Toast
                key={notification.id}
                className="cursor-pointer"
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start space-x-3">
                  <div className={cn('p-1 rounded-full', notificationColors[notification.type])}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">{notification.title}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          dismissToast(notification.id);
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                    <p className="text-sm text-gray-600">{notification.message}</p>
                    {notification.user && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Avatar className="h-5 w-5">
                          <AvatarImage src={notification.user.avatar} />
                          <AvatarFallback className="text-xs">
                            {notification.user.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-gray-500">{notification.user.name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </Toast>
            );
          })}
          <ToastViewport />
        </ToastProvider>
      )}

      {/* Notification Bell Icon with Badge */}
      <div className={cn('relative', className)}>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* Notification List (can be used in a dropdown or sidebar) */}
      <div className="hidden">
        <div className="w-80 max-h-96 overflow-y-auto">
          <div className="p-3 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Notifications</h3>
              {unreadCount > 0 && (
                <Badge variant="secondary">{unreadCount} new</Badge>
              )}
            </div>
          </div>
          
          <div className="divide-y">
            {notifications.slice(0, maxVisible).map((notification) => {
              const Icon = notificationIcons[notification.type];
              return (
                <div
                  key={notification.id}
                  className={cn(
                    'p-3 hover:bg-gray-50 cursor-pointer transition-colors',
                    !notification.read && 'bg-blue-50'
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={cn('p-1 rounded-full', notificationColors[notification.type])}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className={cn(
                          'text-sm',
                          notification.read ? 'text-gray-600' : 'font-medium text-gray-900'
                        )}>
                          {notification.title}
                        </p>
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(notification.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                      {notification.user && (
                        <div className="flex items-center space-x-2 mt-2">
                          <Avatar className="h-5 w-5">
                            <AvatarImage src={notification.user.avatar} />
                            <AvatarFallback className="text-xs">
                              {notification.user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-gray-500">{notification.user.name}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          
          {notifications.length === 0 && (
            <div className="p-8 text-center text-gray-500">
              <Bell className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No notifications yet</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
