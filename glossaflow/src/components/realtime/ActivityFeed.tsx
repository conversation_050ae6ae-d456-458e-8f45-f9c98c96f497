'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getRealtimeService } from '@/lib/realtime/websocket';
import { 
  FileText, 
  MessageSquare, 
  CheckCircle, 
  UserPlus, 
  Upload,
  Edit,
  Trash2,
  Clock,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ActivityItem {
  id: string;
  type: 'translation' | 'comment' | 'review' | 'assignment' | 'upload' | 'edit' | 'delete';
  action: string;
  description: string;
  timestamp: number;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
  target?: {
    type: 'project' | 'segment' | 'file' | 'comment';
    id: string;
    name: string;
  };
  metadata?: Record<string, any>;
}

interface ActivityFeedProps {
  projectId?: string;
  userId?: string;
  maxItems?: number;
  showFilters?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  className?: string;
}

const activityIcons = {
  translation: FileText,
  comment: MessageSquare,
  review: CheckCircle,
  assignment: UserPlus,
  upload: Upload,
  edit: Edit,
  delete: Trash2,
};

const activityColors = {
  translation: 'text-blue-600',
  comment: 'text-green-600',
  review: 'text-purple-600',
  assignment: 'text-orange-600',
  upload: 'text-indigo-600',
  edit: 'text-yellow-600',
  delete: 'text-red-600',
};

// Mock data - in real app, this would come from API
const mockActivities: ActivityItem[] = [
  {
    id: '1',
    type: 'translation',
    action: 'completed translation',
    description: 'Completed translation for segment #234',
    timestamp: Date.now() - 300000, // 5 minutes ago
    user: {
      id: 'user1',
      name: 'Sarah Chen',
      avatar: '',
    },
    target: {
      type: 'segment',
      id: '234',
      name: 'Welcome message',
    },
  },
  {
    id: '2',
    type: 'comment',
    action: 'added comment',
    description: 'Added a comment on segment #234',
    timestamp: Date.now() - 600000, // 10 minutes ago
    user: {
      id: 'user2',
      name: 'Mike Johnson',
      avatar: '',
    },
    target: {
      type: 'segment',
      id: '234',
      name: 'Welcome message',
    },
  },
  {
    id: '3',
    type: 'review',
    action: 'approved translation',
    description: 'Approved translation for segment #233',
    timestamp: Date.now() - 900000, // 15 minutes ago
    user: {
      id: 'user2',
      name: 'Mike Johnson',
      avatar: '',
    },
    target: {
      type: 'segment',
      id: '233',
      name: 'Login button',
    },
  },
];

export function ActivityFeed({
  projectId,
  userId,
  maxItems = 20,
  showFilters = true,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
  className = '',
}: ActivityFeedProps) {
  const [activities, setActivities] = useState<ActivityItem[]>(mockActivities);
  const [filteredActivities, setFilteredActivities] = useState<ActivityItem[]>(mockActivities);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const realtimeService = getRealtimeService();

  useEffect(() => {
    // Set up realtime event handlers
    realtimeService.setHandlers({
      onProjectUpdate: (data) => {
        if (!projectId || data.projectId === projectId) {
          const activity: ActivityItem = {
            id: Math.random().toString(36).substr(2, 9),
            type: data.type || 'edit',
            action: data.action || 'updated project',
            description: data.description || 'Project was updated',
            timestamp: Date.now(),
            user: data.user,
            target: data.target,
            metadata: data.metadata,
          };
          
          setActivities(prev => [activity, ...prev].slice(0, maxItems));
        }
      },
      
      onSegmentUpdate: (data) => {
        if (!projectId || data.projectId === projectId) {
          const activity: ActivityItem = {
            id: Math.random().toString(36).substr(2, 9),
            type: 'translation',
            action: 'updated translation',
            description: `Updated translation for segment #${data.segmentId}`,
            timestamp: Date.now(),
            user: data.user,
            target: {
              type: 'segment',
              id: data.segmentId,
              name: data.segmentName || `Segment #${data.segmentId}`,
            },
            metadata: data.metadata,
          };
          
          setActivities(prev => [activity, ...prev].slice(0, maxItems));
        }
      },
      
      onCommentAdded: (data) => {
        if (!projectId || data.projectId === projectId) {
          const activity: ActivityItem = {
            id: Math.random().toString(36).substr(2, 9),
            type: 'comment',
            action: 'added comment',
            description: `Added a comment on segment #${data.segmentId}`,
            timestamp: Date.now(),
            user: data.user,
            target: {
              type: 'segment',
              id: data.segmentId,
              name: data.segmentName || `Segment #${data.segmentId}`,
            },
            metadata: data.metadata,
          };
          
          setActivities(prev => [activity, ...prev].slice(0, maxItems));
        }
      },
    });

    // Join project room if specified
    if (projectId) {
      realtimeService.joinProject(projectId);
    }

    return () => {
      if (projectId) {
        realtimeService.leaveProject(projectId);
      }
    };
  }, [projectId, maxItems, realtimeService]);

  useEffect(() => {
    // Filter activities based on selected filter
    if (selectedFilter === 'all') {
      setFilteredActivities(activities);
    } else {
      setFilteredActivities(activities.filter(activity => activity.type === selectedFilter));
    }
  }, [activities, selectedFilter]);

  useEffect(() => {
    // Auto-refresh activities
    if (autoRefresh) {
      const interval = setInterval(() => {
        // In a real app, this would fetch latest activities from API
        // For now, we'll just simulate with existing data
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  const filterOptions = [
    { value: 'all', label: 'All Activity' },
    { value: 'translation', label: 'Translations' },
    { value: 'comment', label: 'Comments' },
    { value: 'review', label: 'Reviews' },
    { value: 'assignment', label: 'Assignments' },
    { value: 'upload', label: 'Uploads' },
  ];

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Activity Feed
            </CardTitle>
            <CardDescription>
              Real-time updates from your {projectId ? 'project' : 'workspace'}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
          </Button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="flex flex-wrap gap-2 mt-4">
            {filterOptions.map((option) => (
              <Button
                key={option.value}
                variant={selectedFilter === option.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter(option.value)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {filteredActivities.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Clock className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No activity yet</p>
              <p className="text-xs">Activity will appear here as team members work</p>
            </div>
          ) : (
            filteredActivities.map((activity) => {
              const Icon = activityIcons[activity.type];
              return (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={cn('p-1 rounded-full', activityColors[activity.type])}>
                    <Icon className="h-4 w-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={activity.user.avatar} />
                        <AvatarFallback className="text-xs">
                          {activity.user.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">{activity.user.name}</span>
                      <span className="text-sm text-gray-600">{activity.action}</span>
                      {activity.target && (
                        <>
                          <Badge variant="outline" className="text-xs">
                            {activity.target.name}
                          </Badge>
                        </>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                    
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(activity.timestamp)}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {activity.type}
                      </Badge>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {filteredActivities.length >= maxItems && (
          <div className="text-center mt-4">
            <Button variant="outline" size="sm">
              Load More
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
