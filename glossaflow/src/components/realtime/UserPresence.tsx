'use client';

import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getRealtimeService } from '@/lib/realtime/websocket';
import { cn } from '@/lib/utils';

interface User {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen?: string;
  currentSegment?: string;
}

interface UserPresenceProps {
  projectId?: string;
  segmentId?: string;
  currentUserId: string;
  className?: string;
  maxVisible?: number;
  showStatus?: boolean;
  showNames?: boolean;
}

const statusColors = {
  online: 'bg-green-500',
  away: 'bg-yellow-500',
  busy: 'bg-red-500',
  offline: 'bg-gray-400',
};

const statusLabels = {
  online: 'Online',
  away: 'Away',
  busy: 'Busy',
  offline: 'Offline',
};

export function UserPresence({
  projectId,
  segmentId,
  currentUserId,
  className = '',
  maxVisible = 5,
  showStatus = true,
  showNames = false,
}: UserPresenceProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const realtimeService = getRealtimeService();

  useEffect(() => {
    // Set up realtime event handlers
    realtimeService.setHandlers({
      onUserJoined: (data) => {
        if (data.projectId === projectId) {
          setUsers(prev => {
            const existing = prev.find(u => u.id === data.user.id);
            if (existing) {
              return prev.map(u => u.id === data.user.id ? { ...u, ...data.user } : u);
            }
            return [...prev, data.user];
          });
        }
      },
      
      onUserLeft: (data) => {
        if (data.projectId === projectId) {
          setUsers(prev => prev.filter(u => u.id !== data.userId));
        }
      },
      
      onTypingStart: (data) => {
        if (data.segmentId === segmentId && data.userId !== currentUserId) {
          setTypingUsers(prev => new Set([...prev, data.userId]));
        }
      },
      
      onTypingStop: (data) => {
        if (data.segmentId === segmentId) {
          setTypingUsers(prev => {
            const newSet = new Set(prev);
            newSet.delete(data.userId);
            return newSet;
          });
        }
      },
    });

    // Join project/segment rooms
    if (projectId) {
      realtimeService.joinProject(projectId);
    }
    if (segmentId) {
      realtimeService.joinSegment(segmentId);
    }

    return () => {
      // Leave rooms on cleanup
      if (projectId) {
        realtimeService.leaveProject(projectId);
      }
      if (segmentId) {
        realtimeService.leaveSegment(segmentId);
      }
    };
  }, [projectId, segmentId, currentUserId, realtimeService]);

  // Filter out current user and offline users
  const activeUsers = users.filter(user => 
    user.id !== currentUserId && user.status !== 'offline'
  );

  const visibleUsers = activeUsers.slice(0, maxVisible);
  const hiddenCount = Math.max(0, activeUsers.length - maxVisible);

  const formatLastSeen = (lastSeen?: string) => {
    if (!lastSeen) return 'Unknown';
    const date = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  if (activeUsers.length === 0) {
    return null;
  }

  return (
    <TooltipProvider>
      <div className={cn('flex items-center space-x-2', className)}>
        {/* User Avatars */}
        <div className="flex -space-x-2">
          {visibleUsers.map((user) => (
            <Tooltip key={user.id}>
              <TooltipTrigger asChild>
                <div className="relative">
                  <Avatar className="h-8 w-8 border-2 border-white">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="text-xs">
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Status indicator */}
                  {showStatus && (
                    <div className={cn(
                      'absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white',
                      statusColors[user.status]
                    )} />
                  )}
                  
                  {/* Typing indicator */}
                  {typingUsers.has(user.id) && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
                    </div>
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-center">
                  <p className="font-medium">{user.name}</p>
                  <p className="text-xs text-gray-500">
                    {statusLabels[user.status]}
                    {user.status === 'offline' && user.lastSeen && (
                      <span> • Last seen {formatLastSeen(user.lastSeen)}</span>
                    )}
                  </p>
                  {typingUsers.has(user.id) && (
                    <p className="text-xs text-blue-600">Typing...</p>
                  )}
                  {user.currentSegment && user.currentSegment !== segmentId && (
                    <p className="text-xs text-gray-500">
                      Working on segment {user.currentSegment}
                    </p>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
          
          {/* Hidden users count */}
          {hiddenCount > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center justify-center h-8 w-8 bg-gray-100 border-2 border-white rounded-full">
                  <span className="text-xs font-medium text-gray-600">
                    +{hiddenCount}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{hiddenCount} more user{hiddenCount > 1 ? 's' : ''} online</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* User names (optional) */}
        {showNames && visibleUsers.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {visibleUsers.map((user) => (
              <Badge key={user.id} variant="outline" className="text-xs">
                {user.name}
                {typingUsers.has(user.id) && (
                  <span className="ml-1 text-blue-600">typing...</span>
                )}
              </Badge>
            ))}
          </div>
        )}

        {/* Typing indicators */}
        {typingUsers.size > 0 && !showNames && (
          <div className="flex items-center space-x-1">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
            </div>
            <span className="text-xs text-gray-500">
              {Array.from(typingUsers).map(userId => {
                const user = users.find(u => u.id === userId);
                return user?.name.split(' ')[0];
              }).join(', ')} typing...
            </span>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
