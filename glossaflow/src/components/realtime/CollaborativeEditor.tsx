'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { UserPresence } from './UserPresence';
import { getRealtimeService } from '@/lib/realtime/websocket';
import { cn } from '@/lib/utils';
import { debounce } from 'lodash';

interface CollaborativeEditorProps {
  segmentId: string;
  projectId: string;
  currentUserId: string;
  initialValue?: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  onSave?: (value: string) => void;
  readOnly?: boolean;
  className?: string;
  showPresence?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

interface CursorPosition {
  userId: string;
  position: number;
  selection?: {
    start: number;
    end: number;
  };
}

interface EditOperation {
  type: 'insert' | 'delete' | 'replace';
  position: number;
  content: string;
  length?: number;
  userId: string;
  timestamp: number;
}

export function CollaborativeEditor({
  segmentId,
  projectId,
  currentUserId,
  initialValue = '',
  placeholder = 'Enter translation...',
  onChange,
  onSave,
  readOnly = false,
  className = '',
  showPresence = true,
  autoSave = true,
  autoSaveDelay = 2000,
}: CollaborativeEditorProps) {
  const [value, setValue] = useState(initialValue);
  const [isTyping, setIsTyping] = useState(false);
  const [cursors, setCursors] = useState<CursorPosition[]>([]);
  const [lastSaved, setLastSaved] = useState<string>(initialValue);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const realtimeService = getRealtimeService();
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Debounced functions
  const debouncedSave = useCallback(
    debounce((text: string) => {
      if (autoSave && text !== lastSaved) {
        onSave?.(text);
        setLastSaved(text);
        setHasUnsavedChanges(false);
      }
    }, autoSaveDelay),
    [autoSave, autoSaveDelay, lastSaved, onSave]
  );

  const debouncedStopTyping = useCallback(
    debounce(() => {
      setIsTyping(false);
      realtimeService.stopTyping(segmentId);
    }, 1000),
    [segmentId, realtimeService]
  );

  const debouncedBroadcastChange = useCallback(
    debounce((text: string, operation: EditOperation) => {
      realtimeService.updateSegment(segmentId, {
        content: text,
        operation,
      });
    }, 300),
    [segmentId, realtimeService]
  );

  useEffect(() => {
    // Set up realtime event handlers
    realtimeService.setHandlers({
      onSegmentUpdate: (data) => {
        if (data.segmentId === segmentId && data.userId !== currentUserId) {
          // Apply remote changes
          if (data.operation) {
            applyRemoteOperation(data.operation);
          } else if (data.content !== undefined) {
            setValue(data.content);
          }
        }
      },
      
      onTypingStart: (data) => {
        if (data.segmentId === segmentId && data.userId !== currentUserId) {
          // Update cursor positions or typing indicators
          setCursors(prev => {
            const existing = prev.find(c => c.userId === data.userId);
            if (existing) {
              return prev.map(c => 
                c.userId === data.userId 
                  ? { ...c, position: data.position || c.position }
                  : c
              );
            }
            return [...prev, {
              userId: data.userId,
              position: data.position || 0,
            }];
          });
        }
      },
      
      onTypingStop: (data) => {
        if (data.segmentId === segmentId) {
          setCursors(prev => prev.filter(c => c.userId !== data.userId));
        }
      },
    });

    // Join segment room
    realtimeService.joinSegment(segmentId);

    return () => {
      // Clean up
      realtimeService.leaveSegment(segmentId);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [segmentId, currentUserId, realtimeService]);

  const applyRemoteOperation = (operation: EditOperation) => {
    setValue(currentValue => {
      let newValue = currentValue;
      
      switch (operation.type) {
        case 'insert':
          newValue = 
            currentValue.slice(0, operation.position) +
            operation.content +
            currentValue.slice(operation.position);
          break;
          
        case 'delete':
          newValue = 
            currentValue.slice(0, operation.position) +
            currentValue.slice(operation.position + (operation.length || 0));
          break;
          
        case 'replace':
          newValue = 
            currentValue.slice(0, operation.position) +
            operation.content +
            currentValue.slice(operation.position + (operation.length || 0));
          break;
      }
      
      return newValue;
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const oldValue = value;
    
    setValue(newValue);
    onChange?.(newValue);
    setHasUnsavedChanges(newValue !== lastSaved);

    // Start typing indicator
    if (!isTyping) {
      setIsTyping(true);
      realtimeService.startTyping(segmentId);
    }

    // Reset typing timeout
    debouncedStopTyping();

    // Create operation for broadcasting
    const operation: EditOperation = {
      type: newValue.length > oldValue.length ? 'insert' : 
            newValue.length < oldValue.length ? 'delete' : 'replace',
      position: e.target.selectionStart || 0,
      content: newValue,
      userId: currentUserId,
      timestamp: Date.now(),
    };

    // Broadcast change to other users
    debouncedBroadcastChange(newValue, operation);

    // Auto-save
    if (autoSave) {
      debouncedSave(newValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 's') {
        e.preventDefault();
        onSave?.(value);
        setLastSaved(value);
        setHasUnsavedChanges(false);
      }
    }
  };

  const handleSelectionChange = () => {
    if (textareaRef.current) {
      const position = textareaRef.current.selectionStart;
      const selection = {
        start: textareaRef.current.selectionStart,
        end: textareaRef.current.selectionEnd,
      };

      // Broadcast cursor position (debounced)
      // This would be implemented with a more sophisticated operational transform
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Header with presence and status */}
      {showPresence && (
        <div className="flex items-center justify-between">
          <UserPresence
            projectId={projectId}
            segmentId={segmentId}
            currentUserId={currentUserId}
            maxVisible={3}
            showStatus={false}
          />
          
          <div className="flex items-center space-x-2">
            {isTyping && (
              <Badge variant="outline" className="text-xs">
                Typing...
              </Badge>
            )}
            {hasUnsavedChanges && (
              <Badge variant="outline" className="text-xs text-orange-600">
                Unsaved changes
              </Badge>
            )}
            {autoSave && !hasUnsavedChanges && lastSaved && (
              <Badge variant="outline" className="text-xs text-green-600">
                Auto-saved
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="relative">
        <Textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onSelect={handleSelectionChange}
          placeholder={placeholder}
          readOnly={readOnly}
          className={cn(
            'min-h-[120px] resize-none',
            readOnly && 'bg-gray-50 cursor-not-allowed'
          )}
        />

        {/* Cursor indicators for other users */}
        {cursors.map((cursor) => (
          <div
            key={cursor.userId}
            className="absolute w-0.5 h-5 bg-blue-500 pointer-events-none"
            style={{
              // This would need more sophisticated positioning logic
              // based on text metrics and cursor position
              left: `${Math.min(cursor.position * 8, 100)}px`,
              top: '10px',
            }}
          >
            <div className="absolute -top-6 -left-2 px-2 py-1 bg-blue-500 text-white text-xs rounded whitespace-nowrap">
              User {cursor.userId.slice(0, 8)}
            </div>
          </div>
        ))}
      </div>

      {/* Footer with shortcuts */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>{value.length} characters</span>
          <span>{value.split(/\s+/).filter(w => w.length > 0).length} words</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>Ctrl+S to save</span>
          {autoSave && <span>• Auto-save enabled</span>}
        </div>
      </div>
    </div>
  );
}
