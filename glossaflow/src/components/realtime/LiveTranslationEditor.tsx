'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { UserPresence } from './UserPresence';
import { useRealtime } from './RealtimeProvider';
import { broadcastSegmentUpdate } from '@/lib/supabase/realtime';
import { Save, Clock, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { debounce } from 'lodash';

interface LiveTranslationEditorProps {
  segmentId: string;
  projectId: string;
  sourceText: string;
  initialTranslation?: string;
  targetLanguage: string;
  onSave?: (translation: string) => void;
  readOnly?: boolean;
  className?: string;
}

interface TypingUser {
  userId: string;
  userName: string;
  timestamp: number;
}

export function LiveTranslationEditor({
  segmentId,
  projectId,
  sourceText,
  initialTranslation = '',
  targetLanguage,
  onSave,
  readOnly = false,
  className = '',
}: LiveTranslationEditorProps) {
  const { data: session } = useSession();
  const realtime = useRealtime();
  
  const [translation, setTranslation] = useState(initialTranslation);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [lastSaved, setLastSaved] = useState(initialTranslation);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const currentUserId = session?.user?.id;

  // Debounced functions
  const debouncedSave = useCallback(
    debounce(async (text: string) => {
      if (text !== lastSaved && !readOnly) {
        setIsSaving(true);
        try {
          await broadcastSegmentUpdate(segmentId, {
            target_text: text,
            updated_at: new Date().toISOString(),
            updated_by: currentUserId,
          });
          setLastSaved(text);
          setHasUnsavedChanges(false);
          onSave?.(text);
        } catch (error) {
          console.error('Failed to save translation:', error);
        } finally {
          setIsSaving(false);
        }
      }
    }, 2000),
    [segmentId, lastSaved, readOnly, currentUserId, onSave]
  );

  const debouncedStopTyping = useCallback(
    debounce(() => {
      setIsTyping(false);
      realtime.sendTypingIndicator(projectId, segmentId, false);
    }, 1000),
    [projectId, segmentId, realtime]
  );

  // Set up realtime subscriptions
  useEffect(() => {
    if (!currentUserId) return;

    realtime.subscribeToSegment(segmentId, {
      onSegmentUpdate: (payload) => {
        if (payload.new && payload.new.id === segmentId) {
          const newTranslation = payload.new.target_text;
          if (newTranslation !== translation && payload.new.updated_by !== currentUserId) {
            setTranslation(newTranslation);
            setLastSaved(newTranslation);
            setHasUnsavedChanges(false);
          }
        }
      },
    });

    realtime.subscribeToProject(projectId, {
      onTypingIndicator: (payload) => {
        if (payload.payload.segment_id === segmentId && payload.payload.user_id !== currentUserId) {
          const { user_id, is_typing, timestamp } = payload.payload;
          
          setTypingUsers(prev => {
            const filtered = prev.filter(u => u.userId !== user_id);
            if (is_typing) {
              return [...filtered, {
                userId: user_id,
                userName: `User ${user_id.slice(0, 8)}`, // In real app, get from user data
                timestamp: new Date(timestamp).getTime(),
              }];
            }
            return filtered;
          });
        }
      },
    });

    return () => {
      realtime.unsubscribeFromSegment(segmentId);
    };
  }, [segmentId, projectId, currentUserId, translation, realtime]);

  // Clean up old typing indicators
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setTypingUsers(prev => prev.filter(user => now - user.timestamp < 5000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setTranslation(newValue);
    setHasUnsavedChanges(newValue !== lastSaved);

    // Start typing indicator
    if (!isTyping && !readOnly) {
      setIsTyping(true);
      realtime.sendTypingIndicator(projectId, segmentId, true);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    debouncedStopTyping();

    // Auto-save
    debouncedSave(newValue);
  };

  const handleManualSave = async () => {
    if (hasUnsavedChanges && !readOnly) {
      setIsSaving(true);
      try {
        await broadcastSegmentUpdate(segmentId, {
          target_text: translation,
          updated_at: new Date().toISOString(),
          updated_by: currentUserId,
        });
        setLastSaved(translation);
        setHasUnsavedChanges(false);
        onSave?.(translation);
      } catch (error) {
        console.error('Failed to save translation:', error);
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      e.preventDefault();
      handleManualSave();
    }
  };

  const wordCount = translation.split(/\s+/).filter(w => w.length > 0).length;
  const charCount = translation.length;

  return (
    <Card className={cn('', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Translation Editor</CardTitle>
          <div className="flex items-center space-x-2">
            <UserPresence
              projectId={projectId}
              segmentId={segmentId}
              currentUserId={currentUserId!}
              maxVisible={3}
              showStatus={false}
            />
            {hasUnsavedChanges && (
              <Badge variant="outline" className="text-orange-600">
                Unsaved
              </Badge>
            )}
            {isSaving && (
              <Badge variant="outline" className="text-blue-600">
                Saving...
              </Badge>
            )}
            {!hasUnsavedChanges && !isSaving && lastSaved && (
              <Badge variant="outline" className="text-green-600">
                Saved
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Source Text */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Source Text
          </label>
          <div className="p-3 bg-gray-50 rounded-md border">
            <p className="text-gray-900">{sourceText}</p>
          </div>
        </div>

        {/* Translation Input */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700">
              Translation ({targetLanguage})
            </label>
            <div className="flex items-center space-x-2">
              {typingUsers.length > 0 && (
                <div className="flex items-center space-x-1 text-xs text-blue-600">
                  <Users className="h-3 w-3" />
                  <span>
                    {typingUsers.map(u => u.userName.split(' ')[0]).join(', ')} typing...
                  </span>
                </div>
              )}
            </div>
          </div>
          
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={translation}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              placeholder={`Enter ${targetLanguage} translation...`}
              readOnly={readOnly}
              className={cn(
                'min-h-[120px] resize-none',
                readOnly && 'bg-gray-50 cursor-not-allowed',
                hasUnsavedChanges && 'border-orange-300 focus:border-orange-500'
              )}
            />
            
            {/* Typing indicators overlay */}
            {typingUsers.length > 0 && (
              <div className="absolute bottom-2 right-2 flex space-x-1">
                {typingUsers.slice(0, 3).map((user, index) => (
                  <div
                    key={user.userId}
                    className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                    style={{ animationDelay: `${index * 200}ms` }}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <span>{charCount} characters</span>
            <span>{wordCount} words</span>
            {lastSaved && (
              <div className="flex items-center space-x-1">
                <Clock className="h-3 w-3" />
                <span>Last saved: {new Date(lastSaved).toLocaleTimeString()}</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <span>Ctrl+S to save</span>
            {hasUnsavedChanges && !readOnly && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleManualSave}
                disabled={isSaving}
                className="h-6 px-2 text-xs"
              >
                <Save className="h-3 w-3 mr-1" />
                Save
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
