'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useRealtime } from './RealtimeProvider';
import { broadcastComment } from '@/lib/supabase/realtime';
import { MessageSquare, Send, Reply, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Comment {
  id: string;
  content: string;
  type: 'comment' | 'issue' | 'suggestion';
  status?: 'open' | 'resolved';
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  replies?: Comment[];
  segmentId: string;
  projectId: string;
}

interface LiveCommentsProps {
  segmentId: string;
  projectId: string;
  initialComments?: Comment[];
  className?: string;
}

export function LiveComments({
  segmentId,
  projectId,
  initialComments = [],
  className = '',
}: LiveCommentsProps) {
  const { data: session } = useSession();
  const realtime = useRealtime();
  
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [newComment, setNewComment] = useState('');
  const [commentType, setCommentType] = useState<'comment' | 'issue' | 'suggestion'>('comment');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  
  const commentsEndRef = useRef<HTMLDivElement>(null);
  const currentUserId = session?.user?.id;

  // Set up realtime subscriptions
  useEffect(() => {
    if (!currentUserId) return;

    realtime.subscribeToSegment(segmentId, {
      onCommentAdded: (payload) => {
        if (payload.new && payload.new.segment_id === segmentId) {
          const newComment: Comment = {
            id: payload.new.id,
            content: payload.new.content,
            type: payload.new.type || 'comment',
            status: payload.new.status,
            createdAt: payload.new.created_at,
            updatedAt: payload.new.updated_at,
            author: {
              id: payload.new.author_id,
              name: payload.new.author_name || 'Unknown User',
              avatar: payload.new.author_avatar,
              role: payload.new.author_role || 'user',
            },
            segmentId: payload.new.segment_id,
            projectId: payload.new.project_id,
          };

          setComments(prev => {
            // Check if comment already exists (avoid duplicates)
            if (prev.some(c => c.id === newComment.id)) {
              return prev;
            }
            return [...prev, newComment];
          });
        }
      },
    });

    return () => {
      realtime.unsubscribeFromSegment(segmentId);
    };
  }, [segmentId, currentUserId, realtime]);

  // Auto-scroll to bottom when new comments are added
  useEffect(() => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [comments]);

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !currentUserId || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const commentData = {
        content: newComment.trim(),
        type: commentType,
        status: commentType === 'issue' ? 'open' : undefined,
        segment_id: segmentId,
        project_id: projectId,
        author_id: currentUserId,
        author_name: session?.user?.name || 'Unknown User',
        author_avatar: session?.user?.image,
        author_role: 'user', // In real app, get from user data
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      await broadcastComment(commentData);
      setNewComment('');
      setCommentType('comment');
    } catch (error) {
      console.error('Failed to submit comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitReply = async (parentId: string) => {
    if (!replyText.trim() || !currentUserId || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const replyData = {
        content: replyText.trim(),
        type: 'comment',
        segment_id: segmentId,
        project_id: projectId,
        parent_id: parentId,
        author_id: currentUserId,
        author_name: session?.user?.name || 'Unknown User',
        author_avatar: session?.user?.image,
        author_role: 'user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      await broadcastComment(replyData);
      setReplyText('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Failed to submit reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      action();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'issue': return 'bg-red-100 text-red-800';
      case 'suggestion': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'issue': return '⚠️';
      case 'suggestion': return '💡';
      default: return '💬';
    }
  };

  return (
    <Card className={cn('', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-lg">
          <MessageSquare className="mr-2 h-5 w-5" />
          Comments ({comments.length})
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Comments List */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {comments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No comments yet</p>
              <p className="text-xs">Start a conversation about this segment</p>
            </div>
          ) : (
            comments.map((comment) => (
              <div key={comment.id} className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={comment.author.avatar} />
                    <AvatarFallback className="text-xs">
                      {comment.author.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium">{comment.author.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {comment.author.role}
                      </Badge>
                      <Badge className={cn('text-xs', getTypeColor(comment.type))}>
                        {getTypeIcon(comment.type)} {comment.type}
                      </Badge>
                      {comment.status && (
                        <Badge variant="outline" className="text-xs">
                          {comment.status}
                        </Badge>
                      )}
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(comment.createdAt)}
                      </span>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-sm text-gray-900">{comment.content}</p>
                    </div>
                    
                    <div className="flex items-center space-x-2 mt-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setReplyingTo(comment.id)}
                        className="text-xs h-6 px-2"
                      >
                        <Reply className="h-3 w-3 mr-1" />
                        Reply
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs h-6 px-2"
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Reply Form */}
                    {replyingTo === comment.id && (
                      <div className="mt-3 ml-4 space-y-2">
                        <Textarea
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          placeholder="Write a reply..."
                          className="min-h-[60px] text-sm"
                          onKeyDown={(e) => handleKeyDown(e, () => handleSubmitReply(comment.id))}
                        />
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            onClick={() => handleSubmitReply(comment.id)}
                            disabled={!replyText.trim() || isSubmitting}
                          >
                            <Send className="h-3 w-3 mr-1" />
                            Reply
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setReplyingTo(null);
                              setReplyText('');
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Replies */}
                    {comment.replies && comment.replies.length > 0 && (
                      <div className="ml-4 mt-3 space-y-2 border-l-2 border-gray-200 pl-4">
                        {comment.replies.map((reply) => (
                          <div key={reply.id} className="flex items-start space-x-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={reply.author.avatar} />
                              <AvatarFallback className="text-xs">
                                {reply.author.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="text-xs font-medium">{reply.author.name}</span>
                                <span className="text-xs text-gray-500">
                                  {formatTimestamp(reply.createdAt)}
                                </span>
                              </div>
                              <div className="bg-white rounded p-2 border">
                                <p className="text-xs text-gray-900">{reply.content}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={commentsEndRef} />
        </div>

        {/* New Comment Form */}
        <div className="border-t pt-4 space-y-3">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Add comment:</span>
            <div className="flex space-x-1">
              {(['comment', 'issue', 'suggestion'] as const).map((type) => (
                <Button
                  key={type}
                  variant={commentType === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setCommentType(type)}
                  className="text-xs h-6 px-2"
                >
                  {getTypeIcon(type)} {type}
                </Button>
              ))}
            </div>
          </div>
          
          <Textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder={`Write a ${commentType}...`}
            className="min-h-[80px]"
            onKeyDown={(e) => handleKeyDown(e, handleSubmitComment)}
          />
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">
              Ctrl+Enter to submit
            </span>
            <Button
              onClick={handleSubmitComment}
              disabled={!newComment.trim() || isSubmitting}
              size="sm"
            >
              <Send className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
