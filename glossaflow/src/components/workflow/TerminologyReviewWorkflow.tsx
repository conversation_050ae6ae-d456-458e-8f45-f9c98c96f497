'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  User,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Edit
} from 'lucide-react';

interface TerminologyReviewWorkflowProps {
  projectId: string;
  onWorkflowComplete?: () => void;
  className?: string;
}

interface PendingTerm {
  id: string;
  sourceTerm: string;
  targetTerm: string;
  category: string;
  context?: string;
  usageNotes?: string;
  createdBy: string;
  createdAt: string;
  submissionNotes?: string;
  priority: 'high' | 'medium' | 'low';
}

interface ReviewAction {
  action: 'approve' | 'reject' | 'request_changes';
  notes?: string;
  suggestedChanges?: {
    targetTerm?: string;
    category?: string;
    usageNotes?: string;
  };
}

export function TerminologyReviewWorkflow({ 
  projectId, 
  onWorkflowComplete,
  className = '' 
}: TerminologyReviewWorkflowProps) {
  const [pendingTerms, setPendingTerms] = useState<PendingTerm[]>([]);
  const [currentTermIndex, setCurrentTermIndex] = useState(0);
  const [reviewNotes, setReviewNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [workflowStats, setWorkflowStats] = useState({
    total: 0,
    reviewed: 0,
    approved: 0,
    rejected: 0,
    pending: 0,
  });

  useEffect(() => {
    fetchPendingTerms();
  }, [projectId]);

  const fetchPendingTerms = async () => {
    // Mock data for demonstration
    const mockTerms: PendingTerm[] = [
      {
        id: '1',
        sourceTerm: 'magic sword',
        targetTerm: '魔法の剣',
        category: 'item',
        context: 'Legendary weapon used by the protagonist',
        usageNotes: 'Always capitalize when referring to the specific sword',
        createdBy: '<EMAIL>',
        createdAt: '2024-01-15T10:30:00Z',
        submissionNotes: 'This is a key item that appears throughout the series',
        priority: 'high',
      },
      {
        id: '2',
        sourceTerm: 'ancient temple',
        targetTerm: '古代神殿',
        category: 'location',
        context: 'Sacred place where the final battle occurs',
        createdBy: '<EMAIL>',
        createdAt: '2024-01-15T11:00:00Z',
        priority: 'medium',
      },
      {
        id: '3',
        sourceTerm: 'shadow magic',
        targetTerm: '影の魔法',
        category: 'concept',
        context: 'Dark magic used by antagonists',
        usageNotes: 'Distinguish from regular magic',
        createdBy: '<EMAIL>',
        createdAt: '2024-01-15T11:30:00Z',
        priority: 'medium',
      },
    ];

    setPendingTerms(mockTerms);
    setWorkflowStats({
      total: mockTerms.length,
      reviewed: 0,
      approved: 0,
      rejected: 0,
      pending: mockTerms.length,
    });
  };

  const handleReviewAction = async (action: ReviewAction) => {
    if (currentTermIndex >= pendingTerms.length) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update stats
      setWorkflowStats(prev => ({
        ...prev,
        reviewed: prev.reviewed + 1,
        approved: action.action === 'approve' ? prev.approved + 1 : prev.approved,
        rejected: action.action === 'reject' ? prev.rejected + 1 : prev.rejected,
        pending: prev.pending - 1,
      }));

      // Move to next term
      if (currentTermIndex < pendingTerms.length - 1) {
        setCurrentTermIndex(prev => prev + 1);
        setReviewNotes('');
      } else {
        // Workflow complete
        if (onWorkflowComplete) {
          onWorkflowComplete();
        }
      }
    } catch (error) {
      console.error('Failed to submit review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentTerm = pendingTerms[currentTermIndex];
  const isWorkflowComplete = currentTermIndex >= pendingTerms.length;
  const progress = pendingTerms.length > 0 ? (workflowStats.reviewed / pendingTerms.length) * 100 : 0;

  if (pendingTerms.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
            Terminology Review Complete
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              All terminology has been reviewed and approved. Translation work can begin.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isWorkflowComplete) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
            Review Workflow Complete
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Terminology review workflow has been completed. Translation can now begin.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{workflowStats.approved}</div>
              <div className="text-sm text-gray-600">Approved</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{workflowStats.rejected}</div>
              <div className="text-sm text-gray-600">Rejected</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{workflowStats.total}</div>
              <div className="text-sm text-gray-600">Total Reviewed</div>
            </div>
          </div>

          <Button onClick={onWorkflowComplete} className="w-full">
            Continue to Translation
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-blue-500" />
              Terminology Review Workflow
            </CardTitle>
            <Badge variant="outline">
              {currentTermIndex + 1} of {pendingTerms.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-xs text-gray-600">
              <span>{workflowStats.approved} approved</span>
              <span>{workflowStats.rejected} rejected</span>
              <span>{workflowStats.pending} pending</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Term Review */}
      {currentTerm && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Review Term</CardTitle>
              <div className="flex items-center space-x-2">
                <Badge 
                  variant={currentTerm.priority === 'high' ? 'destructive' : 
                          currentTerm.priority === 'medium' ? 'secondary' : 'outline'}
                >
                  {currentTerm.priority} priority
                </Badge>
                <Badge variant="outline">{currentTerm.category}</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Term Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Source Term</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                    <p className="font-medium">{currentTerm.sourceTerm}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Target Translation</label>
                  <div className="mt-1 p-3 bg-blue-50 rounded-lg">
                    <p className="font-medium text-blue-900">{currentTerm.targetTerm}</p>
                  </div>
                </div>

                {currentTerm.context && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Context</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm">{currentTerm.context}</p>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {currentTerm.usageNotes && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Usage Notes</label>
                    <div className="mt-1 p-3 bg-yellow-50 rounded-lg">
                      <p className="text-sm">{currentTerm.usageNotes}</p>
                    </div>
                  </div>
                )}

                {currentTerm.submissionNotes && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Submission Notes</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm">{currentTerm.submissionNotes}</p>
                    </div>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-700">Submitted By</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{currentTerm.createdBy}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Review Notes */}
            <div>
              <label className="text-sm font-medium text-gray-700">Review Notes (Optional)</label>
              <Textarea
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder="Add notes about your review decision..."
                className="mt-1"
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-4 border-t">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handleReviewAction({ 
                    action: 'request_changes', 
                    notes: reviewNotes 
                  })}
                  disabled={isSubmitting}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Request Changes
                </Button>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handleReviewAction({ 
                    action: 'reject', 
                    notes: reviewNotes 
                  })}
                  disabled={isSubmitting}
                >
                  <ThumbsDown className="mr-2 h-4 w-4" />
                  Reject
                </Button>
                <Button
                  onClick={() => handleReviewAction({ 
                    action: 'approve', 
                    notes: reviewNotes 
                  })}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <ThumbsUp className="mr-2 h-4 w-4" />
                  )}
                  Approve
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Workflow Instructions */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> All terminology must be reviewed and approved before translation work can begin. 
          This ensures consistency across the entire project series.
        </AlertDescription>
      </Alert>
    </div>
  );
}
