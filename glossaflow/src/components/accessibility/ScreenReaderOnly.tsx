interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  className?: string;
}

export function ScreenReaderOnly({ 
  children, 
  as: Component = 'span',
  className = '' 
}: ScreenReaderOnlyProps) {
  return (
    <Component 
      className={`sr-only ${className}`}
      aria-hidden="false"
    >
      {children}
    </Component>
  );
}

// Utility component for live regions
interface LiveRegionProps {
  children: React.ReactNode;
  priority?: 'polite' | 'assertive';
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
}

export function LiveRegion({ 
  children, 
  priority = 'polite',
  atomic = true,
  relevant = 'all'
}: LiveRegionProps) {
  return (
    <div
      aria-live={priority}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className="sr-only"
    >
      {children}
    </div>
  );
}

// Component for status messages
interface StatusMessageProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  visible?: boolean;
}

export function StatusMessage({ 
  message, 
  type = 'info',
  visible = true 
}: StatusMessageProps) {
  if (!visible) return null;

  const priority = type === 'error' ? 'assertive' : 'polite';

  return (
    <LiveRegion priority={priority}>
      <span role="status" aria-label={`${type}: ${message}`}>
        {message}
      </span>
    </LiveRegion>
  );
}
