'use client';

import { useState } from 'react';

interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

export function SkipLink({ href, children, className = '' }: SkipLinkProps) {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <a
      href={href}
      className={`
        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md
        transform transition-transform duration-200 ease-in-out
        focus:translate-y-0 focus:opacity-100
        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}
        ${className}
      `}
      onFocus={() => setIsVisible(true)}
      onBlur={() => setIsVisible(false)}
    >
      {children}
    </a>
  );
}

export function SkipLinks() {
  return (
    <>
      <SkipLink href="#main-content">Skip to main content</SkipLink>
      <SkipLink href="#navigation">Skip to navigation</SkipLink>
      <SkipLink href="#search">Skip to search</SkipLink>
    </>
  );
}
