'use client';

import { SessionProvider } from 'next-auth/react';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { useAppDispatch } from '@/lib/api/hooks';
import { setUser, setLoading } from '@/store/slices/authSlice';

interface AuthProviderProps {
  children: React.ReactNode;
}

function AuthStateManager({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (status === 'loading') {
      dispatch(setLoading(true));
    } else {
      dispatch(setLoading(false));
      
      if (session?.user) {
        dispatch(setUser({
          id: session.user.id || '',
          email: session.user.email || '',
          name: session.user.name || null,
          avatar_url: session.user.image || null,
          email_verified: true, // Assume verified if session exists
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }));
      } else {
        dispatch(setUser(null));
      }
    }
  }, [session, status, dispatch]);

  return <>{children}</>;
}

export function AuthProvider({ children }: AuthProviderProps) {
  return (
    <SessionProvider>
      <AuthStateManager>
        {children}
      </AuthStateManager>
    </SessionProvider>
  );
}
