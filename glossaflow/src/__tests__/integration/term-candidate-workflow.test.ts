import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMocks } from 'node-mocks-http';
import { getServerSession } from 'next-auth/next';
import { createClient } from '@supabase/supabase-js';

// Mock dependencies
vi.mock('next-auth/next');
vi.mock('@supabase/supabase-js');

// Import API handlers
import { POST as extractTerms } from '@/app/api/term-candidates/extract/route';
import { GET as getTermCandidates } from '@/app/api/term-candidates/route';
import { POST as approveCandidate } from '@/app/api/term-candidates/[id]/approve/route';
import { POST as rejectCandidate } from '@/app/api/term-candidates/[id]/reject/route';

const mockSupabaseClient = {
  from: vi.fn(),
  functions: {
    invoke: vi.fn(),
  },
};

const mockSession = {
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    organizationId: 'org-123',
  },
};

const mockProject = {
  id: 'project-123',
  name: 'Test Project',
  organization_id: 'org-123',
  source_language: 'en',
  target_languages: ['ja'],
};

const mockExtractedCandidates = [
  {
    id: 'candidate-1',
    project_id: 'project-123',
    candidate_term: 'machine learning',
    frequency: 5,
    confidence_score: 0.85,
    context_sentence: 'Machine learning is a subset of artificial intelligence.',
    status: 'pending',
    assigned_category: null,
    extraction_metadata: {
      tfidf_score: 0.75,
      pattern_type: 'technical_compound',
    },
    created_by: 'user-123',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    organization_id: 'org-123',
  },
  {
    id: 'candidate-2',
    project_id: 'project-123',
    candidate_term: 'neural network',
    frequency: 3,
    confidence_score: 0.72,
    context_sentence: 'Neural networks are inspired by biological neural networks.',
    status: 'pending',
    assigned_category: null,
    extraction_metadata: {
      tfidf_score: 0.68,
      pattern_type: 'adjective_noun',
    },
    created_by: 'user-123',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    organization_id: 'org-123',
  },
];

describe('Term Candidate Workflow Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getServerSession as any).mockResolvedValue(mockSession);
    (createClient as any).mockReturnValue(mockSupabaseClient);
  });

  describe('Complete Extraction to Approval Workflow', () => {
    it('should extract, review, and approve term candidates', async () => {
      // Step 1: Extract term candidates
      const mockProjectQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockProject,
          error: null,
        }),
      };

      const mockDeleteQuery = {
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockProjectQuery)
        .mockReturnValueOnce(mockDeleteQuery);

      mockSupabaseClient.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          candidates_extracted: 2,
          candidates_saved: 2,
          candidates: mockExtractedCandidates,
        },
        error: null,
      });

      const { req: extractReq } = createMocks({
        method: 'POST',
        body: {
          projectId: 'project-123',
          sourceText: 'Machine learning and neural networks are important AI technologies.',
          sourceLanguage: 'en',
          clearExisting: true,
        },
      });

      const extractResponse = await extractTerms(extractReq as any);
      const extractData = await extractResponse.json();

      expect(extractResponse.status).toBe(200);
      expect(extractData.success).toBe(true);
      expect(extractData.data.statistics.newCandidates).toBe(2);

      // Step 2: Get extracted candidates
      const mockCandidatesQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        range: vi.fn().mockResolvedValue({
          data: mockExtractedCandidates,
          error: null,
          count: 2,
        }),
      };

      mockSupabaseClient.from.mockReturnValue(mockCandidatesQuery);

      const { req: getReq } = createMocks({
        method: 'GET',
        url: '/api/term-candidates?status=pending&projectId=project-123',
      });

      const getResponse = await getTermCandidates(getReq as any);
      const getData = await getResponse.json();

      expect(getResponse.status).toBe(200);
      expect(getData.success).toBe(true);
      expect(getData.data.items).toHaveLength(2);
      expect(getData.data.items[0].candidateTerm).toBe('machine learning');

      // Step 3: Approve first candidate
      const mockCandidateQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            ...mockExtractedCandidates[0],
            project: mockProject,
          },
          error: null,
        }),
      };

      const mockUpdateCandidate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      };

      const mockCreateTerminology = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            id: 'term-123',
            source_term: 'machine learning',
            target_term: '機械学習',
            target_language: 'Japanese',
            category: 'technical',
            approval_status: 'approved',
            frequency: 5,
            created_by: 'user-123',
            project_id: 'project-123',
            organization_id: 'org-123',
          },
          error: null,
        }),
      };

      const mockUpdatedCandidate = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            ...mockExtractedCandidates[0],
            status: 'approved',
            reviewed_by: 'user-123',
            project: mockProject,
            reviewed_by_user: {
              id: 'user-123',
              name: 'Test User',
              email: '<EMAIL>',
            },
          },
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockCandidateQuery)
        .mockReturnValueOnce(mockUpdateCandidate)
        .mockReturnValueOnce(mockCreateTerminology)
        .mockReturnValueOnce(mockUpdatedCandidate);

      const { req: approveReq } = createMocks({
        method: 'POST',
        body: {
          targetTerm: '機械学習',
          targetLanguage: 'Japanese',
          category: 'technical',
          context: 'AI context',
          usageNotes: 'Use in technical documents',
          addToGlossary: false,
        },
      });

      const approveResponse = await approveCandidate(
        approveReq as any,
        { params: { id: 'candidate-1' } }
      );
      const approveData = await approveResponse.json();

      expect(approveResponse.status).toBe(200);
      expect(approveData.success).toBe(true);
      expect(approveData.data.candidate.status).toBe('approved');
      expect(approveData.data.terminologyEntry.target_term).toBe('機械学習');

      // Step 4: Reject second candidate
      const mockRejectCandidateQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            ...mockExtractedCandidates[1],
            project: mockProject,
          },
          error: null,
        }),
      };

      const mockRejectUpdate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            ...mockExtractedCandidates[1],
            status: 'rejected',
            reviewed_by: 'user-123',
            extraction_metadata: {
              ...mockExtractedCandidates[1].extraction_metadata,
              rejection_reason: 'Not relevant for this project',
            },
            project: mockProject,
            reviewed_by_user: {
              id: 'user-123',
              name: 'Test User',
              email: '<EMAIL>',
            },
          },
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockRejectCandidateQuery)
        .mockReturnValueOnce(mockRejectUpdate);

      const { req: rejectReq } = createMocks({
        method: 'POST',
        body: {
          reason: 'Not relevant for this project',
        },
      });

      const rejectResponse = await rejectCandidate(
        rejectReq as any,
        { params: { id: 'candidate-2' } }
      );
      const rejectData = await rejectResponse.json();

      expect(rejectResponse.status).toBe(200);
      expect(rejectData.success).toBe(true);
      expect(rejectData.data.status).toBe('rejected');
      expect(rejectData.data.extractionMetadata.rejection_reason).toBe('Not relevant for this project');

      // Verify all API calls were made correctly
      expect(mockSupabaseClient.functions.invoke).toHaveBeenCalledWith(
        'extract-term-candidates',
        expect.objectContaining({
          body: expect.objectContaining({
            project_id: 'project-123',
            source_text: 'Machine learning and neural networks are important AI technologies.',
          }),
        })
      );

      expect(mockCreateTerminology.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          source_term: 'machine learning',
          target_term: '機械学習',
          target_language: 'Japanese',
          category: 'technical',
          approval_status: 'approved',
        })
      );
    });

    it('should handle extraction errors gracefully', async () => {
      const mockProjectQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockProject,
          error: null,
        }),
      };

      mockSupabaseClient.from.mockReturnValue(mockProjectQuery);

      // Mock extraction function error
      mockSupabaseClient.functions.invoke.mockResolvedValue({
        data: null,
        error: {
          message: 'Extraction failed due to invalid input',
        },
      });

      const { req } = createMocks({
        method: 'POST',
        body: {
          projectId: 'project-123',
          sourceText: 'Short text',
          sourceLanguage: 'en',
        },
      });

      const response = await extractTerms(req as any);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to extract term candidates');
    });

    it('should validate project access during extraction', async () => {
      const mockProjectQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Project not found' },
        }),
      };

      mockSupabaseClient.from.mockReturnValue(mockProjectQuery);

      const { req } = createMocks({
        method: 'POST',
        body: {
          projectId: 'invalid-project',
          sourceText: 'Some text to extract from',
          sourceLanguage: 'en',
        },
      });

      const response = await extractTerms(req as any);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Project not found or access denied');
    });

    it('should handle approval failures and rollback candidate status', async () => {
      const mockCandidateQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            ...mockExtractedCandidates[0],
            project: mockProject,
          },
          error: null,
        }),
      };

      const mockUpdateCandidate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      };

      const mockCreateTerminology = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Failed to create terminology entry' },
        }),
      };

      const mockRollbackUpdate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockCandidateQuery)
        .mockReturnValueOnce(mockUpdateCandidate)
        .mockReturnValueOnce(mockCreateTerminology)
        .mockReturnValueOnce(mockRollbackUpdate);

      const { req } = createMocks({
        method: 'POST',
        body: {
          targetTerm: '機械学習',
          targetLanguage: 'Japanese',
          category: 'technical',
        },
      });

      const response = await approveCandidate(
        req as any,
        { params: { id: 'candidate-1' } }
      );
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to approve term candidate');

      // Verify rollback was attempted
      expect(mockRollbackUpdate.update).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'pending',
          reviewed_by: null,
        })
      );
    });
  });
});
