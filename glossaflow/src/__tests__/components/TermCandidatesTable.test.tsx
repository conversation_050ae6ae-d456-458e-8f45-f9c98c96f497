import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { TermCandidatesTable } from '@/components/term-candidates/TermCandidatesTable';
import { baseApi } from '@/lib/api/base';

// Mock data
const mockCandidates = [
  {
    id: 'candidate-1',
    projectId: 'project-1',
    candidateTerm: 'machine learning',
    frequency: 5,
    confidenceScore: 0.85,
    contextSentence: 'Machine learning is a subset of artificial intelligence.',
    status: 'pending' as const,
    assignedCategory: null,
    extractionMetadata: {
      tfidf_score: 0.75,
      pattern_type: 'technical_compound',
      pos_tags: ['NN', 'NN'],
      sentence_position: 0,
    },
    createdBy: 'user-1',
    reviewedBy: null,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    organizationId: 'org-1',
    project: {
      id: 'project-1',
      name: 'AI Documentation',
      source_language: 'en',
      target_languages: ['ja', 'es'],
    },
  },
  {
    id: 'candidate-2',
    projectId: 'project-1',
    candidateTerm: 'neural network',
    frequency: 3,
    confidenceScore: 0.72,
    contextSentence: 'Neural networks are inspired by biological neural networks.',
    status: 'approved' as const,
    assignedCategory: 'technical',
    extractionMetadata: {
      tfidf_score: 0.68,
      pattern_type: 'adjective_noun',
      pos_tags: ['JJ', 'NN'],
      sentence_position: 1,
    },
    createdBy: 'user-1',
    reviewedBy: 'user-2',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T01:00:00Z',
    organizationId: 'org-1',
    project: {
      id: 'project-1',
      name: 'AI Documentation',
      source_language: 'en',
      target_languages: ['ja', 'es'],
    },
  },
];

// Mock handlers
const mockOnApprove = vi.fn();
const mockOnReject = vi.fn();
const mockOnDelete = vi.fn();
const mockOnAssignCategory = vi.fn();

// Create mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      [baseApi.reducerPath]: baseApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(baseApi.middleware),
  });
};

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('TermCandidatesTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders term candidates correctly', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Check if candidates are displayed
    expect(screen.getByText('machine learning')).toBeInTheDocument();
    expect(screen.getByText('neural network')).toBeInTheDocument();
    
    // Check frequency display
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    
    // Check confidence scores
    expect(screen.getByText('85%')).toBeInTheDocument();
    expect(screen.getByText('72%')).toBeInTheDocument();
  });

  it('displays confidence badges correctly', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // High confidence (>= 0.8)
    expect(screen.getByText('High')).toBeInTheDocument();
    
    // Medium confidence (>= 0.5 && < 0.8)
    expect(screen.getByText('Medium')).toBeInTheDocument();
  });

  it('displays status badges correctly', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Approved')).toBeInTheDocument();
  });

  it('shows context sentences with truncation', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    expect(screen.getByText(/Machine learning is a subset/)).toBeInTheDocument();
    expect(screen.getByText(/Neural networks are inspired/)).toBeInTheDocument();
  });

  it('displays project information', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    expect(screen.getAllByText('AI Documentation')).toHaveLength(2);
    expect(screen.getAllByText('en')).toHaveLength(2);
  });

  it('shows assign category dropdown for unassigned candidates', async () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Find the assign button for the first candidate (unassigned)
    const assignButtons = screen.getAllByText('Assign');
    expect(assignButtons).toHaveLength(1);

    // Click the assign button
    fireEvent.click(assignButtons[0]);

    // Check if dropdown options appear
    await waitFor(() => {
      expect(screen.getByText('Technical')).toBeInTheDocument();
      expect(screen.getByText('General')).toBeInTheDocument();
    });
  });

  it('shows assigned category badge for assigned candidates', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    expect(screen.getByText('technical')).toBeInTheDocument();
  });

  it('calls onAssignCategory when category is selected', async () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Click assign button
    const assignButton = screen.getByText('Assign');
    fireEvent.click(assignButton);

    // Select a category
    await waitFor(() => {
      const technicalOption = screen.getByText('Technical');
      fireEvent.click(technicalOption);
    });

    expect(mockOnAssignCategory).toHaveBeenCalledWith('candidate-1', 'technical');
  });

  it('shows action buttons for pending candidates', async () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Find action menu buttons
    const actionButtons = screen.getAllByRole('button');
    const menuButtons = actionButtons.filter(button => 
      button.querySelector('svg') && 
      button.getAttribute('aria-haspopup') === 'menu'
    );

    // Click the first action menu (for pending candidate)
    fireEvent.click(menuButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Approve')).toBeInTheDocument();
      expect(screen.getByText('Reject')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });
  });

  it('calls onApprove when approve is clicked', async () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Find and click action menu for pending candidate
    const actionButtons = screen.getAllByRole('button');
    const menuButtons = actionButtons.filter(button => 
      button.querySelector('svg') && 
      button.getAttribute('aria-haspopup') === 'menu'
    );

    fireEvent.click(menuButtons[0]);

    await waitFor(() => {
      const approveButton = screen.getByText('Approve');
      fireEvent.click(approveButton);
    });

    expect(mockOnApprove).toHaveBeenCalledWith(mockCandidates[0]);
  });

  it('calls onReject when reject is clicked', async () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Find and click action menu for pending candidate
    const actionButtons = screen.getAllByRole('button');
    const menuButtons = actionButtons.filter(button => 
      button.querySelector('svg') && 
      button.getAttribute('aria-haspopup') === 'menu'
    );

    fireEvent.click(menuButtons[0]);

    await waitFor(() => {
      const rejectButton = screen.getByText('Reject');
      fireEvent.click(rejectButton);
    });

    expect(mockOnReject).toHaveBeenCalledWith('candidate-1');
  });

  it('calls onDelete when delete is clicked', async () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Find and click action menu
    const actionButtons = screen.getAllByRole('button');
    const menuButtons = actionButtons.filter(button => 
      button.querySelector('svg') && 
      button.getAttribute('aria-haspopup') === 'menu'
    );

    fireEvent.click(menuButtons[0]);

    await waitFor(() => {
      const deleteButton = screen.getByText('Delete');
      fireEvent.click(deleteButton);
    });

    expect(mockOnDelete).toHaveBeenCalledWith(mockCandidates[0]);
  });

  it('formats dates correctly', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    // Check if dates are formatted (should show "Jan 1, 2024")
    expect(screen.getAllByText(/Jan 1, 2024/)).toHaveLength(2);
  });

  it('shows extraction metadata', () => {
    renderWithProvider(
      <TermCandidatesTable
        candidates={mockCandidates}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    expect(screen.getByText('technical_compound')).toBeInTheDocument();
    expect(screen.getByText('adjective_noun')).toBeInTheDocument();
  });

  it('returns null when no candidates provided', () => {
    const { container } = renderWithProvider(
      <TermCandidatesTable
        candidates={[]}
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        onDelete={mockOnDelete}
        onAssignCategory={mockOnAssignCategory}
      />
    );

    expect(container.firstChild).toBeNull();
  });
});
