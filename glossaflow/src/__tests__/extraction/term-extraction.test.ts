import { describe, it, expect } from 'vitest';

// Mock implementation of the extraction classes for testing
class TFIDFCalculator {
  private documents: string[] = [];
  private vocabulary: Set<string> = new Set();
  private termFrequency: Map<string, Map<string, number>> = new Map();
  private documentFrequency: Map<string, number> = new Map();

  constructor(documents: string[]) {
    this.documents = documents;
    this.buildVocabulary();
    this.calculateTermFrequencies();
    this.calculateDocumentFrequencies();
  }

  private buildVocabulary() {
    this.documents.forEach(doc => {
      const terms = this.tokenize(doc);
      terms.forEach(term => this.vocabulary.add(term));
    });
  }

  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2 && !this.isStopWord(term));
  }

  private isStopWord(term: string): boolean {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
    ]);
    return stopWords.has(term);
  }

  private calculateTermFrequencies() {
    this.documents.forEach((doc, docIndex) => {
      const terms = this.tokenize(doc);
      const termCount = new Map<string, number>();
      
      terms.forEach(term => {
        termCount.set(term, (termCount.get(term) || 0) + 1);
      });

      this.termFrequency.set(docIndex.toString(), termCount);
    });
  }

  private calculateDocumentFrequencies() {
    this.vocabulary.forEach(term => {
      let docCount = 0;
      this.documents.forEach((doc, docIndex) => {
        const termCount = this.termFrequency.get(docIndex.toString());
        if (termCount && termCount.has(term)) {
          docCount++;
        }
      });
      this.documentFrequency.set(term, docCount);
    });
  }

  calculateTFIDF(term: string, docIndex: number): number {
    const termCount = this.termFrequency.get(docIndex.toString());
    if (!termCount || !termCount.has(term)) return 0;

    const tf = termCount.get(term)! / Array.from(termCount.values()).reduce((a, b) => a + b, 0);
    const df = this.documentFrequency.get(term) || 0;
    const idf = df > 0 ? Math.log(this.documents.length / df) : 0;

    return tf * idf;
  }

  getTopTerms(docIndex: number, limit: number = 50): Array<{term: string, score: number}> {
    const scores: Array<{term: string, score: number}> = [];
    
    this.vocabulary.forEach(term => {
      const score = this.calculateTFIDF(term, docIndex);
      if (score > 0) {
        scores.push({ term, score });
      }
    });

    return scores
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }
}

class LinguisticPatternExtractor {
  private patterns = [
    { regex: /\b([A-Z][a-z]+)\s+([A-Z][a-z]+)\b/g, type: 'proper_noun_phrase' },
    { regex: /\b([a-z]+(?:ive|al|ic|ous|ful|less|able))\s+([a-z]+(?:tion|sion|ment|ness|ity|ism))\b/gi, type: 'adjective_noun' },
    { regex: /\b([a-z]+)\s+([a-z]+)\s+(system|process|method|approach|technique|algorithm|framework|model|protocol)\b/gi, type: 'technical_compound' },
    { regex: /\b([a-z]+)\s+(interface|component|module|service|engine|manager|controller|handler)\b/gi, type: 'software_term' },
  ];

  extractPatterns(text: string): Array<{term: string, type: string, context: string}> {
    const results: Array<{term: string, type: string, context: string}> = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    sentences.forEach(sentence => {
      this.patterns.forEach(pattern => {
        let match;
        while ((match = pattern.regex.exec(sentence)) !== null) {
          const term = match[0].trim();
          if (term.length > 3 && term.length < 50) {
            results.push({
              term: term.toLowerCase(),
              type: pattern.type,
              context: sentence.trim()
            });
          }
        }
        pattern.regex.lastIndex = 0;
      });
    });

    return results;
  }
}

interface TermCandidate {
  candidate_term: string;
  frequency: number;
  confidence_score: number;
  context_sentence: string;
  extraction_metadata: {
    tfidf_score: number;
    pattern_type: string;
    pos_tags: string[];
    sentence_position: number;
  };
}

async function extractTermCandidates(sourceText: string): Promise<TermCandidate[]> {
  const sentences = sourceText.split(/[.!?]+/).filter(s => s.trim().length > 10);
  
  const tfidfCalculator = new TFIDFCalculator([sourceText]);
  const patternExtractor = new LinguisticPatternExtractor();
  
  const tfidfTerms = tfidfCalculator.getTopTerms(0, 100);
  const patternTerms = patternExtractor.extractPatterns(sourceText);
  
  const candidateMap = new Map<string, TermCandidate>();
  
  // Process TF-IDF terms
  tfidfTerms.forEach(({ term, score }) => {
    const frequency = (sourceText.toLowerCase().match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
    const contextSentence = sentences.find(s => s.toLowerCase().includes(term)) || '';
    
    candidateMap.set(term, {
      candidate_term: term,
      frequency,
      confidence_score: Math.min(score * 0.7, 1.0),
      context_sentence: contextSentence.substring(0, 500),
      extraction_metadata: {
        tfidf_score: score,
        pattern_type: 'tfidf',
        pos_tags: [],
        sentence_position: sentences.findIndex(s => s.toLowerCase().includes(term))
      }
    });
  });
  
  // Process pattern-based terms
  patternTerms.forEach(({ term, type, context }) => {
    const frequency = (sourceText.toLowerCase().match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
    const existing = candidateMap.get(term);
    
    if (existing) {
      existing.confidence_score = Math.min(existing.confidence_score + 0.3, 1.0);
      existing.extraction_metadata.pattern_type = `${existing.extraction_metadata.pattern_type},${type}`;
    } else {
      candidateMap.set(term, {
        candidate_term: term,
        frequency,
        confidence_score: 0.6,
        context_sentence: context.substring(0, 500),
        extraction_metadata: {
          tfidf_score: 0,
          pattern_type: type,
          pos_tags: [],
          sentence_position: sentences.findIndex(s => s.toLowerCase().includes(term))
        }
      });
    }
  });
  
  return Array.from(candidateMap.values())
    .filter(candidate => 
      candidate.frequency >= 2 && 
      candidate.confidence_score >= 0.4 &&
      candidate.candidate_term.length >= 3 &&
      candidate.candidate_term.length <= 50
    )
    .sort((a, b) => b.confidence_score - a.confidence_score)
    .slice(0, 50);
}

describe('Term Extraction Algorithm', () => {
  describe('TFIDFCalculator', () => {
    it('should calculate TF-IDF scores correctly', () => {
      const documents = [
        'Machine learning is a subset of artificial intelligence.',
        'Deep learning uses neural networks for pattern recognition.',
        'Natural language processing involves computational linguistics.'
      ];
      
      const calculator = new TFIDFCalculator(documents);
      const score = calculator.calculateTFIDF('machine', 0);
      
      expect(score).toBeGreaterThan(0);
      expect(typeof score).toBe('number');
    });

    it('should return top terms sorted by score', () => {
      const documents = [
        'Machine learning algorithms process large datasets. Machine learning requires computational power.'
      ];
      
      const calculator = new TFIDFCalculator(documents);
      const topTerms = calculator.getTopTerms(0, 5);
      
      expect(topTerms).toHaveLength(5);
      expect(topTerms[0].score).toBeGreaterThanOrEqual(topTerms[1].score);
      expect(topTerms.some(t => t.term === 'machine')).toBe(true);
    });

    it('should filter out stop words', () => {
      const documents = ['The machine learning algorithm is very powerful.'];
      
      const calculator = new TFIDFCalculator(documents);
      const topTerms = calculator.getTopTerms(0, 10);
      
      const termNames = topTerms.map(t => t.term);
      expect(termNames).not.toContain('the');
      expect(termNames).not.toContain('is');
      expect(termNames).not.toContain('very');
    });
  });

  describe('LinguisticPatternExtractor', () => {
    it('should extract proper noun phrases', () => {
      const text = 'Google Cloud Platform provides machine learning services.';
      const extractor = new LinguisticPatternExtractor();
      const patterns = extractor.extractPatterns(text);
      
      const properNouns = patterns.filter(p => p.type === 'proper_noun_phrase');
      expect(properNouns.some(p => p.term.includes('google cloud'))).toBe(true);
    });

    it('should extract technical compound terms', () => {
      const text = 'The neural network algorithm processes data efficiently.';
      const extractor = new LinguisticPatternExtractor();
      const patterns = extractor.extractPatterns(text);
      
      const technicalTerms = patterns.filter(p => p.type === 'technical_compound');
      expect(technicalTerms.some(p => p.term.includes('algorithm'))).toBe(true);
    });

    it('should extract software terms', () => {
      const text = 'The user interface component handles user interactions.';
      const extractor = new LinguisticPatternExtractor();
      const patterns = extractor.extractPatterns(text);
      
      const softwareTerms = patterns.filter(p => p.type === 'software_term');
      expect(softwareTerms.some(p => p.term.includes('interface'))).toBe(true);
    });

    it('should provide context for extracted terms', () => {
      const text = 'Machine learning algorithms are powerful. They process data.';
      const extractor = new LinguisticPatternExtractor();
      const patterns = extractor.extractPatterns(text);
      
      patterns.forEach(pattern => {
        expect(pattern.context).toBeTruthy();
        expect(pattern.context.length).toBeGreaterThan(0);
      });
    });
  });

  describe('extractTermCandidates', () => {
    it('should extract term candidates from technical text', async () => {
      const sourceText = `
        Machine learning is a powerful subset of artificial intelligence that enables computers to learn and improve from experience.
        Deep learning algorithms use neural networks to process complex data patterns.
        Natural language processing involves computational linguistics and machine learning techniques.
        The user interface component provides an intuitive experience for data scientists.
      `;
      
      const candidates = await extractTermCandidates(sourceText);
      
      expect(candidates.length).toBeGreaterThan(0);
      expect(candidates.some(c => c.candidate_term.includes('machine'))).toBe(true);
      expect(candidates.some(c => c.candidate_term.includes('learning'))).toBe(true);
    });

    it('should assign confidence scores correctly', async () => {
      const sourceText = `
        Machine learning machine learning machine learning is important.
        Deep learning is also important.
        Random word appears once.
      `;
      
      const candidates = await extractTermCandidates(sourceText);
      
      // Terms that appear more frequently should have higher confidence
      const machineLearning = candidates.find(c => c.candidate_term.includes('machine'));
      const randomWord = candidates.find(c => c.candidate_term.includes('random'));
      
      if (machineLearning && randomWord) {
        expect(machineLearning.confidence_score).toBeGreaterThan(randomWord.confidence_score);
      }
    });

    it('should filter out low-quality candidates', async () => {
      const sourceText = `
        A b c d e f g h i j k l m n o p q r s t u v w x y z.
        Machine learning algorithms are sophisticated and powerful tools.
      `;
      
      const candidates = await extractTermCandidates(sourceText);
      
      // Should not include single letters or very short terms
      expect(candidates.every(c => c.candidate_term.length >= 3)).toBe(true);
      
      // Should not include terms with very low frequency
      expect(candidates.every(c => c.frequency >= 2)).toBe(true);
      
      // Should not include terms with very low confidence
      expect(candidates.every(c => c.confidence_score >= 0.4)).toBe(true);
    });

    it('should provide extraction metadata', async () => {
      const sourceText = `
        Machine learning algorithms process data efficiently.
        The neural network framework provides excellent performance.
      `;
      
      const candidates = await extractTermCandidates(sourceText);
      
      candidates.forEach(candidate => {
        expect(candidate.extraction_metadata).toBeDefined();
        expect(candidate.extraction_metadata.pattern_type).toBeTruthy();
        expect(typeof candidate.extraction_metadata.sentence_position).toBe('number');
      });
    });

    it('should boost confidence for terms found by multiple methods', async () => {
      const sourceText = `
        Machine learning machine learning is a powerful algorithm.
        The machine learning framework provides excellent results.
      `;
      
      const candidates = await extractTermCandidates(sourceText);
      
      // Terms found by both TF-IDF and pattern matching should have boosted confidence
      const machineLearning = candidates.find(c => 
        c.candidate_term.includes('machine') && 
        c.extraction_metadata.pattern_type.includes(',')
      );
      
      if (machineLearning) {
        expect(machineLearning.confidence_score).toBeGreaterThan(0.7);
      }
    });

    it('should limit results to top candidates', async () => {
      const sourceText = Array(100).fill('unique term').map((term, i) => 
        `${term}${i} appears in this sentence.`
      ).join(' ');
      
      const candidates = await extractTermCandidates(sourceText);
      
      expect(candidates.length).toBeLessThanOrEqual(50);
    });
  });
});
