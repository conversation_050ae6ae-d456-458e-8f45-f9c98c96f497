import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMocks } from 'node-mocks-http';
import { getServerSession } from 'next-auth/next';
import { createClient } from '@supabase/supabase-js';

// Mock dependencies
vi.mock('next-auth/next');
vi.mock('@supabase/supabase-js');

// Import handlers after mocking
import { GET, POST } from '@/app/api/term-candidates/route';
import { GET as getCandidate, PUT, DELETE } from '@/app/api/term-candidates/[id]/route';
import { POST as approveCandidate } from '@/app/api/term-candidates/[id]/approve/route';
import { POST as rejectCandidate } from '@/app/api/term-candidates/[id]/reject/route';

const mockSupabaseClient = {
  from: vi.fn(),
  functions: {
    invoke: vi.fn(),
  },
};

const mockSession = {
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    organizationId: 'org-123',
  },
};

const mockTermCandidate = {
  id: 'candidate-123',
  project_id: 'project-123',
  candidate_term: 'machine learning',
  frequency: 5,
  confidence_score: 0.85,
  context_sentence: 'Machine learning is a subset of artificial intelligence.',
  status: 'pending',
  assigned_category: null,
  extraction_metadata: {
    tfidf_score: 0.75,
    pattern_type: 'technical_compound',
    pos_tags: ['NN', 'NN'],
    sentence_position: 0,
  },
  created_by: 'user-123',
  reviewed_by: null,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  organization_id: 'org-123',
};

describe('/api/term-candidates', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getServerSession as any).mockResolvedValue(mockSession);
    (createClient as any).mockReturnValue(mockSupabaseClient);
  });

  describe('GET /api/term-candidates', () => {
    it('should return paginated term candidates', async () => {
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        or: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        range: vi.fn().mockResolvedValue({
          data: [mockTermCandidate],
          error: null,
          count: 1,
        }),
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/term-candidates?page=1&limit=10&status=pending',
      });

      const response = await GET(req as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.items).toHaveLength(1);
      expect(data.data.items[0].candidateTerm).toBe('machine learning');
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('term_candidates');
    });

    it('should handle search queries', async () => {
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        or: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        range: vi.fn().mockResolvedValue({
          data: [],
          error: null,
          count: 0,
        }),
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/term-candidates?search=machine',
      });

      const response = await GET(req as any);

      expect(mockQuery.or).toHaveBeenCalledWith(
        'candidate_term.ilike.%machine%,context_sentence.ilike.%machine%'
      );
    });

    it('should return 401 for unauthenticated requests', async () => {
      (getServerSession as any).mockResolvedValue(null);

      const { req } = createMocks({
        method: 'GET',
        url: '/api/term-candidates',
      });

      const response = await GET(req as any);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('POST /api/term-candidates', () => {
    it('should create a new term candidate', async () => {
      const mockProject = {
        id: 'project-123',
        organization_id: 'org-123',
      };

      const mockInsert = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockTermCandidate,
          error: null,
        }),
      };

      const mockProjectQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockProject,
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockProjectQuery) // First call for project verification
        .mockReturnValueOnce(mockInsert); // Second call for insertion

      const { req } = createMocks({
        method: 'POST',
        body: {
          projectId: 'project-123',
          candidateTerm: 'machine learning',
          frequency: 5,
          confidenceScore: 0.85,
          contextSentence: 'Machine learning is a subset of artificial intelligence.',
        },
      });

      const response = await POST(req as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.candidateTerm).toBe('machine learning');
      expect(mockInsert.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          project_id: 'project-123',
          candidate_term: 'machine learning',
          frequency: 5,
          confidence_score: 0.85,
          created_by: 'user-123',
          status: 'pending',
        })
      );
    });

    it('should return 400 for missing required fields', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          candidateTerm: 'machine learning',
          // Missing projectId
        },
      });

      const response = await POST(req as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Missing required fields: projectId, candidateTerm');
    });
  });

  describe('PUT /api/term-candidates/[id]', () => {
    it('should update term candidate status', async () => {
      const mockUpdate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { ...mockTermCandidate, status: 'approved' },
          error: null,
        }),
      };

      const mockFetch = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { id: 'candidate-123', project_id: 'project-123' },
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockFetch)
        .mockReturnValueOnce(mockUpdate);

      const { req } = createMocks({
        method: 'PUT',
        body: {
          status: 'approved',
        },
      });

      const response = await PUT(req as any, { params: { id: 'candidate-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe('approved');
      expect(mockUpdate.update).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'approved',
          reviewed_by: 'user-123',
        })
      );
    });
  });

  describe('POST /api/term-candidates/[id]/approve', () => {
    it('should approve candidate and create terminology entry', async () => {
      const mockCandidate = {
        ...mockTermCandidate,
        project: {
          id: 'project-123',
          name: 'Test Project',
          source_language: 'en',
          target_languages: ['ja'],
          organization_id: 'org-123',
        },
      };

      const mockCandidateQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockCandidate,
          error: null,
        }),
      };

      const mockUpdateCandidate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      };

      const mockCreateTerminology = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            id: 'term-123',
            source_term: 'machine learning',
            target_term: '機械学習',
            target_language: 'Japanese',
            category: 'technical',
            approval_status: 'approved',
          },
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockCandidateQuery)
        .mockReturnValueOnce(mockUpdateCandidate)
        .mockReturnValueOnce(mockCreateTerminology);

      const { req } = createMocks({
        method: 'POST',
        body: {
          targetTerm: '機械学習',
          targetLanguage: 'Japanese',
          category: 'technical',
          context: 'AI context',
          usageNotes: 'Use in technical documents',
        },
      });

      const response = await approveCandidate(req as any, { params: { id: 'candidate-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.terminologyEntry.target_term).toBe('機械学習');
      expect(mockCreateTerminology.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          source_term: 'machine learning',
          target_term: '機械学習',
          target_language: 'Japanese',
          category: 'technical',
          approval_status: 'approved',
        })
      );
    });
  });

  describe('POST /api/term-candidates/[id]/reject', () => {
    it('should reject candidate with reason', async () => {
      const mockCandidate = {
        ...mockTermCandidate,
        project: {
          id: 'project-123',
          name: 'Test Project',
        },
      };

      const mockCandidateQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockCandidate,
          error: null,
        }),
      };

      const mockUpdate = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            ...mockCandidate,
            status: 'rejected',
            extraction_metadata: {
              ...mockCandidate.extraction_metadata,
              rejection_reason: 'Not relevant',
            },
          },
          error: null,
        }),
      };

      mockSupabaseClient.from
        .mockReturnValueOnce(mockCandidateQuery)
        .mockReturnValueOnce(mockUpdate);

      const { req } = createMocks({
        method: 'POST',
        body: {
          reason: 'Not relevant',
        },
      });

      const response = await rejectCandidate(req as any, { params: { id: 'candidate-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe('rejected');
      expect(mockUpdate.update).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'rejected',
          reviewed_by: 'user-123',
          extraction_metadata: expect.objectContaining({
            rejection_reason: 'Not relevant',
          }),
        })
      );
    });
  });
});
