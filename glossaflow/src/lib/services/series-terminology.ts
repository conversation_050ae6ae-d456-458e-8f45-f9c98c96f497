/**
 * Series Terminology Management
 * Handles terminology consistency across multiple projects in a series
 */

export interface SeriesTerminology {
  id: string;
  seriesId: string;
  sourceTerm: string;
  targetTerm: string;
  targetLanguage: string;
  category: string;
  context?: string;
  usageNotes?: string;
  frequency: number;
  approvalStatus: 'approved' | 'pending' | 'rejected';
  createdBy: string;
  reviewedBy?: string;
  lastUsed?: string;
  projectUsage: ProjectUsage[];
  createdAt: string;
  updatedAt: string;
}

export interface ProjectUsage {
  projectId: string;
  projectTitle: string;
  frequency: number;
  lastUsed: string;
  consistency: number; // 0-1 score
}

export interface SeriesConsistencyReport {
  seriesId: string;
  overallScore: number;
  totalTerms: number;
  consistentTerms: number;
  inconsistentTerms: number;
  projectReports: ProjectConsistencyReport[];
  recommendations: ConsistencyRecommendation[];
}

export interface ProjectConsistencyReport {
  projectId: string;
  projectTitle: string;
  score: number;
  termCount: number;
  issues: ConsistencyIssue[];
}

export interface ConsistencyIssue {
  type: 'missing_term' | 'different_translation' | 'inconsistent_usage';
  severity: 'high' | 'medium' | 'low';
  sourceTerm: string;
  expectedTerm: string;
  actualTerm?: string;
  projectId: string;
  frequency: number;
}

export interface ConsistencyRecommendation {
  type: 'standardize_term' | 'add_missing_term' | 'review_translation';
  priority: 'high' | 'medium' | 'low';
  description: string;
  affectedProjects: string[];
  sourceTerm: string;
  recommendedTerm: string;
  impact: number; // Number of affected instances
}

export class SeriesTerminologyManager {
  private seriesId: string;

  constructor(seriesId: string) {
    this.seriesId = seriesId;
  }

  /**
   * Get all terminology for a series
   */
  async getSeriesTerminology(targetLanguage?: string): Promise<SeriesTerminology[]> {
    // This would typically make an API call
    // For now, return mock data structure
    return [];
  }

  /**
   * Sync terminology from a master project to other projects in the series
   */
  async syncTerminologyAcrossSeries(
    masterProjectId: string,
    targetProjectIds: string[],
    options: {
      overwriteExisting?: boolean;
      onlyApproved?: boolean;
      categories?: string[];
    } = {}
  ): Promise<{
    synced: number;
    skipped: number;
    conflicts: Array<{
      term: string;
      masterTranslation: string;
      existingTranslation: string;
      projectId: string;
    }>;
  }> {
    const { overwriteExisting = false, onlyApproved = true, categories } = options;
    
    // Implementation would:
    // 1. Fetch terminology from master project
    // 2. For each target project, check for conflicts
    // 3. Apply terminology based on options
    // 4. Return sync results
    
    return {
      synced: 0,
      skipped: 0,
      conflicts: [],
    };
  }

  /**
   * Generate consistency report for the entire series
   */
  async generateConsistencyReport(): Promise<SeriesConsistencyReport> {
    // Implementation would:
    // 1. Fetch all projects in the series
    // 2. Analyze terminology usage across projects
    // 3. Identify inconsistencies and missing terms
    // 4. Calculate consistency scores
    // 5. Generate recommendations
    
    return {
      seriesId: this.seriesId,
      overallScore: 0.85,
      totalTerms: 0,
      consistentTerms: 0,
      inconsistentTerms: 0,
      projectReports: [],
      recommendations: [],
    };
  }

  /**
   * Find terminology conflicts between projects
   */
  async findTerminologyConflicts(projectIds: string[]): Promise<Array<{
    sourceTerm: string;
    translations: Array<{
      projectId: string;
      projectTitle: string;
      translation: string;
      frequency: number;
    }>;
    recommendedTranslation: string;
    confidence: number;
  }>> {
    // Implementation would analyze terminology across projects
    // and identify where the same source term has different translations
    return [];
  }

  /**
   * Propagate terminology changes across series
   */
  async propagateTerminologyChange(
    terminologyId: string,
    newTranslation: string,
    targetProjectIds?: string[]
  ): Promise<{
    updated: number;
    failed: number;
    errors: Array<{
      projectId: string;
      error: string;
    }>;
  }> {
    // Implementation would:
    // 1. Update the terminology entry
    // 2. Find all instances across projects
    // 3. Update translations in target projects
    // 4. Handle conflicts and errors
    
    return {
      updated: 0,
      failed: 0,
      errors: [],
    };
  }

  /**
   * Create series-level terminology template
   */
  async createSeriesTemplate(
    templateName: string,
    sourceProjectId: string,
    categories?: string[]
  ): Promise<{
    templateId: string;
    termCount: number;
    categories: string[];
  }> {
    // Implementation would:
    // 1. Extract terminology from source project
    // 2. Filter by categories if specified
    // 3. Create reusable template
    // 4. Store for future project initialization
    
    return {
      templateId: '',
      termCount: 0,
      categories: [],
    };
  }

  /**
   * Apply series template to new project
   */
  async applySeriesTemplate(
    templateId: string,
    targetProjectId: string,
    options: {
      categories?: string[];
      approvalStatus?: 'approved' | 'pending';
    } = {}
  ): Promise<{
    applied: number;
    skipped: number;
    errors: string[];
  }> {
    // Implementation would:
    // 1. Load template terminology
    // 2. Filter by options
    // 3. Import into target project
    // 4. Handle conflicts and errors
    
    return {
      applied: 0,
      skipped: 0,
      errors: [],
    };
  }

  /**
   * Get terminology usage statistics across series
   */
  async getSeriesTerminologyStats(): Promise<{
    totalTerms: number;
    approvedTerms: number;
    pendingTerms: number;
    mostUsedTerms: Array<{
      sourceTerm: string;
      targetTerm: string;
      totalUsage: number;
      projectCount: number;
    }>;
    categoryDistribution: Record<string, number>;
    consistencyScore: number;
  }> {
    // Implementation would analyze terminology across all projects
    return {
      totalTerms: 0,
      approvedTerms: 0,
      pendingTerms: 0,
      mostUsedTerms: [],
      categoryDistribution: {},
      consistencyScore: 0,
    };
  }

  /**
   * Validate terminology consistency across series
   */
  async validateSeriesConsistency(): Promise<{
    isConsistent: boolean;
    score: number;
    issues: ConsistencyIssue[];
    recommendations: ConsistencyRecommendation[];
  }> {
    // Implementation would:
    // 1. Compare terminology across all projects
    // 2. Identify inconsistencies
    // 3. Generate recommendations
    // 4. Calculate overall consistency score
    
    return {
      isConsistent: true,
      score: 0.85,
      issues: [],
      recommendations: [],
    };
  }
}
