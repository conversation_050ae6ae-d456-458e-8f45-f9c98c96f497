/**
 * Google Gemini AI Service
 * Provides AI-powered terminology extraction and translation suggestions
 */

export interface GeminiConfig {
  apiKey: string;
  baseUrl?: string;
  model?: string;
}

export interface TermExtractionRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage?: string;
  domain?: string;
  maxTerms?: number;
}

export interface ExtractedTerm {
  term: string;
  definition: string;
  confidence: number;
  category: string;
  context: string;
  translations?: Record<string, string>;
}

export interface TranslationSuggestion {
  sourceText: string;
  targetText: string;
  confidence: number;
  alternatives: string[];
  explanation?: string;
}

export interface TerminologyEntry {
  id: string;
  sourceTerm: string;
  targetTerm: string;
  category: string;
  context?: string;
  usageNotes?: string;
  frequency?: number;
}

export class GeminiAIService {
  private config: GeminiConfig;
  private baseUrl: string;

  constructor(config: GeminiConfig) {
    this.config = {
      model: 'gemini-2.0-flash',
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      ...config,
    };
    this.baseUrl = this.config.baseUrl!;
  }

  /**
   * Extract terminology from text using AI
   */
  async extractTerminology(request: TermExtractionRequest): Promise<ExtractedTerm[]> {
    const prompt = this.buildTermExtractionPrompt(request);
    
    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseTermExtractionResponse(response, request);
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error(`Failed to extract terminology: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get translation suggestions for text
   */
  async getTranslationSuggestions(
    sourceText: string,
    sourceLanguage: string,
    targetLanguage: string,
    context?: string,
    terminology?: TerminologyEntry[]
  ): Promise<TranslationSuggestion> {
    const prompt = this.buildTranslationPrompt(sourceText, sourceLanguage, targetLanguage, context, terminology);

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseTranslationResponse(response, sourceText);
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error(`Failed to get translation suggestions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get terminology-aware translation suggestions
   */
  async getTerminologyAwareTranslation(
    sourceText: string,
    sourceLanguage: string,
    targetLanguage: string,
    terminology: TerminologyEntry[],
    context?: string
  ): Promise<TranslationSuggestion> {
    return this.getTranslationSuggestions(sourceText, sourceLanguage, targetLanguage, context, terminology);
  }

  /**
   * Build prompt for terminology extraction
   */
  private buildTermExtractionPrompt(request: TermExtractionRequest): string {
    const { text, sourceLanguage, domain, maxTerms = 20 } = request;
    
    return `You are a professional terminology extraction expert. Extract the most important technical terms, concepts, and domain-specific vocabulary from the following text.

**Instructions:**
1. Extract up to ${maxTerms} terms that are:
   - Technical or domain-specific
   - Important for understanding the content
   - Likely to need consistent translation
   - Not common everyday words

2. For each term, provide:
   - The exact term as it appears
   - A clear, concise definition
   - Confidence score (0.0-1.0)
   - Category (technical, business, legal, medical, etc.)
   - Context sentence where it appears

3. Focus on:
   - Compound terms and phrases
   - Technical jargon
   - Proper nouns and brand names
   - Industry-specific concepts
   - Acronyms and abbreviations

**Source Language:** ${sourceLanguage}
${domain ? `**Domain:** ${domain}` : ''}

**Text to analyze:**
${text}

**Response Format (JSON):**
{
  "terms": [
    {
      "term": "exact term",
      "definition": "clear definition",
      "confidence": 0.95,
      "category": "technical",
      "context": "sentence containing the term"
    }
  ]
}

Respond only with valid JSON.`;
  }

  /**
   * Build prompt for translation suggestions
   */
  private buildTranslationPrompt(
    sourceText: string,
    sourceLanguage: string,
    targetLanguage: string,
    context?: string,
    terminology?: TerminologyEntry[]
  ): string {
    // Build terminology section if provided
    let terminologySection = '';
    if (terminology && terminology.length > 0) {
      terminologySection = `
**CRITICAL: Approved Terminology (MUST USE EXACTLY):**
${terminology.map(term =>
  `- "${term.sourceTerm}" → "${term.targetTerm}" (${term.category}${term.context ? `, ${term.context}` : ''})`
).join('\n')}

**Terminology Rules:**
1. ALWAYS use the exact approved translations for terminology terms
2. Character names, locations, and technical terms MUST match the approved terminology
3. If a term appears in the source text, use the exact target translation provided
4. Maintain consistency with established terminology throughout the series
5. Do not deviate from approved terminology under any circumstances
`;
    }

    return `You are a professional translator specializing in long-form content with terminology consistency. Provide high-quality translation suggestions for the following text.

**Instructions:**
1. Provide the best translation
2. Include 2-3 alternative translations if applicable
3. Explain any translation choices if needed
4. Consider cultural and contextual nuances
5. Maintain the tone and style of the original
${terminology && terminology.length > 0 ? '6. **CRITICAL**: Follow approved terminology exactly as specified below' : ''}

**Source Language:** ${sourceLanguage}
**Target Language:** ${targetLanguage}
${context ? `**Context:** ${context}` : ''}
${terminologySection}
**Text to translate:**
${sourceText}

**Response Format (JSON):**
{
  "translation": "best translation using approved terminology",
  "confidence": 0.95,
  "alternatives": ["alternative 1", "alternative 2"],
  "explanation": "explanation of translation choices and terminology usage",
  "terminologyUsed": ["list of terminology terms used from the approved list"]
}

Respond only with valid JSON.`;
  }

  /**
   * Call the Gemini API
   */
  private async callGeminiAPI(prompt: string): Promise<string> {
    const url = `${this.baseUrl}/models/${this.config.model}:generateContent`;
    
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': this.config.apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Invalid response format from Gemini API');
    }

    return data.candidates[0].content.parts[0].text;
  }

  /**
   * Parse terminology extraction response
   */
  private parseTermExtractionResponse(response: string, request: TermExtractionRequest): ExtractedTerm[] {
    try {
      const parsed = JSON.parse(response);
      
      if (!parsed.terms || !Array.isArray(parsed.terms)) {
        throw new Error('Invalid response format: missing terms array');
      }

      return parsed.terms.map((term: any) => ({
        term: term.term || '',
        definition: term.definition || '',
        confidence: Math.min(Math.max(term.confidence || 0.5, 0), 1),
        category: term.category || 'general',
        context: term.context || '',
        translations: {},
      }));
    } catch (error) {
      console.error('Failed to parse Gemini response:', response);
      throw new Error('Failed to parse AI response');
    }
  }

  /**
   * Parse translation response
   */
  private parseTranslationResponse(response: string, sourceText: string): TranslationSuggestion {
    try {
      const parsed = JSON.parse(response);
      
      return {
        sourceText,
        targetText: parsed.translation || '',
        confidence: Math.min(Math.max(parsed.confidence || 0.8, 0), 1),
        alternatives: parsed.alternatives || [],
        explanation: parsed.explanation,
      };
    } catch (error) {
      console.error('Failed to parse Gemini response:', response);
      throw new Error('Failed to parse AI response');
    }
  }
}

/**
 * Create a configured Gemini AI service instance
 */
export function createGeminiService(apiKey?: string): GeminiAIService {
  const key = apiKey || process.env.GEMINI_API_KEY;
  
  if (!key) {
    throw new Error('Gemini API key is required. Set GEMINI_API_KEY environment variable.');
  }

  return new GeminiAIService({
    apiKey: key,
  });
}
