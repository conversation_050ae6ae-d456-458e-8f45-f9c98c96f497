/**
 * Terminology Validation Engine
 * Validates translations against approved terminology and provides consistency warnings
 */

export interface TerminologyEntry {
  id: string;
  sourceTerm: string;
  targetTerm: string;
  category: string;
  context?: string;
  usageNotes?: string;
  frequency?: number;
  approvalStatus: 'approved' | 'pending' | 'rejected';
}

export interface ValidationResult {
  isValid: boolean;
  score: number; // 0-1 consistency score
  issues: ValidationIssue[];
  suggestions: ValidationSuggestion[];
  terminologyUsage: TerminologyUsage[];
}

export interface ValidationIssue {
  type: 'missing_terminology' | 'incorrect_terminology' | 'inconsistent_usage' | 'unapproved_term';
  severity: 'error' | 'warning' | 'info';
  message: string;
  sourceTerm: string;
  expectedTerm?: string;
  actualTerm?: string;
  position: { start: number; end: number };
  suggestions: string[];
}

export interface ValidationSuggestion {
  type: 'apply_terminology' | 'add_terminology' | 'review_consistency';
  message: string;
  action: string;
  sourceTerm: string;
  targetTerm: string;
  confidence: number;
}

export interface TerminologyUsage {
  sourceTerm: string;
  targetTerm: string;
  isCorrect: boolean;
  frequency: number;
  positions: Array<{ start: number; end: number }>;
}

export class TerminologyValidator {
  private terminology: TerminologyEntry[];

  constructor(terminology: TerminologyEntry[]) {
    this.terminology = terminology.filter(term => term.approvalStatus === 'approved');
  }

  /**
   * Validate a translation against approved terminology
   */
  validateTranslation(
    sourceText: string,
    targetText: string,
    targetLanguage: string
  ): ValidationResult {
    const issues: ValidationIssue[] = [];
    const suggestions: ValidationSuggestion[] = [];
    const terminologyUsage: TerminologyUsage[] = [];

    // Filter terminology for target language
    const relevantTerminology = this.terminology.filter(
      term => term.targetTerm && term.targetTerm.trim().length > 0
    );

    // Check for missing terminology
    const missingTerms = this.findMissingTerminology(sourceText, targetText, relevantTerminology);
    issues.push(...missingTerms);

    // Check for incorrect terminology usage
    const incorrectUsage = this.findIncorrectTerminology(targetText, relevantTerminology);
    issues.push(...incorrectUsage);

    // Check for inconsistent terminology usage
    const inconsistentUsage = this.findInconsistentUsage(targetText, relevantTerminology);
    issues.push(...inconsistentUsage);

    // Generate terminology usage report
    const usageReport = this.generateUsageReport(sourceText, targetText, relevantTerminology);
    terminologyUsage.push(...usageReport);

    // Generate suggestions based on issues
    const generatedSuggestions = this.generateSuggestions(issues, relevantTerminology);
    suggestions.push(...generatedSuggestions);

    // Calculate overall validation score
    const score = this.calculateValidationScore(issues, terminologyUsage);

    return {
      isValid: issues.filter(issue => issue.severity === 'error').length === 0,
      score,
      issues,
      suggestions,
      terminologyUsage,
    };
  }

  /**
   * Find terminology that should be used but is missing from the translation
   */
  private findMissingTerminology(
    sourceText: string,
    targetText: string,
    terminology: TerminologyEntry[]
  ): ValidationIssue[] {
    const issues: ValidationIssue[] = [];
    const sourceTextLower = sourceText.toLowerCase();
    const targetTextLower = targetText.toLowerCase();

    terminology.forEach(term => {
      const sourceTermLower = term.sourceTerm.toLowerCase();
      
      // Check if source term appears in source text
      if (sourceTextLower.includes(sourceTermLower)) {
        const targetTermLower = term.targetTerm.toLowerCase();
        
        // Check if target term is missing from target text
        if (!targetTextLower.includes(targetTermLower)) {
          const sourceIndex = sourceText.toLowerCase().indexOf(sourceTermLower);
          
          issues.push({
            type: 'missing_terminology',
            severity: term.category === 'character' ? 'error' : 'warning',
            message: `Missing approved terminology: "${term.sourceTerm}" should be translated as "${term.targetTerm}"`,
            sourceTerm: term.sourceTerm,
            expectedTerm: term.targetTerm,
            position: { start: sourceIndex, end: sourceIndex + term.sourceTerm.length },
            suggestions: [term.targetTerm],
          });
        }
      }
    });

    return issues;
  }

  /**
   * Find incorrect terminology usage in the translation
   */
  private findIncorrectTerminology(
    targetText: string,
    terminology: TerminologyEntry[]
  ): ValidationIssue[] {
    const issues: ValidationIssue[] = [];
    const targetTextLower = targetText.toLowerCase();

    terminology.forEach(term => {
      const correctTerm = term.targetTerm;
      const correctTermLower = correctTerm.toLowerCase();
      
      // Look for variations or incorrect usage
      const variations = this.generateTermVariations(correctTerm);
      
      variations.forEach(variation => {
        const variationLower = variation.toLowerCase();
        if (variationLower !== correctTermLower && targetTextLower.includes(variationLower)) {
          const index = targetText.toLowerCase().indexOf(variationLower);
          
          issues.push({
            type: 'incorrect_terminology',
            severity: 'error',
            message: `Incorrect terminology usage: "${variation}" should be "${correctTerm}"`,
            sourceTerm: term.sourceTerm,
            expectedTerm: correctTerm,
            actualTerm: variation,
            position: { start: index, end: index + variation.length },
            suggestions: [correctTerm],
          });
        }
      });
    });

    return issues;
  }

  /**
   * Find inconsistent terminology usage within the same text
   */
  private findInconsistentUsage(
    targetText: string,
    terminology: TerminologyEntry[]
  ): ValidationIssue[] {
    const issues: ValidationIssue[] = [];
    
    // This would check for cases where the same concept is translated differently
    // within the same text, which indicates inconsistency
    
    return issues; // Simplified for now
  }

  /**
   * Generate terminology usage report
   */
  private generateUsageReport(
    sourceText: string,
    targetText: string,
    terminology: TerminologyEntry[]
  ): TerminologyUsage[] {
    const usage: TerminologyUsage[] = [];
    const targetTextLower = targetText.toLowerCase();

    terminology.forEach(term => {
      const targetTermLower = term.targetTerm.toLowerCase();
      const positions: Array<{ start: number; end: number }> = [];
      
      let searchIndex = 0;
      while (searchIndex < targetText.length) {
        const index = targetTextLower.indexOf(targetTermLower, searchIndex);
        if (index === -1) break;
        
        positions.push({
          start: index,
          end: index + term.targetTerm.length
        });
        
        searchIndex = index + 1;
      }

      if (positions.length > 0) {
        usage.push({
          sourceTerm: term.sourceTerm,
          targetTerm: term.targetTerm,
          isCorrect: true, // Simplified - would need more complex validation
          frequency: positions.length,
          positions,
        });
      }
    });

    return usage;
  }

  /**
   * Generate suggestions based on validation issues
   */
  private generateSuggestions(
    issues: ValidationIssue[],
    terminology: TerminologyEntry[]
  ): ValidationSuggestion[] {
    const suggestions: ValidationSuggestion[] = [];

    issues.forEach(issue => {
      if (issue.type === 'missing_terminology' && issue.expectedTerm) {
        suggestions.push({
          type: 'apply_terminology',
          message: `Apply approved terminology: "${issue.sourceTerm}" → "${issue.expectedTerm}"`,
          action: 'replace',
          sourceTerm: issue.sourceTerm,
          targetTerm: issue.expectedTerm,
          confidence: 0.95,
        });
      }
    });

    return suggestions;
  }

  /**
   * Calculate overall validation score (0-1)
   */
  private calculateValidationScore(
    issues: ValidationIssue[],
    terminologyUsage: TerminologyUsage[]
  ): number {
    const errorCount = issues.filter(issue => issue.severity === 'error').length;
    const warningCount = issues.filter(issue => issue.severity === 'warning').length;
    const correctUsageCount = terminologyUsage.filter(usage => usage.isCorrect).length;
    const totalUsageCount = terminologyUsage.length;

    // Base score starts at 1.0
    let score = 1.0;

    // Deduct for errors (more severe)
    score -= errorCount * 0.2;

    // Deduct for warnings (less severe)
    score -= warningCount * 0.1;

    // Bonus for correct terminology usage
    if (totalUsageCount > 0) {
      const usageRatio = correctUsageCount / totalUsageCount;
      score += usageRatio * 0.1;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Generate common variations of a term for detection
   */
  private generateTermVariations(term: string): string[] {
    const variations: string[] = [];
    
    // Add common variations (simplified)
    variations.push(term);
    variations.push(term.toLowerCase());
    variations.push(term.toUpperCase());
    
    // Add variations with different spacing/punctuation
    variations.push(term.replace(/\s+/g, ''));
    variations.push(term.replace(/\s+/g, '_'));
    variations.push(term.replace(/\s+/g, '-'));
    
    return [...new Set(variations)]; // Remove duplicates
  }

  /**
   * Get quick validation summary
   */
  getValidationSummary(result: ValidationResult): string {
    const errorCount = result.issues.filter(issue => issue.severity === 'error').length;
    const warningCount = result.issues.filter(issue => issue.severity === 'warning').length;
    
    if (errorCount === 0 && warningCount === 0) {
      return 'Terminology validation passed';
    }
    
    const parts: string[] = [];
    if (errorCount > 0) {
      parts.push(`${errorCount} error${errorCount !== 1 ? 's' : ''}`);
    }
    if (warningCount > 0) {
      parts.push(`${warningCount} warning${warningCount !== 1 ? 's' : ''}`);
    }
    
    return `Terminology validation: ${parts.join(', ')}`;
  }
}
