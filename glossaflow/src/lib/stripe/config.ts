import { loadStripe, Stripe } from '@stripe/stripe-js';

// Stripe configuration
export const stripeConfig = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  currency: 'usd',
  apiVersion: '2023-10-16' as const,
};

// Initialize Stripe
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(stripeConfig.publishableKey);
  }
  return stripePromise;
};

// Subscription plans configuration
export const subscriptionPlans = {
  basic: {
    id: 'basic',
    name: 'Basic',
    description: 'Perfect for individual translators and small projects',
    price: 0,
    priceId: '', // Free plan
    interval: 'month',
    features: [
      'Up to 3 projects',
      '10,000 words per month',
      'Basic terminology management',
      'Email support',
      'Standard translation memory',
    ],
    limits: {
      projects: 3,
      wordsPerMonth: 10000,
      teamMembers: 1,
      storage: '1GB',
    },
  },
  professional: {
    id: 'professional',
    name: 'Professional',
    description: 'Ideal for professional translators and small teams',
    price: 29,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID!,
    interval: 'month',
    features: [
      'Unlimited projects',
      '100,000 words per month',
      'Advanced terminology management',
      'Priority email support',
      'Advanced translation memory',
      'Team collaboration (up to 5 members)',
      'Quality assurance tools',
      'Custom workflows',
    ],
    limits: {
      projects: -1, // Unlimited
      wordsPerMonth: 100000,
      teamMembers: 5,
      storage: '10GB',
    },
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large teams and organizations with advanced needs',
    price: 99,
    priceId: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID!,
    interval: 'month',
    features: [
      'Unlimited projects',
      'Unlimited words',
      'Advanced terminology management',
      '24/7 priority support',
      'Advanced translation memory',
      'Unlimited team members',
      'Quality assurance tools',
      'Custom workflows',
      'API access',
      'SSO integration',
      'Custom integrations',
      'Dedicated account manager',
    ],
    limits: {
      projects: -1, // Unlimited
      wordsPerMonth: -1, // Unlimited
      teamMembers: -1, // Unlimited
      storage: '100GB',
    },
  },
};

// Payment methods configuration
export const paymentMethods = {
  card: {
    name: 'Credit/Debit Card',
    description: 'Visa, Mastercard, American Express',
    icon: '💳',
  },
  sepa: {
    name: 'SEPA Direct Debit',
    description: 'European bank transfer',
    icon: '🏦',
  },
  ideal: {
    name: 'iDEAL',
    description: 'Netherlands bank transfer',
    icon: '🇳🇱',
  },
};

// Billing intervals
export const billingIntervals = {
  month: {
    label: 'Monthly',
    description: 'Billed monthly',
    discount: 0,
  },
  year: {
    label: 'Annual',
    description: 'Billed annually',
    discount: 0.2, // 20% discount
  },
};

// Usage-based pricing for overages
export const usagePricing = {
  words: {
    price: 0.001, // $0.001 per word
    unit: 'word',
    description: 'Additional words beyond plan limit',
  },
  storage: {
    price: 0.1, // $0.10 per GB
    unit: 'GB',
    description: 'Additional storage beyond plan limit',
  },
  teamMembers: {
    price: 5, // $5 per additional team member
    unit: 'member',
    description: 'Additional team members beyond plan limit',
  },
};

// Webhook event types we handle
export const webhookEvents = [
  'customer.subscription.created',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'invoice.payment_succeeded',
  'invoice.payment_failed',
  'customer.created',
  'customer.updated',
  'payment_method.attached',
  'payment_method.detached',
] as const;

export type WebhookEvent = typeof webhookEvents[number];

// Helper functions
export const formatPrice = (amount: number, currency = 'usd') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount);
};

export const calculateAnnualPrice = (monthlyPrice: number) => {
  const annualPrice = monthlyPrice * 12;
  const discount = annualPrice * billingIntervals.year.discount;
  return annualPrice - discount;
};

export const getPlanByPriceId = (priceId: string) => {
  return Object.values(subscriptionPlans).find(plan => plan.priceId === priceId);
};

export const isFeatureAvailable = (planId: string, feature: string) => {
  const plan = subscriptionPlans[planId as keyof typeof subscriptionPlans];
  return plan?.features.includes(feature) || false;
};

export const checkUsageLimit = (planId: string, usage: number, limitType: keyof typeof subscriptionPlans.basic.limits) => {
  const plan = subscriptionPlans[planId as keyof typeof subscriptionPlans];
  if (!plan) return false;
  
  const limit = plan.limits[limitType];
  if (limit === -1) return true; // Unlimited
  
  return usage <= (limit as number);
};
