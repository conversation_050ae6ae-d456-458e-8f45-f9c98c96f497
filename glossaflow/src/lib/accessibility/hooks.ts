'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { 
  generateId, 
  announceToScreenReader, 
  trapFocus, 
  getFocusableElements,
  KEYBOARD_KEYS 
} from './utils';

// Hook for generating stable IDs
export const useId = (prefix = 'id') => {
  const [id] = useState(() => generateId(prefix));
  return id;
};

// Hook for managing focus
export const useFocus = () => {
  const elementRef = useRef<HTMLElement>(null);

  const focus = useCallback(() => {
    elementRef.current?.focus();
  }, []);

  const blur = useCallback(() => {
    elementRef.current?.blur();
  }, []);

  const isFocused = useCallback(() => {
    return document.activeElement === elementRef.current;
  }, []);

  return { elementRef, focus, blur, isFocused };
};

// Hook for focus trap
export const useFocusTrap = (isActive: boolean = true) => {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === KEYBOARD_KEYS.TAB && containerRef.current) {
        trapFocus(containerRef.current, event);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isActive]);

  return containerRef;
};

// Hook for auto focus
export const useAutoFocus = (shouldFocus: boolean = true, delay: number = 0) => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (shouldFocus && elementRef.current) {
      const timeoutId = setTimeout(() => {
        elementRef.current?.focus();
      }, delay);

      return () => clearTimeout(timeoutId);
    }
  }, [shouldFocus, delay]);

  return elementRef;
};

// Hook for focus restoration
export const useFocusRestore = () => {
  const previousActiveElement = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousActiveElement.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (previousActiveElement.current) {
      previousActiveElement.current.focus();
      previousActiveElement.current = null;
    }
  }, []);

  return { saveFocus, restoreFocus };
};

// Hook for keyboard navigation
export const useKeyboardNavigation = (
  items: HTMLElement[],
  orientation: 'horizontal' | 'vertical' = 'vertical'
) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const { key } = event;
    let newIndex = currentIndex;

    if (orientation === 'vertical') {
      if (key === KEYBOARD_KEYS.ARROW_DOWN) {
        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
      } else if (key === KEYBOARD_KEYS.ARROW_UP) {
        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
      }
    } else {
      if (key === KEYBOARD_KEYS.ARROW_RIGHT) {
        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
      } else if (key === KEYBOARD_KEYS.ARROW_LEFT) {
        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
      }
    }

    if (key === KEYBOARD_KEYS.HOME) {
      newIndex = 0;
    } else if (key === KEYBOARD_KEYS.END) {
      newIndex = items.length - 1;
    }

    if (newIndex !== currentIndex) {
      event.preventDefault();
      setCurrentIndex(newIndex);
      items[newIndex]?.focus();
    }
  }, [currentIndex, items, orientation]);

  return { currentIndex, setCurrentIndex, handleKeyDown };
};

// Hook for announcements
export const useAnnouncement = () => {
  const announce = useCallback((
    message: string, 
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    announceToScreenReader(message, priority);
  }, []);

  return { announce };
};

// Hook for managing expanded/collapsed state with announcements
export const useDisclosure = (defaultOpen: boolean = false) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const { announce } = useAnnouncement();

  const open = useCallback(() => {
    setIsOpen(true);
    announce('Expanded', 'polite');
  }, [announce]);

  const close = useCallback(() => {
    setIsOpen(false);
    announce('Collapsed', 'polite');
  }, [announce]);

  const toggle = useCallback(() => {
    if (isOpen) {
      close();
    } else {
      open();
    }
  }, [isOpen, open, close]);

  return { isOpen, open, close, toggle };
};

// Hook for managing modal accessibility
export const useModal = (isOpen: boolean) => {
  const modalRef = useRef<HTMLElement>(null);
  const { saveFocus, restoreFocus } = useFocusRestore();

  useEffect(() => {
    if (isOpen) {
      saveFocus();
      
      // Focus first focusable element in modal
      if (modalRef.current) {
        const focusableElements = getFocusableElements(modalRef.current);
        focusableElements[0]?.focus();
      }

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      restoreFocus();
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen, saveFocus, restoreFocus]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === KEYBOARD_KEYS.ESCAPE && isOpen) {
        restoreFocus();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, restoreFocus]);

  return modalRef;
};

// Hook for managing roving tabindex
export const useRovingTabIndex = (items: HTMLElement[], currentIndex: number) => {
  useEffect(() => {
    items.forEach((item, index) => {
      if (index === currentIndex) {
        item.setAttribute('tabindex', '0');
      } else {
        item.setAttribute('tabindex', '-1');
      }
    });
  }, [items, currentIndex]);
};

// Hook for detecting user preferences
export const useUserPreferences = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [prefersHighContrast, setPrefersHighContrast] = useState(false);

  useEffect(() => {
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');

    setPrefersReducedMotion(motionQuery.matches);
    setPrefersHighContrast(contrastQuery.matches);

    const handleMotionChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    const handleContrastChange = (e: MediaQueryListEvent) => {
      setPrefersHighContrast(e.matches);
    };

    motionQuery.addEventListener('change', handleMotionChange);
    contrastQuery.addEventListener('change', handleContrastChange);

    return () => {
      motionQuery.removeEventListener('change', handleMotionChange);
      contrastQuery.removeEventListener('change', handleContrastChange);
    };
  }, []);

  return { prefersReducedMotion, prefersHighContrast };
};

// Hook for managing live regions
export const useLiveRegion = () => {
  const [message, setMessage] = useState('');
  const [priority, setPriority] = useState<'polite' | 'assertive'>('polite');

  const announce = useCallback((
    newMessage: string, 
    newPriority: 'polite' | 'assertive' = 'polite'
  ) => {
    setMessage(newMessage);
    setPriority(newPriority);

    // Clear message after announcement
    setTimeout(() => {
      setMessage('');
    }, 1000);
  }, []);

  return { message, priority, announce };
};
