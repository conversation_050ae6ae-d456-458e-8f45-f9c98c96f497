export const siteConfig = {
  name: 'GlossaFlow',
  description: 'Professional translation management platform for streamlined localization workflows',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://glossaflow.com',
  ogImage: '/images/og-image.png',
  links: {
    twitter: 'https://twitter.com/glossaflow',
    github: 'https://github.com/glossaflow',
    linkedin: 'https://linkedin.com/company/glossaflow',
  },
  keywords: [
    'translation management',
    'localization platform',
    'CAT tool',
    'translation memory',
    'terminology management',
    'project management',
    'multilingual content',
    'translation workflow',
    'language services',
    'globalization',
  ],
};

export const pageMetadata = {
  dashboard: {
    title: 'Dashboard',
    description: 'Overview of your translation projects, progress, and team activity',
    keywords: ['dashboard', 'overview', 'projects', 'progress'],
  },
  projects: {
    title: 'Projects',
    description: 'Manage your translation projects, track progress, and collaborate with your team',
    keywords: ['projects', 'translation projects', 'project management', 'collaboration'],
  },
  terminology: {
    title: 'Terminology Management',
    description: 'Create and manage terminology databases for consistent translations',
    keywords: ['terminology', 'glossary', 'translation memory', 'consistency'],
  },
  team: {
    title: 'Team Management',
    description: 'Manage your translation team, assign roles, and track performance',
    keywords: ['team', 'translators', 'reviewers', 'collaboration', 'management'],
  },
  translate: {
    title: 'Translation Workspace',
    description: 'Professional translation interface with CAT tools and quality assurance',
    keywords: ['translation', 'CAT tool', 'workspace', 'quality assurance'],
  },
  admin: {
    title: 'Admin Dashboard',
    description: 'System administration, user management, and platform analytics',
    keywords: ['admin', 'administration', 'analytics', 'system management'],
  },
  profile: {
    title: 'Profile Settings',
    description: 'Manage your account settings, preferences, and profile information',
    keywords: ['profile', 'settings', 'account', 'preferences'],
  },
  signin: {
    title: 'Sign In',
    description: 'Sign in to your GlossaFlow account to access your translation projects',
    keywords: ['sign in', 'login', 'authentication', 'access'],
    noIndex: true,
  },
  signup: {
    title: 'Sign Up',
    description: 'Create a new GlossaFlow account to start managing your translation projects',
    keywords: ['sign up', 'register', 'create account', 'get started'],
  },
};

export const structuredDataSchemas = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    url: siteConfig.url,
    logo: `${siteConfig.url}/images/logo.png`,
    description: siteConfig.description,
    sameAs: [
      siteConfig.links.twitter,
      siteConfig.links.linkedin,
      siteConfig.links.github,
    ],
  },
  
  softwareApplication: {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: siteConfig.name,
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web Browser',
    description: siteConfig.description,
    url: siteConfig.url,
    screenshot: `${siteConfig.url}/images/screenshot.png`,
    featureList: [
      'Translation Project Management',
      'Terminology Management',
      'Translation Memory',
      'Collaborative Translation',
      'Quality Assurance',
      'Workflow Automation',
      'Multi-language Support',
      'Real-time Collaboration',
    ],
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      description: 'Free tier available with premium plans',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '150',
      bestRating: '5',
      worstRating: '1',
    },
  },
  
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    url: siteConfig.url,
    description: siteConfig.description,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  },
};

export const robotsTxt = `User-agent: *
Allow: /

# Disallow authentication pages
Disallow: /auth/
Disallow: /api/

# Disallow admin pages for non-admin users
Disallow: /dashboard/admin/

# Allow specific public pages
Allow: /auth/signin
Allow: /auth/signup

Sitemap: ${siteConfig.url}/sitemap.xml
`;

export const generateSitemap = (pages: string[]) => {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages
  .map(
    (page) => `  <url>
    <loc>${siteConfig.url}${page}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${page === '/' ? '1.0' : '0.8'}</priority>
  </url>`
  )
  .join('\n')}
</urlset>`;
  
  return sitemap;
};
