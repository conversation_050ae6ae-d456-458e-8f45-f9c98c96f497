/**
 * AI-Powered Translation API
 * Provides intelligent translation suggestions using Google Gemini AI
 */

import { baseApi } from './base';
import type { ApiResponse } from './base';

export interface TranslationSuggestionRequest {
  sourceText: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  domain?: string;
  projectId?: string;
}

export interface TranslationSuggestion {
  sourceText: string;
  targetText: string;
  confidence: number;
  alternatives: string[];
  explanation?: string;
  terminology?: TerminologyMatch[];
}

export interface TerminologyMatch {
  sourceTerm: string;
  targetTerm: string;
  confidence: number;
  definition?: string;
}

export interface BatchTranslationRequest {
  segments: Array<{
    id: string;
    sourceText: string;
  }>;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  domain?: string;
  projectId?: string;
}

export interface BatchTranslationResponse {
  translations: Array<{
    id: string;
    sourceText: string;
    targetText: string;
    confidence: number;
    alternatives: string[];
  }>;
  overallConfidence: number;
  processingTime: number;
}

export interface QualityAssessment {
  score: number;
  issues: Array<{
    type: 'grammar' | 'terminology' | 'style' | 'accuracy';
    severity: 'low' | 'medium' | 'high';
    description: string;
    suggestion?: string;
  }>;
  improvements: string[];
}

// Extend the base API with AI translation endpoints
export const aiTranslationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get AI translation suggestions for a single segment
    getTranslationSuggestion: builder.mutation<ApiResponse<TranslationSuggestion>, TranslationSuggestionRequest>({
      query: (data) => ({
        url: '/ai/translation/suggest',
        method: 'POST',
        body: data,
      }),
    }),

    // Get batch translation suggestions for multiple segments
    getBatchTranslationSuggestions: builder.mutation<ApiResponse<BatchTranslationResponse>, BatchTranslationRequest>({
      query: (data) => ({
        url: '/ai/translation/batch',
        method: 'POST',
        body: data,
      }),
    }),

    // Assess translation quality using AI
    assessTranslationQuality: builder.mutation<ApiResponse<QualityAssessment>, {
      sourceText: string;
      targetText: string;
      sourceLanguage: string;
      targetLanguage: string;
      context?: string;
    }>({
      query: (data) => ({
        url: '/ai/translation/assess',
        method: 'POST',
        body: data,
      }),
    }),

    // Get terminology suggestions for a project
    getTerminologySuggestions: builder.query<ApiResponse<TerminologyMatch[]>, {
      projectId: string;
      sourceText: string;
      targetLanguage: string;
    }>({
      query: ({ projectId, sourceText, targetLanguage }) => ({
        url: `/ai/terminology/suggest/${projectId}`,
        params: { sourceText, targetLanguage },
      }),
      providesTags: (result, error, { projectId }) => [
        { type: 'Translation', id: `terminology-${projectId}` }
      ],
    }),

    // Improve existing translation using AI
    improveTranslation: builder.mutation<ApiResponse<TranslationSuggestion>, {
      sourceText: string;
      currentTranslation: string;
      sourceLanguage: string;
      targetLanguage: string;
      feedback?: string;
      context?: string;
    }>({
      query: (data) => ({
        url: '/ai/translation/improve',
        method: 'POST',
        body: data,
      }),
    }),

    // Get context-aware translation suggestions
    getContextualTranslation: builder.mutation<ApiResponse<TranslationSuggestion>, {
      sourceText: string;
      sourceLanguage: string;
      targetLanguage: string;
      previousSegments: string[];
      nextSegments: string[];
      documentType?: string;
      domain?: string;
    }>({
      query: (data) => ({
        url: '/ai/translation/contextual',
        method: 'POST',
        body: data,
      }),
    }),

    // Get translation consistency check
    checkTranslationConsistency: builder.mutation<ApiResponse<{
      isConsistent: boolean;
      inconsistencies: Array<{
        term: string;
        translations: string[];
        suggestion: string;
      }>;
      score: number;
    }>, {
      projectId: string;
      segments: Array<{
        sourceText: string;
        targetText: string;
      }>;
    }>({
      query: (data) => ({
        url: '/ai/translation/consistency',
        method: 'POST',
        body: data,
      }),
    }),
  }),
  overrideExisting: true,
});

// Export hooks for use in components
export const {
  useGetTranslationSuggestionMutation,
  useGetBatchTranslationSuggestionsMutation,
  useAssessTranslationQualityMutation,
  useGetTerminologySuggestionsQuery,
  useImproveTranslationMutation,
  useGetContextualTranslationMutation,
  useCheckTranslationConsistencyMutation,
} = aiTranslationApi;

// Utility functions for working with AI translations
export const aiTranslationUtils = {
  /**
   * Format confidence score as percentage
   */
  formatConfidence: (confidence: number): string => {
    return `${Math.round(confidence * 100)}%`;
  },

  /**
   * Get confidence level description
   */
  getConfidenceLevel: (confidence: number): 'low' | 'medium' | 'high' => {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.6) return 'medium';
    return 'low';
  },

  /**
   * Get confidence color for UI
   */
  getConfidenceColor: (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  },

  /**
   * Validate translation suggestion
   */
  isValidSuggestion: (suggestion: TranslationSuggestion): boolean => {
    return !!(
      suggestion.targetText &&
      suggestion.targetText.trim().length > 0 &&
      suggestion.confidence > 0 &&
      suggestion.confidence <= 1
    );
  },

  /**
   * Extract key terminology from suggestions
   */
  extractTerminology: (suggestion: TranslationSuggestion): TerminologyMatch[] => {
    return suggestion.terminology || [];
  },

  /**
   * Get best alternative translation
   */
  getBestAlternative: (suggestion: TranslationSuggestion): string | null => {
    return suggestion.alternatives && suggestion.alternatives.length > 0
      ? suggestion.alternatives[0]
      : null;
  },

  /**
   * Calculate overall batch confidence
   */
  calculateBatchConfidence: (translations: Array<{ confidence: number }>): number => {
    if (translations.length === 0) return 0;
    const sum = translations.reduce((acc, t) => acc + t.confidence, 0);
    return sum / translations.length;
  },
};

export type {
  TranslationSuggestionRequest,
  TranslationSuggestion,
  TerminologyMatch,
  BatchTranslationRequest,
  BatchTranslationResponse,
  QualityAssessment,
};
