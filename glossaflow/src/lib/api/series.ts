import { baseApi } from './base';
import type {
  ApiResponse,
  PaginatedResponse,
  Chapter,
  CreateChapterRequest,
  UpdateChapterRequest,
  GetChaptersResponse,
  GetChapterResponse,
  ChapterProgress,
  TranslationSegment,
} from '@/types';

// Series filters for querying (reusing Chapter types but with series terminology)
export interface SeriesFilters {
  projectId?: string;
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: 'chapter_number' | 'title' | 'progress_percentage' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

// Series statistics (reusing Chapter types)
export interface SeriesStats {
  totalChapters: number; // Note: keeping 'chapters' in the data structure for backend compatibility
  completedChapters: number;
  inProgressChapters: number;
  pendingChapters: number;
  totalWords: number;
  translatedWords: number;
  averageProgress: number;
}

// Type aliases for better semantic meaning
export type Series = Chapter;
export type CreateSeriesRequest = CreateChapterRequest;
export type UpdateSeriesRequest = UpdateChapterRequest;
export type GetSeriesResponse = GetChaptersResponse;
export type GetSingleSeriesResponse = GetChapterResponse;
export type SeriesProgress = ChapterProgress;

// Extend the base API with series management endpoints (using chapters API under the hood)
export const seriesApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get series for a project
    getSeries: builder.query<PaginatedResponse<Series>, SeriesFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `chapters?${params.toString()}`, // Still using chapters endpoint
          method: 'GET',
        };
      },
      providesTags: (result) =>
        result?.data.items
          ? [
              ...result.data.items.map(({ id }) => ({ type: 'Chapter' as const, id })),
              { type: 'Chapter', id: 'LIST' },
            ]
          : [{ type: 'Chapter', id: 'LIST' }],
    }),

    // Get a single series with segments
    getSingleSeries: builder.query<ApiResponse<GetSingleSeriesResponse>, string>({
      query: (seriesId) => `/chapters/${seriesId}`, // Still using chapters endpoint
      providesTags: (result, error, seriesId) => [
        { type: 'Chapter', id: seriesId },
        { type: 'Translation', id: 'LIST' },
      ],
    }),

    // Create a new series
    createSeries: builder.mutation<ApiResponse<Series>, CreateSeriesRequest>({
      query: (data) => ({
        url: '/chapters', // Still using chapters endpoint
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Chapter', id: 'LIST' },
        { type: 'Project', id: 'LIST' },
      ],
    }),

    // Update series
    updateSeries: builder.mutation<ApiResponse<Series>, {
      id: string;
      data: UpdateSeriesRequest;
    }>({
      query: ({ id, data }) => ({
        url: `/chapters/${id}`, // Still using chapters endpoint
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Chapter', id },
        { type: 'Chapter', id: 'LIST' },
      ],
    }),

    // Delete series
    deleteSeries: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/chapters/${id}`, // Still using chapters endpoint
        method: 'DELETE',
      }),
      invalidatesTags: [
        { type: 'Chapter', id: 'LIST' },
        { type: 'Project', id: 'LIST' },
      ],
    }),

    // Get series statistics for a project
    getSeriesStats: builder.query<ApiResponse<SeriesStats>, string>({
      query: (projectId) => `/chapters/stats?projectId=${projectId}`, // Still using chapters endpoint
      providesTags: (result, error, projectId) => [
        { type: 'Chapter', id: `${projectId}-stats` },
      ],
    }),

    // Get series progress
    getSeriesProgress: builder.query<ApiResponse<SeriesProgress>, string>({
      query: (seriesId) => `/chapters/${seriesId}/progress`, // Still using chapters endpoint
      providesTags: (result, error, seriesId) => [
        { type: 'Chapter', id: `${seriesId}-progress` },
      ],
    }),

    // Get segments for a series
    getSeriesSegments: builder.query<PaginatedResponse<TranslationSegment>, {
      seriesId: string;
      page?: number;
      limit?: number;
      status?: string;
    }>({
      query: ({ seriesId, page = 1, limit = 50, status }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
        });
        if (status) params.append('status', status);
        
        return `/chapters/${seriesId}/segments?${params.toString()}`; // Still using chapters endpoint
      },
      providesTags: (result, error, { seriesId }) => [
        { type: 'Translation', id: `chapter-${seriesId}` },
        { type: 'Chapter', id: seriesId },
      ],
    }),

    // Auto-translate series with AI
    autoTranslateSeries: builder.mutation<ApiResponse<{
      translatedSegments: number;
      creditsUsed: number;
      remainingCredits: number;
    }>, {
      seriesId: string;
      targetLanguage: string;
      useCredits: boolean;
    }>({
      query: ({ seriesId, targetLanguage, useCredits }) => ({
        url: `/chapters/${seriesId}/auto-translate`, // Still using chapters endpoint
        method: 'POST',
        body: { targetLanguage, useCredits },
      }),
      invalidatesTags: (result, error, { seriesId }) => [
        { type: 'Translation', id: `chapter-${seriesId}` },
        { type: 'Chapter', id: seriesId },
        { type: 'Chapter', id: `${seriesId}-progress` },
        { type: 'Credits', id: 'BALANCE' },
      ],
    }),

    // Reorder series
    reorderSeries: builder.mutation<ApiResponse<void>, {
      projectId: string;
      seriesIds: string[];
    }>({
      query: ({ projectId, seriesIds }) => ({
        url: `/chapters/reorder`, // Still using chapters endpoint
        method: 'POST',
        body: { projectId, chapterIds: seriesIds }, // Backend expects chapterIds
      }),
      invalidatesTags: [
        { type: 'Chapter', id: 'LIST' },
      ],
    }),
  }),
  overrideExisting: true,
});

// Export hooks for use in components
export const {
  useGetSeriesQuery,
  useGetSingleSeriesQuery,
  useCreateSeriesMutation,
  useUpdateSeriesMutation,
  useDeleteSeriesMutation,
  useGetSeriesStatsQuery,
  useGetSeriesProgressQuery,
  useGetSeriesSegmentsQuery,
  useAutoTranslateSeriesMutation,
  useReorderSeriesMutation,
} = seriesApi;
