import { baseApi, ApiResponse, PaginatedResponse } from './base';

// Types for term candidates
export interface TermCandidate {
  id: string;
  projectId: string;
  candidateTerm: string;
  frequency: number;
  confidenceScore: number;
  contextSentence?: string;
  status: 'pending' | 'approved' | 'rejected';
  assignedCategory?: string;
  extractionMetadata: {
    tfidf_score?: number;
    pattern_type?: string;
    pos_tags?: string[];
    sentence_position?: number;
    rejection_reason?: string;
    rejected_at?: string;
  };
  createdBy: string;
  reviewedBy?: string;
  createdAt: string;
  updatedAt: string;
  organizationId?: string;
  project?: {
    id: string;
    name: string;
    source_language: string;
    target_languages: string[];
  };
  createdByUser?: {
    id: string;
    name: string;
    email: string;
  };
  reviewedByUser?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface TermCandidateFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'pending' | 'approved' | 'rejected';
  projectId?: string;
  sortBy?: 'confidence_score' | 'frequency' | 'created_at' | 'candidate_term';
  sortOrder?: 'asc' | 'desc';
}

export interface CreateTermCandidateRequest {
  projectId: string;
  candidateTerm: string;
  frequency?: number;
  confidenceScore?: number;
  contextSentence?: string;
  assignedCategory?: string;
  extractionMetadata?: Record<string, any>;
}

export interface UpdateTermCandidateRequest {
  status?: 'pending' | 'approved' | 'rejected';
  assignedCategory?: string;
  reviewedBy?: string;
}

export interface ApproveTermCandidateRequest {
  targetTerm: string;
  targetLanguage: string;
  category: string;
  context?: string;
  usageNotes?: string;
  addToGlossary?: boolean;
}

export interface RejectTermCandidateRequest {
  reason?: string;
}

export interface ExtractTermCandidatesRequest {
  projectId: string;
  sourceText: string;
  sourceLanguage?: string;
  clearExisting?: boolean;
}

export interface TermCandidateStats {
  overview: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    recentCandidates: number;
    approvalRate: number;
    avgConfidenceScore: number;
    avgFrequency: number;
  };
  distributions: {
    byCategory: Record<string, number>;
    byProject: Record<string, number>;
    confidence: {
      high: number;
      medium: number;
      low: number;
    };
    frequency: {
      high: number;
      medium: number;
      low: number;
    };
  };
  topCandidates: {
    byConfidence: Array<{
      projectId: string;
      confidenceScore: number;
      frequency: number;
      status: string;
    }>;
    byFrequency: Array<{
      projectId: string;
      confidenceScore: number;
      frequency: number;
      status: string;
    }>;
  };
  filters: {
    projectId?: string;
  };
}

export const termCandidatesApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get term candidates with filtering and pagination
    getTermCandidates: builder.query<PaginatedResponse<TermCandidate>, TermCandidateFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `term-candidates?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) =>
        result?.data.items
          ? [
              ...result.data.items.map(({ id }) => ({ type: 'TermCandidate' as const, id })),
              { type: 'TermCandidate', id: 'LIST' },
            ]
          : [{ type: 'TermCandidate', id: 'LIST' }],
    }),

    // Get single term candidate
    getTermCandidate: builder.query<ApiResponse<TermCandidate>, string>({
      query: (id) => ({
        url: `term-candidates/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'TermCandidate', id }],
    }),

    // Create term candidate
    createTermCandidate: builder.mutation<ApiResponse<TermCandidate>, CreateTermCandidateRequest>({
      query: (data) => ({
        url: 'term-candidates',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'TermCandidate', id: 'LIST' }],
    }),

    // Update term candidate
    updateTermCandidate: builder.mutation<ApiResponse<TermCandidate>, { id: string; data: UpdateTermCandidateRequest }>({
      query: ({ id, data }) => ({
        url: `term-candidates/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'TermCandidate', id },
        { type: 'TermCandidate', id: 'LIST' },
      ],
    }),

    // Delete term candidate
    deleteTermCandidate: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `term-candidates/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'TermCandidate', id },
        { type: 'TermCandidate', id: 'LIST' },
      ],
    }),

    // Approve term candidate
    approveTermCandidate: builder.mutation<ApiResponse<{
      candidate: TermCandidate;
      terminologyEntry: any;
      glossaryEntry?: any;
    }>, { id: string; data: ApproveTermCandidateRequest }>({
      query: ({ id, data }) => ({
        url: `term-candidates/${id}/approve`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'TermCandidate', id },
        { type: 'TermCandidate', id: 'LIST' },
        'Terminology', // Also invalidate terminology cache
      ],
    }),

    // Reject term candidate
    rejectTermCandidate: builder.mutation<ApiResponse<TermCandidate>, { id: string; data: RejectTermCandidateRequest }>({
      query: ({ id, data }) => ({
        url: `term-candidates/${id}/reject`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'TermCandidate', id },
        { type: 'TermCandidate', id: 'LIST' },
      ],
    }),

    // Extract term candidates from text
    extractTermCandidates: builder.mutation<ApiResponse<{
      projectId: string;
      projectName: string;
      extractionResult: any;
      statistics: {
        totalCandidates: number;
        pendingCandidates: number;
        newCandidates: number;
      };
    }>, ExtractTermCandidatesRequest>({
      query: (data) => ({
        url: 'term-candidates/extract',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'TermCandidate', id: 'LIST' },
      ],
    }),

    // Get extraction status/statistics
    getExtractionStatus: builder.query<ApiResponse<{
      projectId: string;
      projectName: string;
      statistics: {
        totalCandidates: number;
        pendingCandidates: number;
        approvedCandidates: number;
        rejectedCandidates: number;
      };
      lastExtraction?: {
        extractedAt: string;
        extractedBy: string;
      };
    }>, string>({
      query: (projectId) => ({
        url: `term-candidates/extract?projectId=${projectId}`,
        method: 'GET',
      }),
    }),

    // Get term candidate statistics
    getTermCandidateStats: builder.query<ApiResponse<TermCandidateStats>, { projectId?: string }>({
      query: ({ projectId } = {}) => {
        const params = new URLSearchParams();
        if (projectId) {
          params.append('projectId', projectId);
        }
        return {
          url: `term-candidates/stats?${params.toString()}`,
          method: 'GET',
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useGetTermCandidatesQuery,
  useGetTermCandidateQuery,
  useCreateTermCandidateMutation,
  useUpdateTermCandidateMutation,
  useDeleteTermCandidateMutation,
  useApproveTermCandidateMutation,
  useRejectTermCandidateMutation,
  useExtractTermCandidatesMutation,
  useGetExtractionStatusQuery,
  useGetTermCandidateStatsQuery,
} = termCandidatesApi;
