import { baseApi, ApiResponse, PaginatedResponse } from './base';

export interface TranslationSegment {
  id: string;
  projectId: string;
  fileId: string;
  segmentNumber: number;
  sourceText: string;
  targetText: string;
  status: 'pending' | 'in_progress' | 'completed' | 'review' | 'approved';
  translatedBy?: string;
  reviewedBy?: string;
  approvedBy?: string;
  translatedAt?: string;
  reviewedAt?: string;
  approvedAt?: string;
  comments: Comment[];
  tmMatches: TMMatch[];
  qualityScore?: number;
  wordCount: number;
  characterCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Comment {
  id: string;
  segmentId: string;
  authorId: string;
  content: string;
  type: 'comment' | 'issue' | 'suggestion';
  status?: 'open' | 'resolved';
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  replies?: Comment[];
}

export interface TMMatch {
  id: string;
  sourceText: string;
  targetText: string;
  similarity: number;
  origin: string;
  projectId?: string;
  createdAt: string;
}

export interface UpdateSegmentRequest {
  targetText: string;
  status?: TranslationSegment['status'];
}

export interface CreateCommentRequest {
  content: string;
  type: Comment['type'];
  parentId?: string;
}

export interface SegmentFilters {
  projectId?: string;
  fileId?: string;
  status?: TranslationSegment['status'];
  translatedBy?: string;
  reviewedBy?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface TranslationStats {
  totalSegments: number;
  completedSegments: number;
  reviewedSegments: number;
  approvedSegments: number;
  pendingSegments: number;
  totalWords: number;
  completedWords: number;
  averageQualityScore: number;
  productivity: {
    segmentsPerHour: number;
    wordsPerHour: number;
  };
}

export const translationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSegments: builder.query<PaginatedResponse<TranslationSegment>, SegmentFilters>({
      query: (filters) => ({
        url: '/translation/segments',
        params: filters,
      }),
      providesTags: ['Translation'],
    }),

    getSegment: builder.query<ApiResponse<TranslationSegment>, string>({
      query: (id) => `/translation/segments/${id}`,
      providesTags: (result, error, id) => [{ type: 'Translation', id }],
    }),

    updateSegment: builder.mutation<ApiResponse<TranslationSegment>, {
      id: string;
      data: UpdateSegmentRequest;
    }>({
      query: ({ id, data }) => ({
        url: `/translation/segments/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Translation', id }],
    }),

    getSegmentComments: builder.query<ApiResponse<Comment[]>, string>({
      query: (segmentId) => `/translation/segments/${segmentId}/comments`,
      providesTags: (result, error, segmentId) => [{ type: 'Comment', id: segmentId }],
    }),

    addSegmentComment: builder.mutation<ApiResponse<Comment>, {
      segmentId: string;
      data: CreateCommentRequest;
    }>({
      query: ({ segmentId, data }) => ({
        url: `/translation/segments/${segmentId}/comments`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { segmentId }) => [{ type: 'Comment', id: segmentId }],
    }),

    updateComment: builder.mutation<ApiResponse<Comment>, {
      commentId: string;
      data: Partial<CreateCommentRequest>;
    }>({
      query: ({ commentId, data }) => ({
        url: `/translation/comments/${commentId}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { commentId }) => [{ type: 'Comment', id: commentId }],
    }),

    deleteComment: builder.mutation<ApiResponse<void>, string>({
      query: (commentId) => ({
        url: `/translation/comments/${commentId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Comment'],
    }),

    getTMMatches: builder.query<ApiResponse<TMMatch[]>, {
      sourceText: string;
      targetLanguage: string;
      projectId?: string;
      threshold?: number;
    }>({
      query: ({ sourceText, targetLanguage, projectId, threshold = 70 }) => ({
        url: '/translation/tm-matches',
        params: { sourceText, targetLanguage, projectId, threshold },
      }),
    }),

    getTranslationStats: builder.query<ApiResponse<TranslationStats>, {
      projectId?: string;
      userId?: string;
      dateFrom?: string;
      dateTo?: string;
    }>({
      query: (params) => ({
        url: '/translation/stats',
        params,
      }),
      providesTags: ['Translation'],
    }),

    assignSegments: builder.mutation<ApiResponse<void>, {
      segmentIds: string[];
      userId: string;
    }>({
      query: (data) => ({
        url: '/translation/assign',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Translation'],
    }),

    bulkUpdateSegments: builder.mutation<ApiResponse<void>, {
      segmentIds: string[];
      data: Partial<UpdateSegmentRequest>;
    }>({
      query: ({ segmentIds, data }) => ({
        url: '/translation/bulk-update',
        method: 'POST',
        body: { segmentIds, ...data },
      }),
      invalidatesTags: ['Translation'],
    }),

    exportTranslation: builder.mutation<Blob, {
      projectId: string;
      format: 'tmx' | 'xliff' | 'csv';
      includeApprovedOnly?: boolean;
    }>({
      query: ({ projectId, format, includeApprovedOnly = false }) => ({
        url: `/translation/export/${projectId}`,
        method: 'POST',
        body: { format, includeApprovedOnly },
        responseHandler: (response) => response.blob(),
      }),
    }),

    getProjectProgress: builder.query<ApiResponse<{
      overall: number;
      byLanguage: Record<string, number>;
      byFile: Record<string, number>;
      byStatus: Record<string, number>;
    }>, string>({
      query: (projectId) => `/translation/progress/${projectId}`,
      providesTags: (result, error, projectId) => [{ type: 'Translation', id: `progress-${projectId}` }],
    }),
  }),
});

export const {
  useGetSegmentsQuery,
  useGetSegmentQuery,
  useUpdateSegmentMutation,
  useGetSegmentCommentsQuery,
  useAddSegmentCommentMutation,
  useUpdateCommentMutation,
  useDeleteCommentMutation,
  useGetTMMatchesQuery,
  useGetTranslationStatsQuery,
  useAssignSegmentsMutation,
  useBulkUpdateSegmentsMutation,
  useExportTranslationMutation,
  useGetProjectProgressQuery,
} = translationApi;
