import { baseApi } from './base';
import type {
  ApiResponse,
  PaginatedResponse,
  Credits,
  CreditTransaction,
  CreateCreditTransactionRequest,
  GetCreditsResponse,
} from '@/types';

// Credit transaction filters
export interface CreditTransactionFilters {
  organizationId?: string;
  userId?: string;
  projectId?: string;
  chapterId?: string;
  transactionType?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// Credit purchase request
export interface CreditPurchaseRequest {
  amount: number;
  paymentMethodId?: string;
  returnUrl?: string;
}

// Credit usage statistics
export interface CreditUsageStats {
  totalCredits: number;
  usedCredits: number;
  remainingCredits: number;
  thisMonthUsage: number;
  lastMonthUsage: number;
  averageMonthlyUsage: number;
  topUsageCategories: Array<{
    category: string;
    amount: number;
    percentage: number;
  }>;
}

// Extend the base API with credit management endpoints
export const creditsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get credit balance and recent transactions
    getCredits: builder.query<ApiResponse<GetCreditsResponse>, string | void>({
      query: (organizationId) => {
        const params = organizationId ? `?organizationId=${organizationId}` : '';
        return `/credits${params}`;
      },
      providesTags: [{ type: 'Credits', id: 'BALANCE' }],
    }),

    // Get credit transactions with filtering
    getCreditTransactions: builder.query<PaginatedResponse<CreditTransaction>, CreditTransactionFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `credits/transactions?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: [{ type: 'Credits', id: 'TRANSACTIONS' }],
    }),

    // Get credit usage statistics
    getCreditUsageStats: builder.query<ApiResponse<CreditUsageStats>, {
      organizationId?: string;
      period?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ organizationId, period = 'month' }) => {
        const params = new URLSearchParams({ period });
        if (organizationId) params.append('organizationId', organizationId);
        return `/credits/usage-stats?${params.toString()}`;
      },
      providesTags: [{ type: 'Credits', id: 'STATS' }],
    }),

    // Purchase credits
    purchaseCredits: builder.mutation<ApiResponse<{
      transactionId: string;
      creditsAdded: number;
      newBalance: number;
      paymentIntentId?: string;
      clientSecret?: string;
    }>, CreditPurchaseRequest>({
      query: (data) => ({
        url: '/credits/purchase',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Credits', id: 'BALANCE' },
        { type: 'Credits', id: 'TRANSACTIONS' },
        { type: 'Credits', id: 'STATS' },
      ],
    }),

    // Deduct credits for AI translation
    deductCredits: builder.mutation<ApiResponse<{
      transactionId: string;
      creditsDeducted: number;
      remainingBalance: number;
    }>, {
      amount: number;
      projectId?: string;
      chapterId?: string;
      segmentId?: string;
      description: string;
      metadata?: Record<string, any>;
    }>({
      query: (data) => ({
        url: '/credits/deduct',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Credits', id: 'BALANCE' },
        { type: 'Credits', id: 'TRANSACTIONS' },
      ],
    }),

    // Check if user has sufficient credits
    checkCreditSufficiency: builder.query<ApiResponse<{
      hasSufficientCredits: boolean;
      currentBalance: number;
      requiredAmount: number;
      shortfall: number;
    }>, {
      requiredAmount: number;
      organizationId?: string;
    }>({
      query: ({ requiredAmount, organizationId }) => {
        const params = new URLSearchParams({ requiredAmount: requiredAmount.toString() });
        if (organizationId) params.append('organizationId', organizationId);
        return `/credits/check-sufficiency?${params.toString()}`;
      },
      // Don't cache this query as credit balance can change frequently
      keepUnusedDataFor: 0,
    }),

    // Get credit pricing information
    getCreditPricing: builder.query<ApiResponse<{
      packages: Array<{
        id: string;
        name: string;
        credits: number;
        price: number;
        pricePerCredit: number;
        popular?: boolean;
        bonus?: number;
      }>;
      aiTranslationCost: {
        perWord: number;
        perCharacter: number;
        minimumCost: number;
      };
    }>, void>({
      query: () => '/credits/pricing',
      // Cache pricing for 1 hour
      keepUnusedDataFor: 3600,
    }),

    // Refund credits (admin only)
    refundCredits: builder.mutation<ApiResponse<{
      transactionId: string;
      creditsRefunded: number;
      newBalance: number;
    }>, {
      organizationId: string;
      amount: number;
      reason: string;
      originalTransactionId?: string;
    }>({
      query: (data) => ({
        url: '/credits/refund',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Credits', id: 'BALANCE' },
        { type: 'Credits', id: 'TRANSACTIONS' },
      ],
    }),

    // Get credit transaction details
    getCreditTransaction: builder.query<ApiResponse<CreditTransaction>, string>({
      query: (transactionId) => `/credits/transactions/${transactionId}`,
      providesTags: (result, error, transactionId) => [
        { type: 'Credits', id: transactionId },
      ],
    }),

    // Estimate translation cost
    estimateTranslationCost: builder.mutation<ApiResponse<{
      estimatedCost: number;
      wordCount: number;
      characterCount: number;
      breakdown: {
        baseCost: number;
        languagePairMultiplier: number;
        complexityMultiplier: number;
        totalCost: number;
      };
    }>, {
      sourceText: string;
      sourceLanguage: string;
      targetLanguage: string;
      domain?: string;
    }>({
      query: (data) => ({
        url: '/credits/estimate-cost',
        method: 'POST',
        body: data,
      }),
    }),

    // Get low credit notifications settings
    getCreditNotificationSettings: builder.query<ApiResponse<{
      lowCreditThreshold: number;
      emailNotifications: boolean;
      inAppNotifications: boolean;
    }>, string | void>({
      query: (organizationId) => {
        const params = organizationId ? `?organizationId=${organizationId}` : '';
        return `/credits/notification-settings${params}`;
      },
      providesTags: [{ type: 'Credits', id: 'SETTINGS' }],
    }),

    // Update credit notification settings
    updateCreditNotificationSettings: builder.mutation<ApiResponse<void>, {
      lowCreditThreshold: number;
      emailNotifications: boolean;
      inAppNotifications: boolean;
      organizationId?: string;
    }>({
      query: (data) => ({
        url: '/credits/notification-settings',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: [{ type: 'Credits', id: 'SETTINGS' }],
    }),
  }),
  overrideExisting: true,
});

// Export hooks for use in components
export const {
  useGetCreditsQuery,
  useGetCreditTransactionsQuery,
  useGetCreditUsageStatsQuery,
  usePurchaseCreditsMutation,
  useDeductCreditsMutation,
  useCheckCreditSufficiencyQuery,
  useGetCreditPricingQuery,
  useRefundCreditsMutation,
  useGetCreditTransactionQuery,
  useEstimateTranslationCostMutation,
  useGetCreditNotificationSettingsQuery,
  useUpdateCreditNotificationSettingsMutation,
} = creditsApi;
