import { baseApi } from './base';
import type {
  ApiResponse,
  PaginatedResponse,
  Chapter,
  CreateChapterRequest,
  UpdateChapterRequest,
  GetChaptersResponse,
  GetChapterResponse,
  ChapterProgress,
  TranslationSegment,
} from '@/types';

// Chapter filters for querying
export interface ChapterFilters {
  projectId?: string;
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: 'chapter_number' | 'title' | 'progress_percentage' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

// Chapter statistics
export interface ChapterStats {
  totalChapters: number;
  completedChapters: number;
  inProgressChapters: number;
  pendingChapters: number;
  totalWords: number;
  translatedWords: number;
  averageProgress: number;
}

// Extend the base API with chapter management endpoints
export const chaptersApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get chapters for a project
    getChapters: builder.query<PaginatedResponse<Chapter>, ChapterFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `chapters?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) =>
        result?.data.items
          ? [
              ...result.data.items.map(({ id }) => ({ type: 'Chapter' as const, id })),
              { type: 'Chapter', id: 'LIST' },
            ]
          : [{ type: 'Chapter', id: 'LIST' }],
    }),

    // Get a single chapter with segments
    getChapter: builder.query<ApiResponse<GetChapterResponse>, string>({
      query: (chapterId) => `/chapters/${chapterId}`,
      providesTags: (result, error, chapterId) => [
        { type: 'Chapter', id: chapterId },
        { type: 'Translation', id: 'LIST' },
      ],
    }),

    // Create a new chapter
    createChapter: builder.mutation<ApiResponse<Chapter>, CreateChapterRequest>({
      query: (data) => ({
        url: '/chapters',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Chapter', id: 'LIST' },
        { type: 'Project', id: 'LIST' },
      ],
    }),

    // Update chapter
    updateChapter: builder.mutation<ApiResponse<Chapter>, {
      id: string;
      data: UpdateChapterRequest;
    }>({
      query: ({ id, data }) => ({
        url: `/chapters/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Chapter', id },
        { type: 'Chapter', id: 'LIST' },
      ],
      // Optimistic update
      async onQueryStarted({ id, data }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          chaptersApi.util.updateQueryData('getChapter', id, (draft) => {
            if (draft.data.chapter) {
              Object.assign(draft.data.chapter, data);
            }
          })
        );
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
    }),

    // Delete chapter
    deleteChapter: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/chapters/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [
        { type: 'Chapter', id: 'LIST' },
        { type: 'Project', id: 'LIST' },
      ],
    }),

    // Get chapter statistics for a project
    getChapterStats: builder.query<ApiResponse<ChapterStats>, string>({
      query: (projectId) => `/chapters/stats?projectId=${projectId}`,
      providesTags: (result, error, projectId) => [
        { type: 'Chapter', id: `${projectId}-stats` },
      ],
    }),

    // Get chapter progress
    getChapterProgress: builder.query<ApiResponse<ChapterProgress>, string>({
      query: (chapterId) => `/chapters/${chapterId}/progress`,
      providesTags: (result, error, chapterId) => [
        { type: 'Chapter', id: `${chapterId}-progress` },
      ],
    }),

    // Get segments for a chapter
    getChapterSegments: builder.query<PaginatedResponse<TranslationSegment>, {
      chapterId: string;
      page?: number;
      limit?: number;
      status?: string;
    }>({
      query: ({ chapterId, page = 1, limit = 50, status }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: limit.toString(),
        });
        if (status) params.append('status', status);
        
        return `/chapters/${chapterId}/segments?${params.toString()}`;
      },
      providesTags: (result, error, { chapterId }) => [
        { type: 'Translation', id: `chapter-${chapterId}` },
        { type: 'Chapter', id: chapterId },
      ],
    }),

    // Bulk update chapter segments
    bulkUpdateChapterSegments: builder.mutation<ApiResponse<void>, {
      chapterId: string;
      segmentIds: string[];
      data: Partial<TranslationSegment>;
    }>({
      query: ({ chapterId, segmentIds, data }) => ({
        url: `/chapters/${chapterId}/segments/bulk-update`,
        method: 'PATCH',
        body: { segmentIds, data },
      }),
      invalidatesTags: (result, error, { chapterId }) => [
        { type: 'Translation', id: `chapter-${chapterId}` },
        { type: 'Chapter', id: chapterId },
        { type: 'Chapter', id: `${chapterId}-progress` },
      ],
    }),

    // Estimate chapter translation cost
    estimateChapterCost: builder.mutation<ApiResponse<{
      estimatedCost: number;
      wordCount: number;
      segmentCount: number;
    }>, {
      chapterId: string;
      targetLanguage: string;
    }>({
      query: ({ chapterId, targetLanguage }) => ({
        url: `/chapters/${chapterId}/estimate-cost`,
        method: 'POST',
        body: { targetLanguage },
      }),
    }),

    // Auto-translate chapter with AI
    autoTranslateChapter: builder.mutation<ApiResponse<{
      translatedSegments: number;
      creditsUsed: number;
      remainingCredits: number;
    }>, {
      chapterId: string;
      targetLanguage: string;
      useCredits: boolean;
    }>({
      query: ({ chapterId, targetLanguage, useCredits }) => ({
        url: `/chapters/${chapterId}/auto-translate`,
        method: 'POST',
        body: { targetLanguage, useCredits },
      }),
      invalidatesTags: (result, error, { chapterId }) => [
        { type: 'Translation', id: `chapter-${chapterId}` },
        { type: 'Chapter', id: chapterId },
        { type: 'Chapter', id: `${chapterId}-progress` },
        { type: 'Credits', id: 'BALANCE' },
      ],
    }),

    // Reorder chapters
    reorderChapters: builder.mutation<ApiResponse<void>, {
      projectId: string;
      chapterIds: string[];
    }>({
      query: ({ projectId, chapterIds }) => ({
        url: `/chapters/reorder`,
        method: 'POST',
        body: { projectId, chapterIds },
      }),
      invalidatesTags: [
        { type: 'Chapter', id: 'LIST' },
      ],
    }),
  }),
  overrideExisting: true,
});

// Export hooks for use in components
export const {
  useGetChaptersQuery,
  useGetChapterQuery,
  useCreateChapterMutation,
  useUpdateChapterMutation,
  useDeleteChapterMutation,
  useGetChapterStatsQuery,
  useGetChapterProgressQuery,
  useGetChapterSegmentsQuery,
  useBulkUpdateChapterSegmentsMutation,
  useEstimateChapterCostMutation,
  useAutoTranslateChapterMutation,
  useReorderChaptersMutation,
} = chaptersApi;
