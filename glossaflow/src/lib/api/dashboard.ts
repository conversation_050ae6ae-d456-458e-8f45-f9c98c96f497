import { baseApi } from './base';

// TypeScript interfaces for dashboard data
export interface DashboardStats {
  projects: {
    total: number;
    active: number;
    completed: number;
    pending: number;
    recent: number;
  };
  terminology: {
    total: number;
    approved: number;
    pending: number;
    rejected: number;
    recent: number;
  };
  team: {
    total: number;
    active: number;
    pending: number;
    invitations: number;
  };
  overview: {
    totalProjects: number;
    activeProjects: number;
    totalTerms: number;
    teamMembers: number;
    pendingReviews: number;
    recentActivity: number;
  };
}

export interface DashboardActivity {
  id: string;
  type: 'project' | 'terminology' | 'team' | 'invitation';
  action: string;
  title: string;
  description: string;
  user: {
    id?: string;
    name: string;
    email: string;
    avatar: string | null;
  };
  timestamp: string;
  metadata: Record<string, any>;
}

export interface DashboardActivityResponse {
  activities: DashboardActivity[];
  total: number;
}

export interface RecentProject {
  id: string;
  name: string;
  description: string | null;
  status: string;
  progress: number;
  sourceLanguage: string;
  targetLanguage: string;
  targetLanguages: string[] | string;
  deadline: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface RecentProjectsResponse {
  projects: RecentProject[];
  total: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Dashboard API slice
export const dashboardApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getDashboardStats: builder.query<ApiResponse<DashboardStats>, void>({
      query: () => '/dashboard/stats',
      providesTags: ['Project', 'Terminology', 'TeamMember'],
      // Refetch every 5 minutes
      keepUnusedDataFor: 300,
    }),

    getDashboardActivity: builder.query<ApiResponse<DashboardActivityResponse>, {
      limit?: number;
    }>({
      query: (params = {}) => ({
        url: '/dashboard/activity',
        params: {
          limit: params.limit || 10,
        },
      }),
      providesTags: ['Project', 'Terminology', 'TeamMember', 'TeamInvitation'],
      // Refetch every 2 minutes for activity feed
      keepUnusedDataFor: 120,
    }),

    getRecentProjects: builder.query<ApiResponse<RecentProjectsResponse>, {
      limit?: number;
    }>({
      query: (params = {}) => ({
        url: '/dashboard/recent-projects',
        params: {
          limit: params.limit || 5,
        },
      }),
      providesTags: ['Project'],
      // Refetch every 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Mutation to refresh dashboard data
    refreshDashboard: builder.mutation<void, void>({
      query: () => ({
        url: '/dashboard/refresh',
        method: 'POST',
      }),
      invalidatesTags: ['Project', 'Terminology', 'TeamMember', 'TeamInvitation'],
    }),
  }),
  overrideExisting: true,
});

// Export hooks for use in components
export const {
  useGetDashboardStatsQuery,
  useGetDashboardActivityQuery,
  useGetRecentProjectsQuery,
  useRefreshDashboardMutation,
} = dashboardApi;
