import { baseApi, ApiResponse, PaginatedResponse } from './base';

export interface TerminologyEntry {
  id: string;
  sourceTerm: string;
  targetTerm: string;
  targetLanguage: string;
  category: string;
  context?: string;
  usageNotes?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  frequency: number;
  createdBy: string;
  reviewedBy?: string;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  projectId?: string;
}

export interface CreateTerminologyRequest {
  sourceTerm: string;
  targetTerm: string;
  targetLanguage: string;
  category: string;
  context?: string;
  usageNotes?: string;
  projectId?: string;
}

export interface UpdateTerminologyRequest extends Partial<CreateTerminologyRequest> {
  approvalStatus?: TerminologyEntry['approvalStatus'];
}

export interface TerminologyFilters {
  search?: string;
  category?: string;
  targetLanguage?: string;
  approvalStatus?: TerminologyEntry['approvalStatus'];
  projectId?: string;
  createdBy?: string;
  page?: number;
  limit?: number;
}

export interface ImportTerminologyRequest {
  file: File;
  format: 'csv' | 'tmx' | 'excel';
  mapping: Record<string, string>;
  overwriteExisting: boolean;
}

export interface ImportResult {
  imported: number;
  errors: number;
  duplicates: number;
  total: number;
  errorDetails?: Array<{
    row: number;
    error: string;
    data: any;
  }>;
}

export const terminologyApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTerminology: builder.query<PaginatedResponse<TerminologyEntry>, TerminologyFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `terminology?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) =>
        result?.data.items
          ? [
              ...result.data.items.map(({ id }) => ({ type: 'Terminology' as const, id })),
              { type: 'Terminology', id: 'LIST' },
            ]
          : [{ type: 'Terminology', id: 'LIST' }],
    }),

    getTerminologyStats: builder.query<ApiResponse<{
      total: number;
      approved: number;
      pending: number;
      rejected: number;
      byLanguage: Record<string, number>;
      byCategory: Record<string, number>;
    }>, void>({
      query: () => ({
        url: 'terminology/stats',
        method: 'GET',
      }),
      providesTags: [{ type: 'Terminology', id: 'STATS' }],
    }),

    getTerminologyEntry: builder.query<ApiResponse<TerminologyEntry>, string>({
      query: (id) => ({
        url: `terminology/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Terminology', id }],
    }),

    createTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, CreateTerminologyRequest>({
      query: (data) => ({
        url: 'terminology',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Terminology', id: 'LIST' },
        { type: 'Terminology', id: 'STATS' },
      ],
    }),

    updateTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, {
      id: string;
      data: UpdateTerminologyRequest;
    }>({
      query: ({ id, data }) => ({
        url: `terminology/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Terminology', id },
        { type: 'Terminology', id: 'LIST' },
        { type: 'Terminology', id: 'STATS' },
      ],
    }),

    deleteTerminologyEntry: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `terminology/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Terminology', id },
        { type: 'Terminology', id: 'LIST' },
        { type: 'Terminology', id: 'STATS' },
      ],
    }),

    approveTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, string>({
      query: (id) => ({
        url: `terminology/${id}/approve`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Terminology', id },
        { type: 'Terminology', id: 'LIST' },
        { type: 'Terminology', id: 'STATS' },
      ],
    }),

    rejectTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, string>({
      query: (id) => ({
        url: `terminology/${id}/reject`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Terminology', id },
        { type: 'Terminology', id: 'LIST' },
        { type: 'Terminology', id: 'STATS' },
      ],
    }),

    importTerminology: builder.mutation<ApiResponse<ImportResult>, ImportTerminologyRequest>({
      query: ({ file, format, mapping, overwriteExisting }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('format', format);
        formData.append('mapping', JSON.stringify(mapping));
        formData.append('overwriteExisting', overwriteExisting.toString());
        return {
          url: '/terminology/import',
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: ['Terminology'],
    }),

    exportTerminology: builder.mutation<Blob, {
      format: 'csv' | 'tmx' | 'excel';
      filters?: TerminologyFilters;
    }>({
      query: ({ format, filters }) => ({
        url: '/terminology/export',
        method: 'POST',
        body: { format, filters },
        responseHandler: (response) => response.blob(),
      }),
    }),

    searchTerminology: builder.query<ApiResponse<TerminologyEntry[]>, {
      query: string;
      targetLanguage?: string;
      limit?: number;
    }>({
      query: ({ query, targetLanguage, limit = 10 }) => ({
        url: '/terminology/search',
        params: { q: query, targetLanguage, limit },
      }),
      providesTags: ['Terminology'],
    }),



    getTerminologyForProject: builder.query<ApiResponse<TerminologyEntry[]>, {
      projectId: string;
      sourceText?: string;
    }>({
      query: ({ projectId, sourceText }) => ({
        url: `/terminology/project/${projectId}`,
        params: sourceText ? { sourceText } : undefined,
      }),
      providesTags: (result, error, { projectId }) => [
        { type: 'Terminology', id: `project-${projectId}` }
      ],
    }),
  }),
  overrideExisting: true,
});

export const {
  useGetTerminologyQuery,
  useGetTerminologyStatsQuery,
  useGetTerminologyEntryQuery,
  useCreateTerminologyEntryMutation,
  useUpdateTerminologyEntryMutation,
  useDeleteTerminologyEntryMutation,
  useApproveTerminologyEntryMutation,
  useRejectTerminologyEntryMutation,
  useImportTerminologyMutation,
  useExportTerminologyMutation,
  useSearchTerminologyQuery,
  useGetTerminologyForProjectQuery,
} = terminologyApi;
