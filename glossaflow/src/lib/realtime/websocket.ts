'use client';

import { io, Socket } from 'socket.io-client';

export interface RealtimeEvent {
  type: string;
  payload: any;
  timestamp: number;
  userId?: string;
  projectId?: string;
  segmentId?: string;
}

export interface RealtimeEventHandlers {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
  onProjectUpdate?: (data: any) => void;
  onSegmentUpdate?: (data: any) => void;
  onCommentAdded?: (data: any) => void;
  onUserJoined?: (data: any) => void;
  onUserLeft?: (data: any) => void;
  onTypingStart?: (data: any) => void;
  onTypingStop?: (data: any) => void;
  onNotification?: (data: any) => void;
}

class RealtimeService {
  private socket: Socket | null = null;
  private handlers: RealtimeEventHandlers = {};
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    if (typeof window !== 'undefined') {
      this.connect();
    }
  }

  private connect() {
    try {
      this.socket = io(process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:3001', {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      this.handlers.onError?.(error as Error);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to WebSocket server');
      this.reconnectAttempts = 0;
      this.handlers.onConnect?.();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from WebSocket server:', reason);
      this.handlers.onDisconnect?.();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.handlers.onError?.(error);
      this.handleReconnect();
    });

    // Project-related events
    this.socket.on('project:updated', (data) => {
      this.handlers.onProjectUpdate?.(data);
    });

    // Segment-related events
    this.socket.on('segment:updated', (data) => {
      this.handlers.onSegmentUpdate?.(data);
    });

    // Comment-related events
    this.socket.on('comment:added', (data) => {
      this.handlers.onCommentAdded?.(data);
    });

    // User presence events
    this.socket.on('user:joined', (data) => {
      this.handlers.onUserJoined?.(data);
    });

    this.socket.on('user:left', (data) => {
      this.handlers.onUserLeft?.(data);
    });

    // Typing indicators
    this.socket.on('typing:start', (data) => {
      this.handlers.onTypingStart?.(data);
    });

    this.socket.on('typing:stop', (data) => {
      this.handlers.onTypingStop?.(data);
    });

    // Notifications
    this.socket.on('notification', (data) => {
      this.handlers.onNotification?.(data);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        this.connect();
      }, delay);
    }
  }

  // Public methods
  public setHandlers(handlers: RealtimeEventHandlers) {
    this.handlers = { ...this.handlers, ...handlers };
  }

  public joinProject(projectId: string) {
    this.socket?.emit('project:join', { projectId });
  }

  public leaveProject(projectId: string) {
    this.socket?.emit('project:leave', { projectId });
  }

  public joinSegment(segmentId: string) {
    this.socket?.emit('segment:join', { segmentId });
  }

  public leaveSegment(segmentId: string) {
    this.socket?.emit('segment:leave', { segmentId });
  }

  public updateSegment(segmentId: string, data: any) {
    this.socket?.emit('segment:update', { segmentId, data });
  }

  public addComment(segmentId: string, comment: any) {
    this.socket?.emit('comment:add', { segmentId, comment });
  }

  public startTyping(segmentId: string) {
    this.socket?.emit('typing:start', { segmentId });
  }

  public stopTyping(segmentId: string) {
    this.socket?.emit('typing:stop', { segmentId });
  }

  public sendNotification(userId: string, notification: any) {
    this.socket?.emit('notification:send', { userId, notification });
  }

  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  public getConnectionId(): string | undefined {
    return this.socket?.id;
  }
}

// Singleton instance
let realtimeService: RealtimeService | null = null;

export const getRealtimeService = (): RealtimeService => {
  if (!realtimeService) {
    realtimeService = new RealtimeService();
  }
  return realtimeService;
};

// React hooks for easier integration
export const useRealtime = (handlers: RealtimeEventHandlers) => {
  const service = getRealtimeService();
  
  React.useEffect(() => {
    service.setHandlers(handlers);
    
    return () => {
      // Cleanup handlers on unmount
      service.setHandlers({});
    };
  }, [handlers, service]);

  return service;
};

// Utility functions
export const formatRealtimeEvent = (type: string, payload: any): RealtimeEvent => ({
  type,
  payload,
  timestamp: Date.now(),
});

export const isRecentEvent = (event: RealtimeEvent, maxAgeMs = 30000): boolean => {
  return Date.now() - event.timestamp <= maxAgeMs;
};
