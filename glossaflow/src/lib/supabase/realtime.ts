'use client';

import { createClient } from '@supabase/supabase-js';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export interface RealtimeEventHandlers {
  onProjectUpdate?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onSegmentUpdate?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onCommentAdded?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onUserPresence?: (payload: any) => void;
  onTypingIndicator?: (payload: any) => void;
}

class SupabaseRealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private presenceChannels: Map<string, RealtimeChannel> = new Map();

  // Subscribe to project changes
  subscribeToProject(projectId: string, handlers: RealtimeEventHandlers) {
    const channelName = `project:${projectId}`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'projects',
          filter: `id=eq.${projectId}`,
        },
        (payload) => {
          handlers.onProjectUpdate?.(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'translation_segments',
          filter: `project_id=eq.${projectId}`,
        },
        (payload) => {
          handlers.onSegmentUpdate?.(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'comments',
          filter: `project_id=eq.${projectId}`,
        },
        (payload) => {
          handlers.onCommentAdded?.(payload);
        }
      )
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  // Subscribe to segment-specific changes
  subscribeToSegment(segmentId: string, handlers: RealtimeEventHandlers) {
    const channelName = `segment:${segmentId}`;
    
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'translation_segments',
          filter: `id=eq.${segmentId}`,
        },
        (payload) => {
          handlers.onSegmentUpdate?.(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'comments',
          filter: `segment_id=eq.${segmentId}`,
        },
        (payload) => {
          handlers.onCommentAdded?.(payload);
        }
      )
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  // Subscribe to user presence in a project
  subscribeToPresence(projectId: string, userId: string, userInfo: any, handlers: RealtimeEventHandlers) {
    const channelName = `presence:project:${projectId}`;
    
    if (this.presenceChannels.has(channelName)) {
      return this.presenceChannels.get(channelName)!;
    }

    const channel = supabase
      .channel(channelName, {
        config: {
          presence: {
            key: userId,
          },
        },
      })
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        handlers.onUserPresence?.(state);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        handlers.onUserPresence?.({ type: 'join', key, presences: newPresences });
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        handlers.onUserPresence?.({ type: 'leave', key, presences: leftPresences });
      })
      .on('broadcast', { event: 'typing' }, (payload) => {
        handlers.onTypingIndicator?.(payload);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          await channel.track({
            user_id: userId,
            ...userInfo,
            online_at: new Date().toISOString(),
          });
        }
      });

    this.presenceChannels.set(channelName, channel);
    return channel;
  }

  // Send typing indicator
  sendTypingIndicator(projectId: string, segmentId: string, userId: string, isTyping: boolean) {
    const channelName = `presence:project:${projectId}`;
    const channel = this.presenceChannels.get(channelName);
    
    if (channel) {
      channel.send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          segment_id: segmentId,
          user_id: userId,
          is_typing: isTyping,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }

  // Update user presence
  updatePresence(projectId: string, updates: any) {
    const channelName = `presence:project:${projectId}`;
    const channel = this.presenceChannels.get(channelName);
    
    if (channel) {
      channel.track(updates);
    }
  }

  // Unsubscribe from a channel
  unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName) || this.presenceChannels.get(channelName);
    
    if (channel) {
      supabase.removeChannel(channel);
      this.channels.delete(channelName);
      this.presenceChannels.delete(channelName);
    }
  }

  // Unsubscribe from project
  unsubscribeFromProject(projectId: string) {
    this.unsubscribe(`project:${projectId}`);
    this.unsubscribe(`presence:project:${projectId}`);
  }

  // Unsubscribe from segment
  unsubscribeFromSegment(segmentId: string) {
    this.unsubscribe(`segment:${segmentId}`);
  }

  // Unsubscribe from all channels
  unsubscribeAll() {
    [...this.channels.keys(), ...this.presenceChannels.keys()].forEach(channelName => {
      this.unsubscribe(channelName);
    });
  }

  // Get current presence state
  getPresenceState(projectId: string) {
    const channelName = `presence:project:${projectId}`;
    const channel = this.presenceChannels.get(channelName);
    
    return channel ? channel.presenceState() : {};
  }
}

// Singleton instance
let realtimeService: SupabaseRealtimeService | null = null;

export const getSupabaseRealtimeService = (): SupabaseRealtimeService => {
  if (!realtimeService) {
    realtimeService = new SupabaseRealtimeService();
  }
  return realtimeService;
};

// React hook for Supabase Realtime
export const useSupabaseRealtime = (
  projectId: string,
  userId: string,
  userInfo: any,
  handlers: RealtimeEventHandlers
) => {
  const service = getSupabaseRealtimeService();

  React.useEffect(() => {
    // Subscribe to project and presence
    service.subscribeToProject(projectId, handlers);
    service.subscribeToPresence(projectId, userId, userInfo, handlers);

    return () => {
      service.unsubscribeFromProject(projectId);
    };
  }, [projectId, userId, userInfo, handlers, service]);

  return {
    sendTypingIndicator: (segmentId: string, isTyping: boolean) =>
      service.sendTypingIndicator(projectId, segmentId, userId, isTyping),
    updatePresence: (updates: any) => service.updatePresence(projectId, updates),
    getPresenceState: () => service.getPresenceState(projectId),
  };
};

// Utility functions for real-time operations
export const broadcastSegmentUpdate = async (segmentId: string, updates: any) => {
  try {
    const { error } = await supabase
      .from('translation_segments')
      .update(updates)
      .eq('id', segmentId);

    if (error) throw error;
  } catch (error) {
    console.error('Error broadcasting segment update:', error);
  }
};

export const broadcastComment = async (commentData: any) => {
  try {
    const { error } = await supabase
      .from('comments')
      .insert(commentData);

    if (error) throw error;
  } catch (error) {
    console.error('Error broadcasting comment:', error);
  }
};

export const broadcastProjectUpdate = async (projectId: string, updates: any) => {
  try {
    const { error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', projectId);

    if (error) throw error;
  } catch (error) {
    console.error('Error broadcasting project update:', error);
  }
};
