'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DeleteConfirmDialog } from '@/components/common/DeleteConfirmDialog';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  BookOpen,
  Check,
  X,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { TerminologyTable } from '@/components/terminology/TerminologyTable';
import { CreateTermDialog } from '@/components/terminology/CreateTermDialog';
import { ImportTerminologyDialog } from '@/components/terminology/ImportTerminologyDialog';
import {
  useGetTerminologyQuery,
  useGetTerminologyStatsQuery,
  useCreateTerminologyEntryMutation,
  useUpdateTerminologyEntryMutation,
  useDeleteTerminologyEntryMutation,
  useApproveTerminologyEntryMutation,
  useRejectTerminologyEntryMutation,
  type TerminologyEntry,
  type CreateTerminologyRequest,
  type TerminologyFilters
} from '@/lib/api/terminology';

const categories = ['all', 'technical', 'general', 'character', 'location', 'concept'];
const languages = ['Japanese', 'Spanish', 'French', 'German', 'Korean'];
const statuses = ['all', 'approved', 'pending', 'rejected'];

export default function TerminologyPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [termToDelete, setTermToDelete] = useState<TerminologyEntry | null>(null);

  // Build filters for API query
  const filters: TerminologyFilters = {
    page: currentPage,
    limit: 10,
    ...(searchQuery && { search: searchQuery }),
    ...(selectedCategory !== 'all' && { category: selectedCategory }),
    ...(selectedLanguage !== 'all' && { targetLanguage: selectedLanguage }),
    ...(selectedStatus !== 'all' && {
      approvalStatus: selectedStatus as 'pending' | 'approved' | 'rejected'
    }),
  };

  // API queries
  const {
    data: terminologyResponse,
    isLoading: isLoadingTerminology,
    isError: isTerminologyError,
    refetch: refetchTerminology,
  } = useGetTerminologyQuery(filters, {
    refetchOnFocus: true,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: statsResponse,
    isLoading: isLoadingStats,
  } = useGetTerminologyStatsQuery();

  // Mutations
  const [createTerminologyEntry] = useCreateTerminologyEntryMutation();
  const [updateTerminologyEntry] = useUpdateTerminologyEntryMutation();
  const [deleteTerminologyEntry, { isLoading: isDeleting }] = useDeleteTerminologyEntryMutation();
  const [approveTerminologyEntry] = useApproveTerminologyEntryMutation();
  const [rejectTerminologyEntry] = useRejectTerminologyEntryMutation();

  const terminology = terminologyResponse?.data?.items || [];
  const totalTerminology = terminologyResponse?.data?.total || 0;
  const stats = statsResponse?.data || {
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0,
    byLanguage: {},
    byCategory: {},
  };

  const handleCreateTerm = async (termData: CreateTerminologyRequest) => {
    try {
      await createTerminologyEntry(termData).unwrap();
      setShowCreateDialog(false);
    } catch (error) {
      console.error('Failed to create terminology entry:', error);
    }
  };

  const handleEditTerm = async (term: TerminologyEntry) => {
    // For now, just log - could open an edit dialog
    console.log('Edit term:', term);
  };

  const handleDeleteTerm = async () => {
    if (!termToDelete) return;

    try {
      await deleteTerminologyEntry(termToDelete.id).unwrap();
      setShowDeleteDialog(false);
      setTermToDelete(null);
    } catch (error) {
      console.error('Failed to delete terminology entry:', error);
    }
  };

  const openDeleteDialog = (term: TerminologyEntry) => {
    setTermToDelete(term);
    setShowDeleteDialog(true);
  };

  const handleApproveTerm = async (termId: string) => {
    try {
      await approveTerminologyEntry(termId).unwrap();
    } catch (error) {
      console.error('Failed to approve terminology entry:', error);
    }
  };

  const handleRejectTerm = async (termId: string) => {
    try {
      await rejectTerminologyEntry(termId).unwrap();
    } catch (error) {
      console.error('Failed to reject terminology entry:', error);
    }
  };

  const handleImportTerminology = async (data: any) => {
    console.log('Import terminology:', data);
    setShowImportDialog(false);
    // TODO: Implement import functionality
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Terminology Management</h1>
            <p className="text-gray-600">
              Manage your translation terminology database
              {totalTerminology > 0 && (
                <span className="ml-2 text-sm text-gray-500">
                  ({totalTerminology} total)
                </span>
              )}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchTerminology()}
              disabled={isLoadingTerminology}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingTerminology ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={() => setShowImportDialog(true)}>
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Term
            </Button>
          </div>
        </div>

        {/* Error State */}
        {isTerminologyError && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load terminology. Please try again.
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => refetchTerminology()}
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Terms</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Filter className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <X className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search terms..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Category:</span>
                  {categories.map(category => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Language:</span>
                  <Button
                    variant={selectedLanguage === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedLanguage('all')}
                  >
                    All
                  </Button>
                  {languages.map(language => (
                    <Button
                      key={language}
                      variant={selectedLanguage === language ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedLanguage(language)}
                    >
                      {language}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Status:</span>
                  {statuses.map(status => (
                    <Button
                      key={status}
                      variant={selectedStatus === status ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus(status)}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoadingTerminology && (
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-8 w-24" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Terminology Table */}
        {!isLoadingTerminology && !isTerminologyError && (
          <TerminologyTable
            terminology={terminology}
            onEdit={handleEditTerm}
            onDelete={openDeleteDialog}
            onApprove={handleApproveTerm}
            onReject={handleRejectTerm}
            isLoading={isLoadingTerminology}
          />
        )}

        {/* Empty State */}
        {!isLoadingTerminology && !isTerminologyError && terminology.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <BookOpen className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No terminology found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'Try adjusting your search criteria' : 'Get started by adding your first terminology entry'}
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Term
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Dialogs */}
        <CreateTermDialog
          open={showCreateDialog}
          onOpenChange={setShowCreateDialog}
          onSubmit={handleCreateTerm}
        />

        <ImportTerminologyDialog
          open={showImportDialog}
          onOpenChange={setShowImportDialog}
          onImport={handleImportTerminology}
        />

        <DeleteConfirmDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={handleDeleteTerm}
          title="Delete Terminology Entry"
          description="Are you sure you want to delete this terminology entry? This action cannot be undone."
          itemName={termToDelete?.sourceTerm}
          itemType="terminology entry"
          isLoading={isDeleting}
        />
      </div>
    </DashboardLayout>
  );
}
