'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  User,
  Calendar,
  MessageSquare,
  Filter,
  Search,
  ThumbsUp,
  ThumbsDown,
  Edit,
  Eye,
  MoreHorizontal
} from 'lucide-react';

interface PendingApproval {
  id: string;
  sourceTerm: string;
  targetTerm: string;
  targetLanguage: string;
  category: string;
  context?: string;
  usageNotes?: string;
  projectId: string;
  projectTitle: string;
  submittedBy: string;
  submittedAt: string;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'approved' | 'rejected' | 'changes_requested';
  reviewNotes?: string;
  reviewedBy?: string;
  reviewedAt?: string;
}

export default function TerminologyApprovalsPage() {
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [selectedApproval, setSelectedApproval] = useState<PendingApproval | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [filterStatus, setFilterStatus] = useState('pending');
  const [filterPriority, setFilterPriority] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchPendingApprovals();
  }, []);

  const fetchPendingApprovals = async () => {
    // Mock data for demonstration
    const mockApprovals: PendingApproval[] = [
      {
        id: '1',
        sourceTerm: 'magic sword',
        targetTerm: '魔法の剣',
        targetLanguage: 'ja',
        category: 'item',
        context: 'Legendary weapon used by the protagonist',
        usageNotes: 'Always capitalize when referring to the specific sword',
        projectId: 'proj1',
        projectTitle: 'Fantasy Adventure Vol. 1',
        submittedBy: '<EMAIL>',
        submittedAt: '2024-01-15T10:30:00Z',
        priority: 'high',
        status: 'pending',
      },
      {
        id: '2',
        sourceTerm: 'ancient temple',
        targetTerm: '古代神殿',
        targetLanguage: 'ja',
        category: 'location',
        context: 'Sacred place where the final battle occurs',
        projectId: 'proj1',
        projectTitle: 'Fantasy Adventure Vol. 1',
        submittedBy: '<EMAIL>',
        submittedAt: '2024-01-15T11:00:00Z',
        priority: 'medium',
        status: 'pending',
      },
      {
        id: '3',
        sourceTerm: 'shadow magic',
        targetTerm: '影の魔法',
        targetLanguage: 'ja',
        category: 'concept',
        context: 'Dark magic used by antagonists',
        usageNotes: 'Distinguish from regular magic',
        projectId: 'proj2',
        projectTitle: 'Fantasy Adventure Vol. 2',
        submittedBy: '<EMAIL>',
        submittedAt: '2024-01-15T11:30:00Z',
        priority: 'medium',
        status: 'changes_requested',
        reviewNotes: 'Please provide more context about usage',
        reviewedBy: '<EMAIL>',
        reviewedAt: '2024-01-15T12:00:00Z',
      },
    ];

    setPendingApprovals(mockApprovals);
  };

  const handleApprovalAction = async (
    approvalId: string, 
    action: 'approve' | 'reject' | 'request_changes',
    notes?: string
  ) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update local state
      setPendingApprovals(prev => 
        prev.map(approval => 
          approval.id === approvalId 
            ? {
                ...approval,
                status: action === 'approve' ? 'approved' : 
                       action === 'reject' ? 'rejected' : 'changes_requested',
                reviewNotes: notes,
                reviewedBy: '<EMAIL>',
                reviewedAt: new Date().toISOString(),
              }
            : approval
        )
      );

      setSelectedApproval(null);
      setReviewNotes('');
    } catch (error) {
      console.error('Failed to submit approval action:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredApprovals = pendingApprovals.filter(approval => {
    const matchesStatus = filterStatus === 'all' || approval.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || approval.priority === filterPriority;
    const matchesSearch = !searchQuery || 
      approval.sourceTerm.toLowerCase().includes(searchQuery.toLowerCase()) ||
      approval.targetTerm.toLowerCase().includes(searchQuery.toLowerCase()) ||
      approval.projectTitle.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesStatus && matchesPriority && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'changes_requested': return 'outline';
      default: return 'outline';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const stats = {
    pending: pendingApprovals.filter(a => a.status === 'pending').length,
    approved: pendingApprovals.filter(a => a.status === 'approved').length,
    rejected: pendingApprovals.filter(a => a.status === 'rejected').length,
    changesRequested: pendingApprovals.filter(a => a.status === 'changes_requested').length,
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Terminology Approvals</h1>
            <p className="text-gray-600">Review and approve terminology submissions</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Bulk Actions
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Pending</p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Approved</p>
                  <p className="text-2xl font-bold">{stats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <XCircle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Rejected</p>
                  <p className="text-2xl font-bold">{stats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Edit className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Changes Requested</p>
                  <p className="text-2xl font-bold">{stats.changesRequested}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search terms, projects..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="changes_requested">Changes Requested</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterPriority} onValueChange={setFilterPriority}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Approvals List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* List View */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">
              Pending Approvals ({filteredApprovals.length})
            </h3>
            
            {filteredApprovals.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No approvals match your filters</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {filteredApprovals.map((approval) => (
                  <Card 
                    key={approval.id}
                    className={`cursor-pointer transition-colors ${
                      selectedApproval?.id === approval.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedApproval(approval)}
                  >
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium">"{approval.sourceTerm}"</h4>
                            <p className="text-sm text-blue-600">→ {approval.targetTerm}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getPriorityColor(approval.priority)}>
                              {approval.priority}
                            </Badge>
                            <Badge variant={getStatusColor(approval.status)}>
                              {approval.status.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>{approval.projectTitle}</span>
                          <span>{approval.category}</span>
                        </div>

                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{approval.submittedBy}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(approval.submittedAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Detail View */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Review Details</h3>
            
            {selectedApproval ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>"{selectedApproval.sourceTerm}"</CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge className={getPriorityColor(selectedApproval.priority)}>
                        {selectedApproval.priority}
                      </Badge>
                      <Badge variant={getStatusColor(selectedApproval.status)}>
                        {selectedApproval.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Term Details */}
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Target Translation</label>
                      <div className="mt-1 p-3 bg-blue-50 rounded-lg">
                        <p className="font-medium text-blue-900">{selectedApproval.targetTerm}</p>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-700">Category</label>
                      <div className="mt-1">
                        <Badge variant="outline">{selectedApproval.category}</Badge>
                      </div>
                    </div>

                    {selectedApproval.context && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">Context</label>
                        <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm">{selectedApproval.context}</p>
                        </div>
                      </div>
                    )}

                    {selectedApproval.usageNotes && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">Usage Notes</label>
                        <div className="mt-1 p-3 bg-yellow-50 rounded-lg">
                          <p className="text-sm">{selectedApproval.usageNotes}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Review Notes */}
                  {selectedApproval.status === 'pending' && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Review Notes</label>
                      <Textarea
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        placeholder="Add notes about your review decision..."
                        className="mt-1"
                        rows={3}
                      />
                    </div>
                  )}

                  {/* Previous Review Notes */}
                  {selectedApproval.reviewNotes && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Previous Review Notes</label>
                      <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm">{selectedApproval.reviewNotes}</p>
                        {selectedApproval.reviewedBy && (
                          <p className="text-xs text-gray-500 mt-2">
                            By {selectedApproval.reviewedBy} on {new Date(selectedApproval.reviewedAt!).toLocaleString()}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  {selectedApproval.status === 'pending' && (
                    <div className="flex justify-between pt-4 border-t">
                      <Button
                        variant="outline"
                        onClick={() => handleApprovalAction(selectedApproval.id, 'request_changes', reviewNotes)}
                        disabled={isSubmitting}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Request Changes
                      </Button>

                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => handleApprovalAction(selectedApproval.id, 'reject', reviewNotes)}
                          disabled={isSubmitting}
                        >
                          <ThumbsDown className="mr-2 h-4 w-4" />
                          Reject
                        </Button>
                        <Button
                          onClick={() => handleApprovalAction(selectedApproval.id, 'approve', reviewNotes)}
                          disabled={isSubmitting}
                        >
                          <ThumbsUp className="mr-2 h-4 w-4" />
                          Approve
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Select an approval to review details</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
