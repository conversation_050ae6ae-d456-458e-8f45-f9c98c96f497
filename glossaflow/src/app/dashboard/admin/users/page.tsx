'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal,
  UserPlus,
  Mail,
  Shield,
  Ban,
  Edit,
  Trash2,
  Download,
  Eye
} from 'lucide-react';

// Mock user data
const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'translator',
    status: 'active',
    plan: 'professional',
    joinedAt: '2023-06-15',
    lastActive: '2024-01-16T10:30:00Z',
    projectsCompleted: 24,
    wordsTranslated: 125000,
    avatar: '',
    organization: 'TechCorp Inc.',
    languages: ['English', 'Japanese', 'Korean'],
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'reviewer',
    status: 'active',
    plan: 'enterprise',
    joinedAt: '2023-03-20',
    lastActive: '2024-01-16T08:15:00Z',
    projectsCompleted: 18,
    wordsTranslated: 89000,
    avatar: '',
    organization: 'Global Translations',
    languages: ['English', 'Japanese'],
  },
  {
    id: '3',
    name: 'Emma Davis',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    plan: 'enterprise',
    joinedAt: '2023-01-10',
    lastActive: '2024-01-16T12:45:00Z',
    projectsCompleted: 45,
    wordsTranslated: 0,
    avatar: '',
    organization: 'GlossaFlow',
    languages: ['English'],
  },
  {
    id: '4',
    name: 'Alex Kim',
    email: '<EMAIL>',
    role: 'translator',
    status: 'suspended',
    plan: 'basic',
    joinedAt: '2023-09-05',
    lastActive: '2024-01-10T16:20:00Z',
    projectsCompleted: 12,
    wordsTranslated: 67000,
    avatar: '',
    organization: 'Freelance',
    languages: ['English', 'Korean', 'Japanese'],
  },
  {
    id: '5',
    name: 'Lisa Wang',
    email: '<EMAIL>',
    role: 'project_manager',
    status: 'inactive',
    plan: 'professional',
    joinedAt: '2023-04-12',
    lastActive: '2024-01-05T09:30:00Z',
    projectsCompleted: 8,
    wordsTranslated: 34000,
    avatar: '',
    organization: 'Asia Translations',
    languages: ['English', 'Chinese (Simplified)', 'Chinese (Traditional)'],
  },
];

const roleColors = {
  admin: 'bg-red-100 text-red-800',
  project_manager: 'bg-blue-100 text-blue-800',
  reviewer: 'bg-purple-100 text-purple-800',
  translator: 'bg-green-100 text-green-800',
  client: 'bg-gray-100 text-gray-800',
};

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  suspended: 'bg-red-100 text-red-800',
  pending: 'bg-yellow-100 text-yellow-800',
};

const planColors = {
  basic: 'bg-gray-100 text-gray-800',
  professional: 'bg-blue-100 text-blue-800',
  enterprise: 'bg-purple-100 text-purple-800',
};

export default function AdminUsersPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPlan, setSelectedPlan] = useState('all');

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.organization.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    const matchesStatus = selectedStatus === 'all' || user.status === selectedStatus;
    const matchesPlan = selectedPlan === 'all' || user.plan === selectedPlan;
    return matchesSearch && matchesRole && matchesStatus && matchesPlan;
  });

  const roles = [...new Set(mockUsers.map(user => user.role))];
  const statuses = [...new Set(mockUsers.map(user => user.status))];
  const plans = [...new Set(mockUsers.map(user => user.plan))];

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600">Manage platform users and their permissions</p>
          </div>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{mockUsers.length}</p>
                <p className="text-sm text-gray-600">Total Users</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {mockUsers.filter(u => u.status === 'active').length}
                </p>
                <p className="text-sm text-gray-600">Active Users</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {mockUsers.filter(u => u.plan === 'enterprise').length}
                </p>
                <p className="text-sm text-gray-600">Enterprise Users</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">
                  {mockUsers.filter(u => u.status === 'suspended').length}
                </p>
                <p className="text-sm text-gray-600">Suspended Users</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search users by name, email, or organization..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Role:</span>
                  <Button
                    variant={selectedRole === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedRole('all')}
                  >
                    All
                  </Button>
                  {roles.map(role => (
                    <Button
                      key={role}
                      variant={selectedRole === role ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedRole(role)}
                    >
                      {role.replace('_', ' ')}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Status:</span>
                  <Button
                    variant={selectedStatus === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus('all')}
                  >
                    All
                  </Button>
                  {statuses.map(status => (
                    <Button
                      key={status}
                      variant={selectedStatus === status ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus(status)}
                    >
                      {status}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Plan:</span>
                  <Button
                    variant={selectedPlan === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedPlan('all')}
                  >
                    All
                  </Button>
                  {plans.map(plan => (
                    <Button
                      key={plan}
                      variant={selectedPlan === plan ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedPlan(plan)}
                    >
                      {plan}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card>
          <CardHeader>
            <CardTitle>Users ({filteredUsers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">User</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Role</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Plan</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Activity</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Performance</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-gray-900">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                            <div className="text-xs text-gray-400">{user.organization}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={roleColors[user.role as keyof typeof roleColors]}>
                          {user.role.replace('_', ' ')}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={statusColors[user.status as keyof typeof statusColors]}>
                          {user.status}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className={planColors[user.plan as keyof typeof planColors]}>
                          {user.plan}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <div>Joined {user.joinedAt}</div>
                          <div className="text-gray-500">
                            Last active: {formatLastActive(user.lastActive)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <div>{user.projectsCompleted} projects</div>
                          <div className="text-gray-500">
                            {user.wordsTranslated.toLocaleString()} words
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              Send Message
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Shield className="mr-2 h-4 w-4" />
                              Change Role
                            </DropdownMenuItem>
                            {user.status === 'active' ? (
                              <DropdownMenuItem className="text-orange-600">
                                <Ban className="mr-2 h-4 w-4" />
                                Suspend User
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem className="text-green-600">
                                <Shield className="mr-2 h-4 w-4" />
                                Activate User
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete User
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-12">
                <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                <p className="text-gray-600">Try adjusting your search criteria</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
