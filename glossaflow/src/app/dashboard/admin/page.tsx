'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  FolderOpen, 
  BookOpen, 
  TrendingUp,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Settings
} from 'lucide-react';

// Mock admin data
const mockAdminStats = {
  totalUsers: 1247,
  activeUsers: 892,
  totalProjects: 156,
  activeProjects: 43,
  completedProjects: 98,
  totalRevenue: 125000,
  monthlyRevenue: 18500,
  totalTerms: 45230,
  systemHealth: 98.5,
  storageUsed: 67.3,
  apiCalls: 234567,
};

const mockRecentUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Translator',
    status: 'active',
    joinedAt: '2024-01-15',
    lastActive: '2 hours ago',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Project Manager',
    status: 'active',
    joinedAt: '2024-01-14',
    lastActive: '1 day ago',
  },
  {
    id: '3',
    name: 'David Chen',
    email: '<EMAIL>',
    role: 'Reviewer',
    status: 'pending',
    joinedAt: '2024-01-13',
    lastActive: 'Never',
  },
];

const mockSystemAlerts = [
  {
    id: '1',
    type: 'warning',
    title: 'High API Usage',
    description: 'API usage is approaching monthly limit',
    timestamp: '2 hours ago',
  },
  {
    id: '2',
    type: 'info',
    title: 'Scheduled Maintenance',
    description: 'System maintenance scheduled for this weekend',
    timestamp: '1 day ago',
  },
  {
    id: '3',
    type: 'success',
    title: 'Backup Completed',
    description: 'Daily backup completed successfully',
    timestamp: '6 hours ago',
  },
];

const alertIcons = {
  warning: AlertTriangle,
  info: Activity,
  success: CheckCircle,
  error: AlertTriangle,
};

const alertColors = {
  warning: 'text-yellow-600',
  info: 'text-blue-600',
  success: 'text-green-600',
  error: 'text-red-600',
};

export default function AdminDashboardPage() {
  const [timeRange, setTimeRange] = useState('7d');

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600">System overview and management</p>
          </div>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              System Settings
            </Button>
            <Button>
              <BarChart3 className="mr-2 h-4 w-4" />
              View Reports
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{mockAdminStats.totalUsers.toLocaleString()}</p>
                  <p className="text-xs text-green-600">+12% from last month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FolderOpen className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold text-gray-900">{mockAdminStats.activeProjects}</p>
                  <p className="text-xs text-green-600">+8% from last week</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">${mockAdminStats.monthlyRevenue.toLocaleString()}</p>
                  <p className="text-xs text-green-600">+15% from last month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">System Health</p>
                  <p className="text-2xl font-bold text-gray-900">{mockAdminStats.systemHealth}%</p>
                  <p className="text-xs text-green-600">All systems operational</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>User Growth</CardTitle>
              <CardDescription>New user registrations over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Chart visualization would go here</p>
                  <p className="text-xs text-gray-400">Integration with charting library needed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Status Distribution</CardTitle>
              <CardDescription>Breakdown of project statuses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Completed</span>
                    <span>{mockAdminStats.completedProjects} projects</span>
                  </div>
                  <Progress value={63} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>In Progress</span>
                    <span>{mockAdminStats.activeProjects} projects</span>
                  </div>
                  <Progress value={28} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Pending</span>
                    <span>15 projects</span>
                  </div>
                  <Progress value={9} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity and System Status */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent User Registrations</CardTitle>
              <CardDescription>Latest users who joined the platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" size="sm">
                          {user.role}
                        </Badge>
                        <Badge 
                          variant={user.status === 'active' ? 'default' : 'secondary'}
                          size="sm"
                        >
                          {user.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <p>Joined {user.joinedAt}</p>
                      <p>Last active: {user.lastActive}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>Important system notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockSystemAlerts.map((alert) => {
                  const Icon = alertIcons[alert.type as keyof typeof alertIcons];
                  return (
                    <div key={alert.id} className="flex items-start space-x-3">
                      <Icon className={`h-5 w-5 mt-0.5 ${alertColors[alert.type as keyof typeof alertColors]}`} />
                      <div className="flex-1">
                        <p className="font-medium">{alert.title}</p>
                        <p className="text-sm text-gray-600">{alert.description}</p>
                        <p className="text-xs text-gray-500 mt-1">{alert.timestamp}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Resources */}
        <Card>
          <CardHeader>
            <CardTitle>System Resources</CardTitle>
            <CardDescription>Current system usage and performance metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Storage Usage</span>
                  <span>{mockAdminStats.storageUsed}% used</span>
                </div>
                <Progress value={mockAdminStats.storageUsed} />
                <p className="text-xs text-gray-500 mt-1">2.1 TB of 3.2 TB used</p>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>API Calls (Monthly)</span>
                  <span>{mockAdminStats.apiCalls.toLocaleString()}</span>
                </div>
                <Progress value={78} />
                <p className="text-xs text-gray-500 mt-1">78% of monthly limit</p>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Active Users</span>
                  <span>{mockAdminStats.activeUsers} online</span>
                </div>
                <Progress value={71} />
                <p className="text-xs text-gray-500 mt-1">71% of total users active</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
