'use client';

import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { BillingDashboard } from '@/components/billing/BillingDashboard';
import { SEOHead } from '@/components/seo/SEOHead';

export default function BillingPage() {
  return (
    <>
      <SEOHead
        title="Billing & Subscription"
        description="Manage your GlossaFlow subscription, view billing history, and update payment methods"
        noIndex={true}
      />
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Billing & Subscription</h1>
            <p className="text-gray-600">Manage your subscription and billing information</p>
          </div>
          
          <BillingDashboard />
        </div>
      </DashboardLayout>
    </>
  );
}
