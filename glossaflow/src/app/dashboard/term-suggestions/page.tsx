'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DeleteConfirmDialog } from '@/components/common/DeleteConfirmDialog';
import {
  Search,
  Filter,
  Download,
  Upload,
  Lightbulb,
  Check,
  X,
  AlertCircle,
  RefreshCw,
  Zap,
  TrendingUp,
  Clock,
  Target
} from 'lucide-react';
import { TermCandidatesTable } from '@/components/term-candidates/TermCandidatesTable';
import { ExtractTermsDialog } from '@/components/term-candidates/ExtractTermsDialog';
import { ApproveTermDialog } from '@/components/term-candidates/ApproveTermDialog';
import {
  useGetTermCandidatesQuery,
  useGetTermCandidateStatsQuery,
  useDeleteTermCandidateMutation,
  useApproveTermCandidateMutation,
  useRejectTermCandidateMutation,
  useExtractTermCandidatesMutation,
  type TermCandidate,
  type TermCandidateFilters
} from '@/lib/api/term-candidates';

const statuses = ['all', 'pending', 'approved', 'rejected'];
const sortOptions = [
  { value: 'confidence_score', label: 'Confidence Score' },
  { value: 'frequency', label: 'Frequency' },
  { value: 'created_at', label: 'Date Created' },
  { value: 'candidate_term', label: 'Term Name' },
];

export default function TermSuggestionsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('pending');
  const [selectedProject, setSelectedProject] = useState('all');
  const [sortBy, setSortBy] = useState('confidence_score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [showExtractDialog, setShowExtractDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [candidateToApprove, setCandidateToApprove] = useState<TermCandidate | null>(null);
  const [candidateToDelete, setCandidateToDelete] = useState<TermCandidate | null>(null);

  // Build filters for API query
  const filters: TermCandidateFilters = {
    page: currentPage,
    limit: 20,
    sortBy,
    sortOrder,
    ...(searchQuery && { search: searchQuery }),
    ...(selectedStatus !== 'all' && { status: selectedStatus as 'pending' | 'approved' | 'rejected' }),
    ...(selectedProject !== 'all' && { projectId: selectedProject }),
  };

  // API queries
  const {
    data: candidatesResponse,
    isLoading: isLoadingCandidates,
    isError: isCandidatesError,
    refetch: refetchCandidates,
  } = useGetTermCandidatesQuery(filters, {
    refetchOnFocus: true,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: statsResponse,
    isLoading: isLoadingStats,
  } = useGetTermCandidateStatsQuery({});

  // Mutations
  const [deleteTermCandidate, { isLoading: isDeleting }] = useDeleteTermCandidateMutation();
  const [approveTermCandidate, { isLoading: isApproving }] = useApproveTermCandidateMutation();
  const [rejectTermCandidate, { isLoading: isRejecting }] = useRejectTermCandidateMutation();
  const [extractTermCandidates, { isLoading: isExtracting }] = useExtractTermCandidatesMutation();

  const candidates = candidatesResponse?.data?.items || [];
  const totalCandidates = candidatesResponse?.data?.total || 0;
  const stats = statsResponse?.data?.overview || {
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    recentCandidates: 0,
    approvalRate: 0,
    avgConfidenceScore: 0,
    avgFrequency: 0,
  };

  const handleDeleteCandidate = async () => {
    if (!candidateToDelete) return;

    try {
      await deleteTermCandidate(candidateToDelete.id).unwrap();
      setShowDeleteDialog(false);
      setCandidateToDelete(null);
    } catch (error) {
      console.error('Failed to delete term candidate:', error);
    }
  };

  const openDeleteDialog = (candidate: TermCandidate) => {
    setCandidateToDelete(candidate);
    setShowDeleteDialog(true);
  };

  const openApproveDialog = (candidate: TermCandidate) => {
    setCandidateToApprove(candidate);
    setShowApproveDialog(true);
  };

  const handleApproveCandidate = async (approvalData: any) => {
    if (!candidateToApprove) return;

    try {
      await approveTermCandidate({
        id: candidateToApprove.id,
        data: approvalData,
      }).unwrap();
      setShowApproveDialog(false);
      setCandidateToApprove(null);
    } catch (error) {
      console.error('Failed to approve term candidate:', error);
    }
  };

  const handleRejectCandidate = async (candidateId: string, reason?: string) => {
    try {
      await rejectTermCandidate({
        id: candidateId,
        data: { reason },
      }).unwrap();
    } catch (error) {
      console.error('Failed to reject term candidate:', error);
    }
  };

  const handleExtractTerms = async (extractionData: any) => {
    try {
      await extractTermCandidates(extractionData).unwrap();
      setShowExtractDialog(false);
    } catch (error) {
      console.error('Failed to extract term candidates:', error);
    }
  };

  const handleAssignCategory = async (candidateId: string, category: string) => {
    // This would be handled by the table component
    console.log('Assign category:', candidateId, category);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Term Suggestions</h1>
            <p className="text-gray-600">
              Review and approve automatically extracted terminology candidates
              {totalCandidates > 0 && (
                <span className="ml-2 text-sm text-gray-500">
                  ({totalCandidates} total)
                </span>
              )}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchCandidates()}
              disabled={isLoadingCandidates}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingCandidates ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={() => setShowExtractDialog(true)}>
              <Zap className="mr-2 h-4 w-4" />
              Extract Terms
            </Button>
          </div>
        </div>

        {/* Error State */}
        {isCandidatesError && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load term candidates. Please try again.
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => refetchCandidates()}
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Lightbulb className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Candidates</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Review</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Approval Rate</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.approvalRate}%</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search term candidates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex flex-wrap gap-4">
                <div className="flex gap-1 items-center">
                  <span className="text-sm font-medium text-gray-700">Status:</span>
                  {statuses.map(status => (
                    <Button
                      key={status}
                      variant={selectedStatus === status ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus(status)}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1 items-center">
                  <span className="text-sm font-medium text-gray-700">Sort by:</span>
                  {sortOptions.map(option => (
                    <Button
                      key={option.value}
                      variant={sortBy === option.value ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSortBy(option.value)}
                    >
                      {option.label}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  >
                    {sortOrder === 'asc' ? '↑' : '↓'}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoadingCandidates && (
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-8 w-24" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Term Candidates Table */}
        {!isLoadingCandidates && !isCandidatesError && (
          <TermCandidatesTable
            candidates={candidates}
            onApprove={openApproveDialog}
            onReject={handleRejectCandidate}
            onDelete={openDeleteDialog}
            onAssignCategory={handleAssignCategory}
            isLoading={isLoadingCandidates}
          />
        )}

        {/* Empty State */}
        {!isLoadingCandidates && !isCandidatesError && candidates.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <Lightbulb className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No term candidates found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'Try adjusting your search criteria' : 'Extract terms from your project documents to get started'}
              </p>
              <Button onClick={() => setShowExtractDialog(true)}>
                <Zap className="mr-2 h-4 w-4" />
                Extract Terms
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Dialogs */}
        <ExtractTermsDialog
          open={showExtractDialog}
          onOpenChange={setShowExtractDialog}
          onExtract={handleExtractTerms}
          isLoading={isExtracting}
        />

        <ApproveTermDialog
          open={showApproveDialog}
          onOpenChange={setShowApproveDialog}
          candidate={candidateToApprove}
          onApprove={handleApproveCandidate}
          isLoading={isApproving}
        />

        <DeleteConfirmDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={handleDeleteCandidate}
          title="Delete Term Candidate"
          description="Are you sure you want to delete this term candidate? This action cannot be undone."
          itemName={candidateToDelete?.candidateTerm}
          itemType="term candidate"
          isLoading={isDeleting}
        />
      </div>
    </DashboardLayout>
  );
}
