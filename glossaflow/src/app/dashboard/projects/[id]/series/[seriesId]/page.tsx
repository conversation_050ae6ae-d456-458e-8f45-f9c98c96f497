'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, BookOpen, Loader2, Edit, Save, X, CheckCircle, AlertTriangle } from 'lucide-react';
import { useGetSingleSeriesQuery, useUpdateSeriesMutation } from '@/lib/api/series';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function SeriesDetailPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params.id as string;
  const seriesId = params.seriesId as string;
  const [isEditing, setIsEditing] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [formData, setFormData] = useState({
    chapter_number: '',
    title: '',
    description: '',
    source_word_count: '',
    estimated_cost: '',
  });

  const { data: seriesData, isLoading, error } = useGetSingleSeriesQuery(seriesId);
  const [updateSeries, { isLoading: isUpdating }] = useUpdateSeriesMutation();

  const series = seriesData?.data?.chapter;

  useEffect(() => {
    if (series) {
      setFormData({
        chapter_number: series.chapter_number?.toString() || '',
        title: series.title || '',
        description: series.description || '',
        source_word_count: series.source_word_count?.toString() || '',
        estimated_cost: series.estimated_cost?.toString() || '',
      });
    }
  }, [series]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    if (!formData.chapter_number || !formData.title) {
      setMessage({
        type: 'error',
        text: 'Series number and title are required.'
      });
      return;
    }

    try {
      await updateSeries({
        id: seriesId,
        data: {
          chapter_number: parseInt(formData.chapter_number),
          title: formData.title,
          description: formData.description || undefined,
          source_word_count: formData.source_word_count ? parseInt(formData.source_word_count) : 0,
          estimated_cost: formData.estimated_cost ? parseFloat(formData.estimated_cost) : 0,
        }
      }).unwrap();

      setMessage({
        type: 'success',
        text: 'Series updated successfully!'
      });

      setIsEditing(false);

      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error: any) {
      console.error('Error updating series:', error);
      setMessage({
        type: 'error',
        text: error?.data?.error || 'Failed to update series. Please try again.'
      });
    }
  };

  const handleCancel = () => {
    if (series) {
      setFormData({
        chapter_number: series.chapter_number?.toString() || '',
        title: series.title || '',
        description: series.description || '',
        source_word_count: series.source_word_count?.toString() || '',
        estimated_cost: series.estimated_cost?.toString() || '',
      });
    }
    setIsEditing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !series) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Series Not Found</h1>
          <p className="text-gray-600 mt-2">The series you're looking for doesn't exist or you don't have access to it.</p>
          <Button
            className="mt-4"
            onClick={() => router.push(`/dashboard/projects/${projectId}`)}
          >
            Back to Project
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/projects/${projectId}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Series {series.chapter_number}: {series.title}</h1>
            <p className="text-gray-600">Manage series details and translation progress</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getStatusColor(series.status)}>
            {series.status.replace('_', ' ')}
          </Badge>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button onClick={handleSave} disabled={isUpdating}>
                {isUpdating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                Save
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          {message.type === 'error' ? (
            <AlertTriangle className="h-4 w-4 text-red-600" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-600" />
          )}
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Series Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            Series Details
          </CardTitle>
          <CardDescription>
            {isEditing ? 'Edit the series information below' : 'View and manage series information'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="chapter_number">Series Number</Label>
                {isEditing ? (
                  <Input
                    id="chapter_number"
                    name="chapter_number"
                    type="number"
                    min="1"
                    value={formData.chapter_number}
                    onChange={handleInputChange}
                    required
                  />
                ) : (
                  <p className="text-sm font-medium">{series.chapter_number}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="source_word_count">Word Count</Label>
                {isEditing ? (
                  <Input
                    id="source_word_count"
                    name="source_word_count"
                    type="number"
                    min="0"
                    value={formData.source_word_count}
                    onChange={handleInputChange}
                  />
                ) : (
                  <p className="text-sm font-medium">{series.source_word_count?.toLocaleString() || 0}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Series Title</Label>
              {isEditing ? (
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              ) : (
                <p className="text-sm font-medium">{series.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              {isEditing ? (
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              ) : (
                <p className="text-sm text-gray-600">{series.description || 'No description provided'}</p>
              )}
            </div>

            {!isEditing && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                <div>
                  <Label>Progress</Label>
                  <p className="text-sm font-medium">{Math.round(series.progress_percentage || 0)}%</p>
                </div>
                <div>
                  <Label>Estimated Cost</Label>
                  <p className="text-sm font-medium">{series.estimated_cost || 0} credits</p>
                </div>
                <div>
                  <Label>Actual Cost</Label>
                  <p className="text-sm font-medium">{series.actual_cost || 0} credits</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
