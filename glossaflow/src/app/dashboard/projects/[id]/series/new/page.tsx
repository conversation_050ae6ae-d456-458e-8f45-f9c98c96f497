'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, BookOpen, Loader2, CheckCircle, AlertTriangle } from 'lucide-react';
import { useCreateSeriesMutation } from '@/lib/api/series';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function NewSeriesPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params.id as string;

  const [formData, setFormData] = useState({
    chapter_number: '',
    title: '',
    description: '',
    source_word_count: '',
    estimated_cost: '',
  });

  const [createSeries, { isLoading }] = useCreateSeriesMutation();
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.chapter_number || !formData.title) {
      setMessage({
        type: 'error',
        text: 'Series number and title are required.'
      });
      return;
    }

    try {
      const result = await createSeries({
        project_id: projectId,
        chapter_number: parseInt(formData.chapter_number),
        title: formData.title,
        description: formData.description || undefined,
        source_word_count: formData.source_word_count ? parseInt(formData.source_word_count) : 0,
        estimated_cost: formData.estimated_cost ? parseFloat(formData.estimated_cost) : 0,
      }).unwrap();

      setMessage({
        type: 'success',
        text: 'Series created successfully!'
      });

      // Navigate back to project details after a short delay
      setTimeout(() => {
        router.push(`/dashboard/projects/${projectId}`);
      }, 1500);
    } catch (error: any) {
      console.error('Error creating series:', error);
      setMessage({
        type: 'error',
        text: error?.data?.error || 'Failed to create series. Please try again.'
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/dashboard/projects/${projectId}`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Create New Series</h1>
          <p className="text-gray-600">Add a new series to your translation project</p>
        </div>
      </div>

      {/* Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          {message.type === 'error' ? (
            <AlertTriangle className="h-4 w-4 text-red-600" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-600" />
          )}
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            Series Details
          </CardTitle>
          <CardDescription>
            Enter the details for your new series. The series number should be unique within this project.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="chapter_number">Series Number *</Label>
                <Input
                  id="chapter_number"
                  name="chapter_number"
                  type="number"
                  min="1"
                  value={formData.chapter_number}
                  onChange={handleInputChange}
                  placeholder="e.g., 1"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="source_word_count">Word Count</Label>
                <Input
                  id="source_word_count"
                  name="source_word_count"
                  type="number"
                  min="0"
                  value={formData.source_word_count}
                  onChange={handleInputChange}
                  placeholder="e.g., 5000"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Series Title *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter series title"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Optional description of this series"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="estimated_cost">Estimated Cost (Credits)</Label>
              <Input
                id="estimated_cost"
                name="estimated_cost"
                type="number"
                min="0"
                step="0.01"
                value={formData.estimated_cost}
                onChange={handleInputChange}
                placeholder="e.g., 10.50"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/dashboard/projects/${projectId}`)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <BookOpen className="mr-2 h-4 w-4" />
                    Create Series
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
