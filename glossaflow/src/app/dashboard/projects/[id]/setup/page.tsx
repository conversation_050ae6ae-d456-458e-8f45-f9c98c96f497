'use client';

import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PreTranslationSetup } from '@/components/setup/PreTranslationSetup';

export default function ProjectSetupPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  // Mock project data - in real app, this would be fetched from API
  const projectData = {
    title: 'Fantasy Adventure Series - Volume 1',
    sourceLanguage: 'English',
    targetLanguage: 'Japanese',
  };

  const handleSetupComplete = (setupData: any) => {
    console.log('Setup completed:', setupData);
    // In real app, this would save the setup data to the backend
    
    // Redirect to project detail page
    router.push(`/dashboard/projects/${projectId}`);
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Project Setup</h1>
          <p className="text-gray-600 mt-2">
            Complete the terminology setup before beginning translation work
          </p>
        </div>

        <PreTranslationSetup
          projectId={projectId}
          projectTitle={projectData.title}
          sourceLanguage={projectData.sourceLanguage}
          targetLanguage={projectData.targetLanguage}
          onSetupComplete={handleSetupComplete}
        />
      </div>
    </DashboardLayout>
  );
}
