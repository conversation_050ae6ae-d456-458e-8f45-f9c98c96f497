'use client';

import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TerminologyReviewWorkflow } from '@/components/workflow/TerminologyReviewWorkflow';
import { 
  BookOpen, 
  Plus, 
  Search, 
  Filter,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft
} from 'lucide-react';

export default function ProjectTerminologyPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [activeTab, setActiveTab] = useState('workflow');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data
  const projectData = {
    title: 'Fantasy Adventure Series - Volume 1',
    sourceLanguage: 'English',
    targetLanguage: 'Japanese',
  };

  const terminologyStats = {
    total: 45,
    approved: 32,
    pending: 8,
    rejected: 5,
  };

  const mockTerminology = [
    {
      id: '1',
      sourceTerm: 'magic sword',
      targetTerm: '魔法の剣',
      category: 'item',
      status: 'approved',
      context: 'Legendary weapon used by the protagonist',
      usageNotes: 'Always capitalize when referring to the specific sword',
      frequency: 15,
      lastUsed: '2024-01-15',
    },
    {
      id: '2',
      sourceTerm: 'ancient temple',
      targetTerm: '古代神殿',
      category: 'location',
      status: 'approved',
      context: 'Sacred place where the final battle occurs',
      frequency: 8,
      lastUsed: '2024-01-14',
    },
    {
      id: '3',
      sourceTerm: 'shadow magic',
      targetTerm: '影の魔法',
      category: 'concept',
      status: 'pending',
      context: 'Dark magic used by antagonists',
      usageNotes: 'Distinguish from regular magic',
      frequency: 12,
      lastUsed: '2024-01-13',
    },
  ];

  const handleWorkflowComplete = () => {
    console.log('Terminology workflow completed');
    setActiveTab('overview');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'character': return 'bg-purple-100 text-purple-800';
      case 'location': return 'bg-green-100 text-green-800';
      case 'item': return 'bg-blue-100 text-blue-800';
      case 'concept': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTerminology = mockTerminology.filter(term => {
    const matchesSearch = !searchQuery || 
      term.sourceTerm.toLowerCase().includes(searchQuery.toLowerCase()) ||
      term.targetTerm.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'all' || term.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || term.status === filterStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              onClick={() => router.push(`/dashboard/projects/${projectId}`)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-3xl font-bold">{projectData.title}</h1>
              <p className="text-gray-600">Terminology Management</p>
            </div>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Term
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Terms</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="workflow">Review Workflow</TabsTrigger>
            <TabsTrigger value="overview">All Terms</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="workflow" className="space-y-4">
            <TerminologyReviewWorkflow
              projectId={projectId}
              onWorkflowComplete={handleWorkflowComplete}
            />
          </TabsContent>

          <TabsContent value="overview" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-wrap gap-4">
                  <div className="flex-1 min-w-[200px]">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search terms..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="character">Character</SelectItem>
                      <SelectItem value="location">Location</SelectItem>
                      <SelectItem value="item">Item</SelectItem>
                      <SelectItem value="concept">Concept</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Terminology List */}
            <div className="space-y-3">
              {filteredTerminology.map((term) => (
                <Card key={term.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium">"{term.sourceTerm}"</h4>
                          <span className="text-gray-400">→</span>
                          <span className="text-blue-600 font-medium">{term.targetTerm}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Badge className={getCategoryColor(term.category)}>
                            {term.category}
                          </Badge>
                          <Badge className={getStatusColor(term.status)}>
                            {term.status}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            Used {term.frequency} times
                          </span>
                        </div>

                        {term.context && (
                          <p className="text-sm text-gray-600">{term.context}</p>
                        )}

                        {term.usageNotes && (
                          <p className="text-xs text-gray-500 italic">{term.usageNotes}</p>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                        {term.status === 'pending' && (
                          <Button size="sm">
                            Review
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Terminology Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Analytics and insights coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
