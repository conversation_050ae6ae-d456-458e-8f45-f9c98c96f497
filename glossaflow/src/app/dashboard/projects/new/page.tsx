'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  X, 
  Calendar,
  Users,
  Globe,
  FileText,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';

const languages = [
  'English', 'Japanese', 'Spanish', 'French', 'German', 'Korean',
  'Chinese (Simplified)', 'Chinese (Traditional)', 'Portuguese', 'Italian', 'Russian'
];

const projectTypes = [
  { value: 'document', label: 'Document Translation', description: 'Translate documents, manuals, or reports' },
  { value: 'website', label: 'Website Localization', description: 'Localize websites and web applications' },
  { value: 'software', label: 'Software Localization', description: 'Translate software interfaces and content' },
  { value: 'marketing', label: 'Marketing Content', description: 'Translate marketing materials and campaigns' },
  { value: 'legal', label: 'Legal Documents', description: 'Translate contracts, agreements, and legal texts' },
];

const urgencyLevels = [
  { value: 'low', label: 'Low Priority', description: '2-4 weeks', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'Medium Priority', description: '1-2 weeks', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'High Priority', description: '3-7 days', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', description: '1-2 days', color: 'bg-red-100 text-red-800' },
];

export default function NewProjectPage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: '',
    sourceLanguage: '',
    targetLanguages: [] as string[],
    dueDate: '',
    urgency: '',
    budget: '',
    specialInstructions: '',
    files: [] as File[],
    assignees: [] as string[],
    useTerminology: true,
    requireReview: true,
  });

  const handleNext = () => {
    if (step < 4) setStep(step + 1);
  };

  const handleBack = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleSubmit = () => {
    console.log('Creating project:', formData);
    // TODO: Implement project creation
    router.push('/dashboard/projects');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({
      ...prev,
      files: [...prev.files, ...files]
    }));
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  };

  const toggleTargetLanguage = (language: string) => {
    setFormData(prev => ({
      ...prev,
      targetLanguages: prev.targetLanguages.includes(language)
        ? prev.targetLanguages.filter(l => l !== language)
        : [...prev.targetLanguages, language]
    }));
  };

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create New Project</h1>
          <p className="text-gray-600">Set up a new translation project</p>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          {[1, 2, 3, 4].map((stepNumber) => (
            <div key={stepNumber} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  stepNumber <= step
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {stepNumber}
              </div>
              {stepNumber < 4 && (
                <div
                  className={`w-16 h-1 mx-2 ${
                    stepNumber < step ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Basic Information */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>
                Provide basic information about your translation project
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Project Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter project name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Project Type *</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select project type" />
                    </SelectTrigger>
                    <SelectContent>
                      {projectTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-sm text-gray-500">{type.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your project requirements..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={formData.dueDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">Budget (Optional)</Label>
                  <Input
                    id="budget"
                    placeholder="Enter budget"
                    value={formData.budget}
                    onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Languages */}
        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle>Language Configuration</CardTitle>
              <CardDescription>
                Select source and target languages for your project
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Source Language *</Label>
                <Select
                  value={formData.sourceLanguage}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, sourceLanguage: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select source language" />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map((language) => (
                      <SelectItem key={language} value={language}>
                        {language}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label>Target Languages *</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {languages
                    .filter(lang => lang !== formData.sourceLanguage)
                    .map((language) => (
                      <div
                        key={language}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          formData.targetLanguages.includes(language)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => toggleTargetLanguage(language)}
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={formData.targetLanguages.includes(language)}
                            onChange={() => {}}
                          />
                          <span className="text-sm">{language}</span>
                        </div>
                      </div>
                    ))}
                </div>
                {formData.targetLanguages.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {formData.targetLanguages.map((language) => (
                      <Badge key={language} variant="secondary">
                        {language}
                        <button
                          onClick={() => toggleTargetLanguage(language)}
                          className="ml-2 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>Priority Level</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {urgencyLevels.map((level) => (
                    <div
                      key={level.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        formData.urgency === level.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setFormData(prev => ({ ...prev, urgency: level.value }))}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{level.label}</div>
                          <div className="text-sm text-gray-500">{level.description}</div>
                        </div>
                        <Badge className={level.color}>
                          {level.label.split(' ')[0]}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={step === 1}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          
          {step < 4 ? (
            <Button
              onClick={handleNext}
              disabled={
                (step === 1 && (!formData.name || !formData.type)) ||
                (step === 2 && (!formData.sourceLanguage || formData.targetLanguages.length === 0))
              }
            >
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button onClick={handleSubmit}>
              Create Project
            </Button>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
