'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Loader2,
  FolderOpen,
  BookOpen,
  Users,
  TrendingUp,
  CheckCircle,
  Plus,
  ArrowRight,
  UserPlus
} from 'lucide-react';
import {
  useGetDashboardStatsQuery,
  useGetDashboardActivityQuery,
  useGetRecentProjectsQuery,
  useRefreshDashboardMutation
} from '@/lib/api/dashboard';
import { InviteTeamMemberDialog } from '@/components/team/InviteTeamMemberDialog';

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [showInviteDialog, setShowInviteDialog] = useState(false);

  // RTK Query hooks for dashboard data
  const {
    data: statsResponse,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useGetDashboardStatsQuery();

  const {
    data: activityResponse,
    isLoading: activityLoading,
    error: activityError,
    refetch: refetchActivity
  } = useGetDashboardActivityQuery({ limit: 10 });

  const {
    data: recentProjectsResponse,
    isLoading: projectsLoading,
    error: projectsError,
    refetch: refetchProjects
  } = useGetRecentProjectsQuery({ limit: 5 });

  const [refreshDashboard, { isLoading: isRefreshing }] = useRefreshDashboardMutation();

  // Extract data from API responses
  const stats = statsResponse?.data;
  const activities = activityResponse?.data?.activities || [];
  const recentProjects = recentProjectsResponse?.data?.projects || [];

  // Helper functions
  const handleRefreshDashboard = async () => {
    try {
      await refreshDashboard().unwrap();
      refetchStats();
      refetchActivity();
      refetchProjects();
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    }
  };

  const handleCreateProject = () => {
    router.push('/dashboard/projects/new');
  };

  const handleManageTerminology = () => {
    router.push('/dashboard/terminology');
  };

  const handleViewAllProjects = () => {
    router.push('/dashboard/projects');
  };

  const handleViewAllActivity = () => {
    // Could navigate to a dedicated activity page in the future
    console.log('View all activity');
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'review':
      case 'in_progress':
        return 'secondary';
      case 'on_hold':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getActivityIcon = (type: string, action: string) => {
    switch (type) {
      case 'project':
        return action === 'created' ? <Plus className="h-4 w-4" /> : <FolderOpen className="h-4 w-4" />;
      case 'terminology':
        return <BookOpen className="h-4 w-4" />;
      case 'team':
        return <Users className="h-4 w-4" />;
      case 'invitation':
        return <UserPlus className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getActivityIconColor = (type: string) => {
    switch (type) {
      case 'project':
        return 'text-blue-600 bg-blue-100';
      case 'terminology':
        return 'text-purple-600 bg-purple-100';
      case 'team':
        return 'text-green-600 bg-green-100';
      case 'invitation':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return null; // Will redirect
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome back, {session?.user?.name || 'User'}!
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your translation projects today.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FolderOpen className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Projects</p>
                  {statsLoading ? (
                    <Skeleton className="h-8 w-16 mt-1" />
                  ) : statsError ? (
                    <p className="text-2xl font-bold text-red-500">--</p>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats?.overview.totalProjects || 0}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Projects</p>
                  {statsLoading ? (
                    <Skeleton className="h-8 w-16 mt-1" />
                  ) : statsError ? (
                    <p className="text-2xl font-bold text-red-500">--</p>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats?.overview.activeProjects || 0}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Terminology</p>
                  {statsLoading ? (
                    <Skeleton className="h-8 w-16 mt-1" />
                  ) : statsError ? (
                    <p className="text-2xl font-bold text-red-500">--</p>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats?.overview.totalTerms || 0}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Team Members</p>
                  {statsLoading ? (
                    <Skeleton className="h-8 w-16 mt-1" />
                  ) : statsError ? (
                    <p className="text-2xl font-bold text-red-500">--</p>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats?.overview.teamMembers || 0}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Projects */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Projects</CardTitle>
                <CardDescription>Your latest translation projects</CardDescription>
              </div>
              <Button size="sm" onClick={handleCreateProject}>
                <Plus className="mr-2 h-4 w-4" />
                New Project
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {projectsLoading ? (
                  // Loading skeleton for projects
                  Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <Skeleton className="h-5 w-48" />
                        <Skeleton className="h-5 w-20" />
                      </div>
                      <div className="flex items-center mb-2">
                        <Skeleton className="h-4 w-32" />
                      </div>
                      <div className="flex items-center">
                        <Skeleton className="h-2 flex-1 mr-2" />
                        <Skeleton className="h-4 w-8" />
                      </div>
                    </div>
                  ))
                ) : projectsError ? (
                  <div className="p-4 border rounded-lg border-red-200 bg-red-50">
                    <p className="text-red-600 text-sm">Failed to load recent projects</p>
                  </div>
                ) : recentProjects.length === 0 ? (
                  <div className="p-4 border rounded-lg border-gray-200 bg-gray-50">
                    <p className="text-gray-600 text-sm text-center">No recent projects</p>
                  </div>
                ) : (
                  recentProjects.map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{project.name}</h4>
                          <Badge variant={getStatusBadgeVariant(project.status)}>
                            {project.status === 'in_progress' ? 'In Progress' :
                             project.status === 'review' ? 'In Review' :
                             project.status === 'completed' ? 'Completed' :
                             project.status === 'on_hold' ? 'On Hold' :
                             project.status === 'planning' ? 'Planning' : 'Pending'}
                          </Badge>
                        </div>
                        <div className="flex items-center text-sm text-gray-600 mb-2">
                          <span>Target: {project.targetLanguage}</span>
                          {project.deadline && (
                            <>
                              <span className="mx-2">•</span>
                              <span>Due: {formatDate(project.deadline)}</span>
                            </>
                          )}
                        </div>
                        <div className="flex items-center">
                          <Progress value={project.progress} className="flex-1 mr-2" />
                          <span className="text-sm text-gray-600">{project.progress}%</span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
              <div className="mt-4">
                <Button variant="ghost" className="w-full" onClick={handleViewAllProjects}>
                  View All Projects
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Activity Feed */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates from your team</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activityLoading ? (
                  // Loading skeleton for activity
                  Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <Skeleton className="h-6 w-6 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-3 w-20" />
                      </div>
                    </div>
                  ))
                ) : activityError ? (
                  <div className="p-4 border rounded-lg border-red-200 bg-red-50">
                    <p className="text-red-600 text-sm">Failed to load recent activity</p>
                  </div>
                ) : activities.length === 0 ? (
                  <div className="p-4 border rounded-lg border-gray-200 bg-gray-50">
                    <p className="text-gray-600 text-sm text-center">No recent activity</p>
                  </div>
                ) : (
                  activities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`p-1 rounded-full ${getActivityIconColor(activity.type)}`}>
                        {getActivityIcon(activity.type, activity.action)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm">
                          {activity.description}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
              <div className="mt-4">
                <Button variant="ghost" className="w-full" onClick={handleViewAllActivity}>
                  View All Activity
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Get started with common tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button className="h-20 flex-col" onClick={handleCreateProject}>
                <Plus className="h-6 w-6 mb-2" />
                Create Project
              </Button>
              <Button variant="outline" className="h-20 flex-col" onClick={handleManageTerminology}>
                <BookOpen className="h-6 w-6 mb-2" />
                Manage Terminology
              </Button>
              <Button variant="outline" className="h-20 flex-col" onClick={() => setShowInviteDialog(true)}>
                <UserPlus className="h-6 w-6 mb-2" />
                Invite Team Member
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Invite Team Member Dialog */}
        <InviteTeamMemberDialog
          open={showInviteDialog}
          onOpenChange={setShowInviteDialog}
        />
      </div>
    </DashboardLayout>
  );
}
