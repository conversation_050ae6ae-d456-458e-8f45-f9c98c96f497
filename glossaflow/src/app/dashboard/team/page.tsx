'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DeleteConfirmDialog } from '@/components/common/DeleteConfirmDialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { InviteTeamMemberDialog } from '@/components/team/InviteTeamMemberDialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  MoreHorizontal,
  Users,
  Globe,
  BarChart3,
  Edit,
  Trash2,
  UserPlus,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import {
  useGetTeamMembersQuery,
  useGetTeamStatsQuery,
  useGetTeamRolesQuery,
  useUpdateTeamMemberMutation,
  useDeleteTeamMemberMutation,
  type TeamMember,
  type TeamFilters
} from '@/lib/api/team';

// Constants for styling and options

const roleColors = {
  'Lead Translator': 'bg-purple-100 text-purple-800',
  'Senior Reviewer': 'bg-blue-100 text-blue-800',
  'Project Manager': 'bg-green-100 text-green-800',
  'Translator': 'bg-gray-100 text-gray-800',
  'Reviewer': 'bg-yellow-100 text-yellow-800',
};

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800',
  suspended: 'bg-red-100 text-red-800',
};

const statuses = ['all', 'active', 'inactive', 'pending', 'suspended'];

export default function TeamPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null);
  const [showInviteDialog, setShowInviteDialog] = useState(false);

  // Build filters for API query
  const filters: TeamFilters = {
    page: currentPage,
    limit: 10,
    ...(searchQuery && { search: searchQuery }),
    ...(selectedRole !== 'all' && { role: selectedRole }),
    ...(selectedStatus !== 'all' && {
      status: selectedStatus as 'active' | 'inactive' | 'pending' | 'suspended'
    }),
    ...(selectedLanguage !== 'all' && { language: selectedLanguage }),
  };

  // API queries
  const {
    data: teamResponse,
    isLoading: isLoadingTeam,
    isError: isTeamError,
    refetch: refetchTeam,
  } = useGetTeamMembersQuery(filters, {
    refetchOnFocus: true,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: statsResponse,
    isLoading: isLoadingStats,
  } = useGetTeamStatsQuery();

  const {
    data: rolesResponse,
  } = useGetTeamRolesQuery();

  // Mutations
  const [updateTeamMember] = useUpdateTeamMemberMutation();
  const [deleteTeamMember, { isLoading: isDeleting }] = useDeleteTeamMemberMutation();

  const teamMembers = teamResponse?.data?.items || [];
  const totalMembers = teamResponse?.data?.total || 0;
  const stats = statsResponse?.data || {
    totalMembers: 0,
    activeMembers: 0,
    inactiveMembers: 0,
    pendingInvitations: 0,
    byRole: {},
    byLanguage: {},
    bySpecialization: {},
  };
  const roles = rolesResponse?.data || [];

  const handleEditMember = async (member: TeamMember) => {
    // For now, just log - could open an edit dialog
    console.log('Edit member:', member);
  };

  const handleDeleteMember = async () => {
    if (!memberToDelete) return;

    try {
      await deleteTeamMember(memberToDelete.id).unwrap();
      setShowDeleteDialog(false);
      setMemberToDelete(null);
    } catch (error) {
      console.error('Failed to delete team member:', error);
    }
  };

  const openDeleteDialog = (member: TeamMember) => {
    setMemberToDelete(member);
    setShowDeleteDialog(true);
  };

  const handleStatusChange = async (memberId: string, newStatus: TeamMember['status']) => {
    try {
      await updateTeamMember({
        id: memberId,
        data: { status: newStatus }
      }).unwrap();
    } catch (error) {
      console.error('Failed to update member status:', error);
    }
  };

  // Get unique languages from team members for filter
  const languages = Array.from(new Set(
    teamMembers.flatMap(member => member.languages || [])
  )).sort();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Team Management</h1>
            <p className="text-gray-600">
              Manage your translation team members
              {totalMembers > 0 && (
                <span className="ml-2 text-sm text-gray-500">
                  ({totalMembers} total)
                </span>
              )}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchTeam()}
              disabled={isLoadingTeam}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingTeam ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={() => setShowInviteDialog(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Invite Member
            </Button>
          </div>
        </div>

        {/* Error State */}
        {isTeamError && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load team members. Please try again.
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => refetchTeam()}
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Members</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.totalMembers}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Members</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.activeMembers}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Languages</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">
                      {Object.keys(stats.byLanguage).length}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Invites</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-16" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">{stats.pendingInvitations}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search team members..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedRole === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedRole('all')}
                >
                  All Roles
                </Button>
                {roles.map(role => (
                  <Button
                    key={role.id}
                    variant={selectedRole === role.name ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedRole(role.name)}
                  >
                    {role.name}
                  </Button>
                ))}
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedStatus === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('all')}
                >
                  All Status
                </Button>
                {statuses.map(status => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoadingTeam && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div>
                        <Skeleton className="h-5 w-32 mb-2" />
                        <Skeleton className="h-4 w-48" />
                      </div>
                    </div>
                    <Skeleton className="h-8 w-8" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-6 w-24" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                    <Skeleton className="h-16 w-full" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Team Members Grid */}
        {!isLoadingTeam && !isTeamError && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {teamMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={member.user?.avatar} />
                      <AvatarFallback>
                        {member.user?.name?.split(' ').map((n: string) => n[0]).join('') ||
                         member.user?.email?.slice(0, 2).toUpperCase() || '??'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">{member.user?.name || 'Unknown'}</CardTitle>
                      <p className="text-sm text-gray-600">{member.user?.email}</p>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleStatusChange(member.id, member.status === 'active' ? 'inactive' : 'active')}
                      >
                        <Users className="mr-2 h-4 w-4" />
                        {member.status === 'active' ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => openDeleteDialog(member)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className={roleColors[member.roleName as keyof typeof roleColors] || 'bg-gray-100 text-gray-800'}>
                      {member.roleName}
                    </Badge>
                    <Badge className={statusColors[member.status as keyof typeof statusColors]}>
                      {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                    </Badge>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">Languages</p>
                    <div className="flex flex-wrap gap-1">
                      {member.languages?.map((lang) => (
                        <Badge key={lang} variant="outline" className="text-xs">
                          {lang}
                        </Badge>
                      )) || <span className="text-xs text-gray-500">No languages specified</span>}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">Specializations</p>
                    <div className="flex flex-wrap gap-1">
                      {member.specializations?.map((spec) => (
                        <Badge key={spec} variant="outline" className="text-xs">
                          {spec}
                        </Badge>
                      )) || <span className="text-xs text-gray-500">No specializations specified</span>}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-gray-600">Projects</p>
                      <p>{member.stats?.projectsCompleted || 0} completed</p>
                      <p>{member.stats?.currentProjects || 0} active</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-600">Performance</p>
                      <p>⭐ {member.stats?.averageRating || 0}/5.0</p>
                      <p>{(member.stats?.wordsTranslated || 0).toLocaleString()} words</p>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500">
                    Joined {new Date(member.joinedAt).toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoadingTeam && !isTeamError && teamMembers.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <Search className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No team members found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search criteria or invite new members to join your team.
              </p>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                Invite Team Member
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={handleDeleteMember}
          title="Remove Team Member"
          description="Are you sure you want to remove this team member? This action cannot be undone."
          itemName={memberToDelete?.user?.name || memberToDelete?.user?.email}
          itemType="team member"
          isLoading={isDeleting}
        />

        {/* Invite Team Member Dialog */}
        <InviteTeamMemberDialog
          open={showInviteDialog}
          onOpenChange={setShowInviteDialog}
        />
      </div>
    </DashboardLayout>
  );
}
