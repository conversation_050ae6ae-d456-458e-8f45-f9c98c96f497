'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TerminologyConsistencyReport } from '@/components/reports/TerminologyConsistencyReport';
import { 
  BookOpen, 
  Settings, 
  Sync, 
  AlertTriangle, 
  CheckCircle,
  Plus,
  Download,
  Upload,
  Target,
  Users,
  BarChart3,
  RefreshCw
} from 'lucide-react';

interface SeriesTerminologyPageProps {
  params: {
    seriesId: string;
  };
}

export default function SeriesTerminologyPage({ params }: SeriesTerminologyPageProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('ja');

  // Mock data
  const seriesData = {
    id: params.seriesId,
    title: 'Fantasy Adventure Series',
    projectCount: 3,
    totalTerms: 245,
    consistencyScore: 0.87,
    lastSync: '2024-01-15T10:30:00Z',
  };

  const projects = [
    { id: '1', title: 'Volume 1: The Beginning', termCount: 89, score: 0.92, status: 'active' },
    { id: '2', title: 'Volume 2: The Journey', termCount: 156, score: 0.85, status: 'active' },
    { id: '3', title: 'Volume 3: The Destination', termCount: 78, score: 0.84, status: 'draft' },
  ];

  const conflictingTerms = [
    {
      sourceTerm: 'magic sword',
      translations: [
        { projectId: '1', translation: '魔法の剣', frequency: 12 },
        { projectId: '2', translation: '魔剣', frequency: 8 },
        { projectId: '3', translation: '魔法剣', frequency: 5 },
      ],
      recommendedTranslation: '魔法の剣',
    },
    {
      sourceTerm: 'ancient temple',
      translations: [
        { projectId: '1', translation: '古代神殿', frequency: 6 },
        { projectId: '2', translation: '古い神殿', frequency: 4 },
      ],
      recommendedTranslation: '古代神殿',
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">{seriesData.title}</h1>
            <p className="text-gray-600">Series Terminology Management</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button size="sm">
              <Sync className="mr-2 h-4 w-4" />
              Sync All
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Projects</p>
                  <p className="text-2xl font-bold">{seriesData.projectCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Target className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Total Terms</p>
                  <p className="text-2xl font-bold">{seriesData.totalTerms}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm font-medium">Consistency</p>
                  <p className="text-2xl font-bold">{Math.round(seriesData.consistencyScore * 100)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Conflicts</p>
                  <p className="text-2xl font-bold">{conflictingTerms.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="conflicts">Conflicts</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Terminology synced across 3 projects</p>
                        <p className="text-xs text-gray-500">2 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">2 new conflicts detected</p>
                        <p className="text-xs text-gray-500">1 day ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Plus className="h-4 w-4 text-blue-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">15 new terms added to Volume 3</p>
                        <p className="text-xs text-gray-500">2 days ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button className="w-full justify-start">
                      <Sync className="mr-2 h-4 w-4" />
                      Sync Terminology Across Projects
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      Resolve All Conflicts
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Download className="mr-2 h-4 w-4" />
                      Export Series Glossary
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Generate Consistency Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="projects" className="space-y-4">
            <div className="space-y-4">
              {projects.map((project) => (
                <Card key={project.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <h4 className="font-medium">{project.title}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>{project.termCount} terms</span>
                          <span>Consistency: {Math.round(project.score * 100)}%</span>
                          <Badge variant={project.status === 'active' ? 'default' : 'secondary'}>
                            {project.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          View Terms
                        </Button>
                        <Button variant="outline" size="sm">
                          <Sync className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="conflicts" className="space-y-4">
            <div className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {conflictingTerms.length} terminology conflicts detected across projects. 
                  Resolve these to improve series consistency.
                </AlertDescription>
              </Alert>

              {conflictingTerms.map((conflict, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">"{conflict.sourceTerm}"</h4>
                        <Badge variant="destructive">Conflict</Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Current Translations:</p>
                        {conflict.translations.map((translation, idx) => (
                          <div key={idx} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm">
                              {projects.find(p => p.id === translation.projectId)?.title}: 
                              <strong className="ml-1">{translation.translation}</strong>
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {translation.frequency}x
                            </Badge>
                          </div>
                        ))}
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t">
                        <div>
                          <p className="text-sm font-medium">Recommended: 
                            <span className="text-green-600 ml-1">{conflict.recommendedTranslation}</span>
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Review
                          </Button>
                          <Button size="sm">
                            Apply Fix
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <TerminologyConsistencyReport seriesId={params.seriesId} />
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Series Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Default Target Language</label>
                  <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ja">Japanese</SelectItem>
                      <SelectItem value="ko">Korean</SelectItem>
                      <SelectItem value="zh">Chinese</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Auto-sync Frequency</label>
                  <Select defaultValue="daily">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="realtime">Real-time</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="manual">Manual only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="pt-4">
                  <Button>Save Settings</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
