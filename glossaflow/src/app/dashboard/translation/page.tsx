'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DataTable } from '@/components/common/DataTable';
import { StatusBadge } from '@/components/common/StatusBadge';
import { SEOHead } from '@/components/seo/SEOHead';
import {
  Search,
  Filter,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  MoreHorizontal,
  Eye,
  Edit,
  MessageSquare,
} from 'lucide-react';

// Mock data for translation segments
const mockSegments = [
  {
    id: '1',
    projectName: 'Website Localization',
    fileName: 'homepage.json',
    segmentNumber: 1,
    sourceText: 'Welcome to our platform',
    targetText: 'Bienvenido a nuestra plataforma',
    status: 'completed',
    wordCount: 4,
    assignedTo: '<PERSON>',
    deadline: '2024-02-15',
    progress: 100,
  },
  {
    id: '2',
    projectName: 'Mobile App',
    fileName: 'navigation.json',
    segmentNumber: 5,
    sourceText: 'Settings and preferences',
    targetText: '',
    status: 'in_progress',
    wordCount: 3,
    assignedTo: 'Mike Johnson',
    deadline: '2024-02-20',
    progress: 0,
  },
  {
    id: '3',
    projectName: 'Documentation',
    fileName: 'user-guide.md',
    segmentNumber: 12,
    sourceText: 'Click the button to continue',
    targetText: 'Haga clic en el botón para continuar',
    status: 'review',
    wordCount: 6,
    assignedTo: 'Sarah Chen',
    deadline: '2024-02-18',
    progress: 100,
  },
];

const columns = [
  {
    key: 'projectName' as const,
    label: 'Project',
    sortable: true,
  },
  {
    key: 'fileName' as const,
    label: 'File',
    sortable: true,
  },
  {
    key: 'segmentNumber' as const,
    label: 'Segment',
    sortable: true,
  },
  {
    key: 'sourceText' as const,
    label: 'Source Text',
    render: (value: string) => (
      <div className="max-w-xs truncate" title={value}>
        {value}
      </div>
    ),
  },
  {
    key: 'status' as const,
    label: 'Status',
    render: (value: string) => (
      <StatusBadge status={value as any} />
    ),
  },
  {
    key: 'wordCount' as const,
    label: 'Words',
    sortable: true,
  },
  {
    key: 'assignedTo' as const,
    label: 'Assigned To',
    sortable: true,
  },
  {
    key: 'deadline' as const,
    label: 'Deadline',
    sortable: true,
    render: (value: string) => new Date(value).toLocaleDateString(),
  },
];

export default function TranslationPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const filteredSegments = mockSegments.filter(segment => {
    const matchesSearch = segment.sourceText.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         segment.projectName.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || segment.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: mockSegments.length,
    pending: mockSegments.filter(s => s.status === 'pending').length,
    inProgress: mockSegments.filter(s => s.status === 'in_progress').length,
    completed: mockSegments.filter(s => s.status === 'completed').length,
    review: mockSegments.filter(s => s.status === 'review').length,
  };

  return (
    <>
      <SEOHead
        title="Translation Workspace"
        description="Manage your translation assignments and work on translation segments"
        noIndex={true}
      />
      
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Translation Workspace</h1>
              <p className="text-gray-600">Manage your translation assignments and segments</p>
            </div>
            <Button>
              <Play className="mr-2 h-4 w-4" />
              Start Translating
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Segments</p>
                    <p className="text-2xl font-bold">{stats.total}</p>
                  </div>
                  <FileText className="h-8 w-8 text-gray-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">In Progress</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.inProgress}</p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">In Review</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.review}</p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-yellow-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Pending</p>
                    <p className="text-2xl font-bold text-gray-600">{stats.pending}</p>
                  </div>
                  <Clock className="h-8 w-8 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="my-assignments" className="space-y-4">
            <TabsList>
              <TabsTrigger value="my-assignments">My Assignments</TabsTrigger>
              <TabsTrigger value="available">Available Work</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>

            <TabsContent value="my-assignments" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>My Translation Assignments</CardTitle>
                  <CardDescription>
                    Segments assigned to you for translation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search segments..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="px-3 py-2 border rounded-md"
                    >
                      <option value="all">All Status</option>
                      <option value="pending">Pending</option>
                      <option value="in_progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="review">In Review</option>
                    </select>
                  </div>

                  <DataTable
                    data={filteredSegments}
                    columns={columns}
                    searchable={false}
                    actions={(row) => (
                      <>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="available" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Available Translation Work</CardTitle>
                  <CardDescription>
                    Unassigned segments available for translation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">No available work</h3>
                    <p className="text-sm">All segments are currently assigned. Check back later for new work.</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="completed" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Completed Translations</CardTitle>
                  <CardDescription>
                    Your completed translation work
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <DataTable
                    data={mockSegments.filter(s => s.status === 'completed')}
                    columns={columns}
                    searchable={true}
                    searchPlaceholder="Search completed work..."
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DashboardLayout>
    </>
  );
}
