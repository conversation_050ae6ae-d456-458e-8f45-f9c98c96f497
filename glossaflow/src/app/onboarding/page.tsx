'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, ArrowRight, Building, Users, Globe, CheckCircle } from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

interface OnboardingData {
  organizationName: string;
  industry: string;
  teamSize: string;
  primaryLanguages: string[];
  projectTypes: string[];
  subscriptionTier: string;
}

const industries = [
  'publishing',
  'gaming',
  'software',
  'marketing',
  'legal',
  'medical',
  'education',
  'entertainment',
  'other'
];

const teamSizes = [
  '1',
  '2-5',
  '6-20',
  '21-50',
  '51-200',
  '200+'
];

const languages = [
  { code: 'en', name: 'English' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'zh', name: 'Chinese' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ru', name: 'Russian' },
];

const projectTypes = [
  'novel',
  'game',
  'technical',
  'marketing'
];

const subscriptionTiers = [
  { id: 'starter', name: 'Starter', price: 'Free', description: 'Perfect for small teams' },
  { id: 'professional', name: 'Professional', price: '$29/month', description: 'For growing agencies' },
  { id: 'enterprise', name: 'Enterprise', price: '$99/month', description: 'For large organizations' },
];

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [data, setData] = useState<OnboardingData>({
    organizationName: '',
    industry: '',
    teamSize: '',
    primaryLanguages: [],
    projectTypes: [],
    subscriptionTier: 'starter',
  });
  const router = useRouter();

  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleLanguageToggle = (languageCode: string) => {
    setData(prev => ({
      ...prev,
      primaryLanguages: prev.primaryLanguages.includes(languageCode)
        ? prev.primaryLanguages.filter(l => l !== languageCode)
        : [...prev.primaryLanguages, languageCode]
    }));
  };

  const handleProjectTypeToggle = (type: string) => {
    setData(prev => ({
      ...prev,
      projectTypes: prev.projectTypes.includes(type)
        ? prev.projectTypes.filter(t => t !== type)
        : [...prev.projectTypes, type]
    }));
  };

  const handleComplete = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Here you would typically create the organization and set up the user
      // For now, we'll just simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Redirect to dashboard
      router.push('/dashboard');
    } catch (error) {
      setError('Failed to complete setup. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return data.organizationName.trim() && data.industry && data.teamSize;
      case 2:
        return data.primaryLanguages.length > 0;
      case 3:
        return data.projectTypes.length > 0;
      case 4:
        return true;
      default:
        return false;
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Welcome to GlossaFlow</h1>
            <p className="mt-2 text-gray-600">Let's set up your translation workspace</p>
          </div>

          <div className="mb-8">
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-gray-600 mt-2 text-center">
              Step {currentStep} of {totalSteps}
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {currentStep === 1 && <Building className="mr-2 h-5 w-5" />}
                {currentStep === 2 && <Globe className="mr-2 h-5 w-5" />}
                {currentStep === 3 && <Users className="mr-2 h-5 w-5" />}
                {currentStep === 4 && <CheckCircle className="mr-2 h-5 w-5" />}
                {currentStep === 1 && 'Organization Details'}
                {currentStep === 2 && 'Language Preferences'}
                {currentStep === 3 && 'Project Types'}
                {currentStep === 4 && 'Choose Your Plan'}
              </CardTitle>
              <CardDescription>
                {currentStep === 1 && 'Tell us about your organization'}
                {currentStep === 2 && 'What languages do you work with?'}
                {currentStep === 3 && 'What types of content do you translate?'}
                {currentStep === 4 && 'Select the plan that fits your needs'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="organizationName">Organization Name</Label>
                    <Input
                      id="organizationName"
                      value={data.organizationName}
                      onChange={(e) => setData(prev => ({ ...prev, organizationName: e.target.value }))}
                      placeholder="Enter your organization name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="industry">Industry</Label>
                    <Select value={data.industry} onValueChange={(value) => setData(prev => ({ ...prev, industry: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your industry" />
                      </SelectTrigger>
                      <SelectContent>
                        {industries.map(industry => (
                          <SelectItem key={industry} value={industry}>
                            {industry.charAt(0).toUpperCase() + industry.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="teamSize">Team Size</Label>
                    <Select value={data.teamSize} onValueChange={(value) => setData(prev => ({ ...prev, teamSize: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your team size" />
                      </SelectTrigger>
                      <SelectContent>
                        {teamSizes.map(size => (
                          <SelectItem key={size} value={size}>
                            {size} {size === '1' ? 'person' : 'people'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Select the languages you commonly work with (you can add more later):
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    {languages.map(language => (
                      <div key={language.code} className="flex items-center space-x-2">
                        <Checkbox
                          id={language.code}
                          checked={data.primaryLanguages.includes(language.code)}
                          onCheckedChange={() => handleLanguageToggle(language.code)}
                        />
                        <Label htmlFor={language.code}>{language.name}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    What types of content do you typically translate?
                  </p>
                  <div className="grid grid-cols-2 gap-3">
                    {projectTypes.map(type => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={type}
                          checked={data.projectTypes.includes(type)}
                          onCheckedChange={() => handleProjectTypeToggle(type)}
                        />
                        <Label htmlFor={type}>
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-4">
                  {subscriptionTiers.map(tier => (
                    <Card 
                      key={tier.id} 
                      className={`cursor-pointer transition-colors ${
                        data.subscriptionTier === tier.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => setData(prev => ({ ...prev, subscriptionTier: tier.id }))}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold">{tier.name}</h3>
                            <p className="text-sm text-gray-600">{tier.description}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold">{tier.price}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              <div className="flex justify-between pt-6">
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={currentStep === 1}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>

                {currentStep < totalSteps ? (
                  <Button
                    onClick={handleNext}
                    disabled={!canProceed()}
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleComplete}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Setting up...' : 'Complete Setup'}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
