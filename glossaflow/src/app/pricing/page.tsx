'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PricingPlans } from '@/components/billing/PricingPlans';
import { SEOHead } from '@/components/seo/SEOHead';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Check, 
  ArrowRight, 
  Users, 
  Globe, 
  Shield, 
  Zap,
  HeadphonesIcon,
  Star
} from 'lucide-react';

const features = [
  {
    icon: Users,
    title: 'Team Collaboration',
    description: 'Work together with translators, reviewers, and project managers in real-time.',
  },
  {
    icon: Globe,
    title: 'Multi-language Support',
    description: 'Support for 100+ languages with advanced typography and RTL text support.',
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'SOC 2 compliant with end-to-end encryption and advanced access controls.',
  },
  {
    icon: Zap,
    title: 'AI-Powered Tools',
    description: 'Leverage AI for translation suggestions, quality checks, and terminology management.',
  },
];

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Translation Manager',
    company: 'TechCorp',
    content: '<PERSON>lossa<PERSON><PERSON> has transformed our localization workflow. We\'ve reduced project turnaround time by 40%.',
    rating: 5,
  },
  {
    name: '<PERSON>',
    role: 'Freelance Translator',
    company: 'Independent',
    content: 'The terminology management and CAT tools are exceptional. It\'s like having a professional translation suite in the cloud.',
    rating: 5,
  },
  {
    name: 'Emma <PERSON>',
    role: 'Localization Director',
    company: 'Global Games',
    content: 'The collaboration features and project management tools have streamlined our entire localization process.',
    rating: 5,
  },
];

const faqs = [
  {
    question: 'Can I change my plan at any time?',
    answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we\'ll prorate any billing adjustments.',
  },
  {
    question: 'Is there a free trial?',
    answer: 'Yes, all paid plans come with a 14-day free trial. No credit card required to start.',
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards, PayPal, and bank transfers for enterprise customers.',
  },
  {
    question: 'Can I cancel my subscription?',
    answer: 'Yes, you can cancel your subscription at any time. You\'ll continue to have access until the end of your current billing period.',
  },
  {
    question: 'Do you offer discounts for non-profits?',
    answer: 'Yes, we offer special pricing for non-profit organizations and educational institutions. Contact our sales team for details.',
  },
];

export default function PricingPage() {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const handleSelectPlan = (planId: string, interval: 'month' | 'year') => {
    setSelectedPlan(planId);
    if (planId === 'basic') {
      router.push('/auth/signup');
    } else {
      router.push(`/checkout?plan=${planId}&interval=${interval}`);
    }
  };

  return (
    <>
      <SEOHead
        title="Pricing Plans"
        description="Choose the perfect GlossaFlow plan for your translation needs. Free trial available for all plans."
        keywords={['pricing', 'plans', 'subscription', 'translation software', 'CAT tool pricing']}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-blue-600">GlossaFlow</h1>
              </div>
              <div className="flex items-center space-x-4">
                <Button variant="outline" onClick={() => router.push('/auth/signin')}>
                  Sign In
                </Button>
                <Button onClick={() => router.push('/auth/signup')}>
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Hero Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center space-y-6">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              Simple, Transparent Pricing
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the perfect plan for your translation needs. Start with our free tier 
              and upgrade as you grow. All plans include a 14-day free trial.
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                No setup fees
              </div>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Cancel anytime
              </div>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                24/7 support
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Plans */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <PricingPlans onSelectPlan={handleSelectPlan} showCurrentPlan={false} />
        </div>

        {/* Features Section */}
        <div className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-6 mb-16">
              <h2 className="text-3xl font-bold text-gray-900">
                Everything you need for professional translation
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our platform includes all the tools and features you need to manage 
                translation projects efficiently and deliver high-quality results.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="text-center space-y-4">
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-6 mb-16">
              <h2 className="text-3xl font-bold text-gray-900">
                Trusted by translation professionals worldwide
              </h2>
              <p className="text-xl text-gray-600">
                See what our customers have to say about GlossaFlow
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      {Array.from({ length: testimonial.rating }).map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-gray-700 mb-4">"{testimonial.content}"</p>
                    <div>
                      <p className="font-semibold text-gray-900">{testimonial.name}</p>
                      <p className="text-sm text-gray-600">
                        {testimonial.role} at {testimonial.company}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-6 mb-16">
              <h2 className="text-3xl font-bold text-gray-900">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Have questions? We have answers.
              </p>
            </div>

            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {faq.question}
                    </h3>
                    <p className="text-gray-600">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-blue-600 py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to get started?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of translation professionals who trust GlossaFlow
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                variant="secondary"
                onClick={() => router.push('/auth/signup')}
              >
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="text-white border-white hover:bg-white hover:text-blue-600"
                onClick={() => router.push('/contact')}
              >
                <HeadphonesIcon className="mr-2 h-5 w-5" />
                Contact Sales
              </Button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-900 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <p className="text-gray-400">
                © 2024 GlossaFlow. All rights reserved.
              </p>
              <div className="flex items-center space-x-6 text-gray-400">
                <a href="/privacy" className="hover:text-white">Privacy</a>
                <a href="/terms" className="hover:text-white">Terms</a>
                <a href="/contact" className="hover:text-white">Contact</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
