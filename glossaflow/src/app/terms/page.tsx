'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SEOHead } from '@/components/seo/SEOHead';
import { ArrowLeft, FileText, Scale, AlertTriangle, Shield, CreditCard, Users } from 'lucide-react';

export default function TermsPage() {
  const router = useRouter();

  return (
    <>
      <SEOHead
        title="Terms of Service"
        description="Read the GlossaFlow Terms of Service and understand your rights and responsibilities"
        keywords={['terms of service', 'terms and conditions', 'legal', 'agreement']}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => router.back()}
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <h1 className="text-2xl font-bold text-blue-600">GlossaFlow</h1>
              </div>
              <div className="flex items-center space-x-4">
                <Button variant="outline" onClick={() => router.push('/auth/signin')}>
                  Sign In
                </Button>
                <Button onClick={() => router.push('/auth/signup')}>
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <Scale className="h-12 w-12 text-blue-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Terms of Service
            </h1>
            <p className="text-lg text-gray-600">
              Last updated: February 10, 2024
            </p>
          </div>

          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Agreement to Terms
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  By accessing and using GlossaFlow ("Service"), you accept and agree to be bound by 
                  the terms and provision of this agreement. If you do not agree to abide by the above, 
                  please do not use this service.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Use License
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600">
                  Permission is granted to temporarily use GlossaFlow for personal and commercial purposes. 
                  This is the grant of a license, not a transfer of title, and under this license you may not:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Modify or copy the materials</li>
                  <li>Use the materials for any commercial purpose or for any public display</li>
                  <li>Attempt to reverse engineer any software contained on the website</li>
                  <li>Remove any copyright or other proprietary notations from the materials</li>
                  <li>Share your account credentials with others</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  User Accounts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Account Creation</h3>
                  <p className="text-gray-600">
                    To use certain features of our Service, you must create an account. You agree to:
                  </p>
                  <ul className="list-disc list-inside mt-2 text-gray-600 space-y-1">
                    <li>Provide accurate and complete information</li>
                    <li>Maintain the security of your account</li>
                    <li>Notify us immediately of any unauthorized use</li>
                    <li>Accept responsibility for all activities under your account</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Account Termination</h3>
                  <p className="text-gray-600">
                    We reserve the right to terminate accounts that violate these terms or engage in 
                    prohibited activities.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment Terms
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Subscription Fees</h3>
                  <p className="text-gray-600">
                    Paid subscriptions are billed in advance on a monthly or annual basis. 
                    All fees are non-refundable except as required by law.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Price Changes</h3>
                  <p className="text-gray-600">
                    We reserve the right to modify subscription fees with 30 days advance notice. 
                    Continued use after price changes constitutes acceptance of new pricing.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Cancellation</h3>
                  <p className="text-gray-600">
                    You may cancel your subscription at any time. Cancellation will take effect at 
                    the end of your current billing period.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Content and Intellectual Property</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Your Content</h3>
                  <p className="text-gray-600">
                    You retain ownership of content you upload to our Service. By uploading content, 
                    you grant us a license to use, store, and process your content to provide the Service.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Our Content</h3>
                  <p className="text-gray-600">
                    The Service and its original content, features, and functionality are owned by 
                    GlossaFlow and are protected by international copyright, trademark, and other laws.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="mr-2 h-5 w-5" />
                  Prohibited Uses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  You may not use our Service:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>For any unlawful purpose or to solicit others to unlawful acts</li>
                  <li>To violate any international, federal, provincial, or state regulations or laws</li>
                  <li>To infringe upon or violate our intellectual property rights or the rights of others</li>
                  <li>To harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate</li>
                  <li>To submit false or misleading information</li>
                  <li>To upload or transmit viruses or any other type of malicious code</li>
                  <li>To spam, phish, pharm, pretext, spider, crawl, or scrape</li>
                  <li>For any obscene or immoral purpose</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Availability</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  We strive to maintain high service availability but do not guarantee uninterrupted access. 
                  We may temporarily suspend the Service for maintenance, updates, or other operational reasons. 
                  We are not liable for any downtime or service interruptions.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Disclaimer</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  The information on this website is provided on an "as is" basis. To the fullest extent 
                  permitted by law, this Company excludes all representations, warranties, conditions and 
                  terms whether express or implied, statutory or otherwise.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Limitations</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  In no event shall GlossaFlow or its suppliers be liable for any damages (including, 
                  without limitation, damages for loss of data or profit, or due to business interruption) 
                  arising out of the use or inability to use the Service, even if GlossaFlow or its 
                  authorized representative has been notified orally or in writing of the possibility of such damage.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Accuracy of Materials</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  The materials appearing on GlossaFlow could include technical, typographical, or 
                  photographic errors. GlossaFlow does not warrant that any of the materials on its 
                  website are accurate, complete, or current.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Links</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  GlossaFlow has not reviewed all of the sites linked to our website and is not 
                  responsible for the contents of any such linked site. The inclusion of any link 
                  does not imply endorsement by GlossaFlow of the site.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Modifications</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  GlossaFlow may revise these terms of service at any time without notice. By using 
                  this website, you are agreeing to be bound by the then current version of these terms.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Governing Law</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  These terms and conditions are governed by and construed in accordance with the laws 
                  of California, United States, and you irrevocably submit to the exclusive jurisdiction 
                  of the courts in that state or location.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  If you have any questions about these Terms of Service, please contact us:
                </p>
                <div className="space-y-2 text-gray-600">
                  <p>Email: <EMAIL></p>
                  <p>Address: 123 Translation Street, San Francisco, CA 94105</p>
                  <p>Phone: +****************</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-900 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <p className="text-gray-400">
                © 2024 GlossaFlow. All rights reserved.
              </p>
              <div className="flex items-center space-x-6 text-gray-400">
                <a href="/privacy" className="hover:text-white">Privacy</a>
                <a href="/terms" className="hover:text-white">Terms</a>
                <a href="/contact" className="hover:text-white">Contact</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
