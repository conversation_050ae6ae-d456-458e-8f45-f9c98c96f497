'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Elements } from '@stripe/react-stripe-js';
import { getStripe } from '@/lib/stripe/config';
import { CheckoutForm } from '@/components/billing/CheckoutForm';
import { SEOHead } from '@/components/seo/SEOHead';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [stripePromise] = useState(() => getStripe());
  
  const planId = searchParams.get('plan');
  const interval = searchParams.get('interval') as 'month' | 'year';

  useEffect(() => {
    if (!planId || !interval) {
      router.push('/pricing');
    }
  }, [planId, interval, router]);

  if (!planId || !interval) {
    return null;
  }

  const handleSuccess = () => {
    router.push('/dashboard/billing/success');
  };

  const handleError = (error: string) => {
    console.error('Checkout error:', error);
    // Could show error toast or redirect to error page
  };

  return (
    <>
      <SEOHead
        title="Checkout"
        description="Complete your GlossaFlow subscription"
        noIndex={true}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => router.back()}
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <h1 className="text-2xl font-bold text-blue-600">GlossaFlow</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Complete Your Subscription
            </h1>
            <p className="text-lg text-gray-600">
              You're just one step away from accessing all GlossaFlow features
            </p>
          </div>

          <Elements stripe={stripePromise}>
            <CheckoutForm
              planId={planId}
              interval={interval}
              onSuccess={handleSuccess}
              onError={handleError}
            />
          </Elements>

          {/* Security Notice */}
          <div className="mt-8 text-center">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                  <span>🔒</span>
                  <span>
                    Your payment information is secure and encrypted. 
                    We use Stripe for payment processing and never store your card details.
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
