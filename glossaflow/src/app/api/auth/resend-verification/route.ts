import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';
import * as z from 'zod';

const resendVerificationSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = resendVerificationSchema.parse(body);
    
    const supabase = createClient();
    
    // Resend verification email
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: validatedData.email,
    });

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'Verification email sent successfully',
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error('Resend verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
