import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';
import * as z from 'zod';

const verifyEmailSchema = z.object({
  token: z.string().min(1, 'Verification token is required'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = verifyEmailSchema.parse(body);
    
    const supabase = createClient();
    
    // Verify email with token
    const { error } = await supabase.auth.verifyOtp({
      token_hash: validatedData.token,
      type: 'email',
    });

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'Email verified successfully',
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
