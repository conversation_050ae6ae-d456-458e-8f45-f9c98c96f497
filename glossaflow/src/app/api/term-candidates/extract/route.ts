import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      projectId,
      sourceText,
      sourceLanguage,
      clearExisting = false,
    } = body;

    // Validate required fields
    if (!projectId || !sourceText) {
      return NextResponse.json(
        { error: 'Missing required fields: projectId, sourceText', success: false },
        { status: 400 }
      );
    }

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name, organization_id, source_language')
      .eq('id', projectId)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Clear existing candidates if requested
    if (clearExisting) {
      const { error: deleteError } = await supabase
        .from('term_candidates')
        .delete()
        .eq('project_id', projectId)
        .eq('status', 'pending');

      if (deleteError) {
        console.error('Error clearing existing candidates:', deleteError);
        // Continue anyway, don't fail the entire operation
      }
    }

    // Call the Supabase Edge Function for term extraction
    const extractionPayload = {
      project_id: projectId,
      source_text: sourceText,
      source_language: sourceLanguage || project.source_language || 'en',
      user_id: session.user.id,
      organization_id: session.user.organizationId || project.organization_id,
    };

    const { data: extractionResult, error: extractionError } = await supabase.functions
      .invoke('extract-term-candidates', {
        body: extractionPayload,
      });

    if (extractionError) {
      console.error('Extraction function error:', extractionError);
      return NextResponse.json(
        { 
          error: 'Failed to extract term candidates', 
          success: false,
          details: extractionError.message 
        },
        { status: 500 }
      );
    }

    // Get the updated candidates count
    const { count: totalCandidates } = await supabase
      .from('term_candidates')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId);

    const { count: pendingCandidates } = await supabase
      .from('term_candidates')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('status', 'pending');

    return NextResponse.json({
      success: true,
      data: {
        projectId,
        projectName: project.name,
        extractionResult,
        statistics: {
          totalCandidates: totalCandidates || 0,
          pendingCandidates: pendingCandidates || 0,
          newCandidates: extractionResult?.candidates_saved || 0,
        },
      },
      message: `Successfully extracted ${extractionResult?.candidates_extracted || 0} term candidates`,
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

// GET endpoint to check extraction status or get extraction history
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing required parameter: projectId', success: false },
        { status: 400 }
      );
    }

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name')
      .eq('id', projectId)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Get extraction statistics
    const { count: totalCandidates } = await supabase
      .from('term_candidates')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId);

    const { count: pendingCandidates } = await supabase
      .from('term_candidates')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('status', 'pending');

    const { count: approvedCandidates } = await supabase
      .from('term_candidates')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('status', 'approved');

    const { count: rejectedCandidates } = await supabase
      .from('term_candidates')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('status', 'rejected');

    // Get latest extraction info
    const { data: latestCandidate } = await supabase
      .from('term_candidates')
      .select('created_at, created_by')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    return NextResponse.json({
      success: true,
      data: {
        projectId,
        projectName: project.name,
        statistics: {
          totalCandidates: totalCandidates || 0,
          pendingCandidates: pendingCandidates || 0,
          approvedCandidates: approvedCandidates || 0,
          rejectedCandidates: rejectedCandidates || 0,
        },
        lastExtraction: latestCandidate ? {
          extractedAt: latestCandidate.created_at,
          extractedBy: latestCandidate.created_by,
        } : null,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
