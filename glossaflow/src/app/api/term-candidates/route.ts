import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const projectId = searchParams.get('projectId');
    const sortBy = searchParams.get('sortBy') || 'confidence_score';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Build the query
    let query = supabase
      .from('term_candidates')
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages
        ),
        created_by_user:users!term_candidates_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!term_candidates_reviewed_by_fkey(
          id,
          name,
          email
        )
      `, { count: 'exact' });

    // Apply filters
    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`candidate_term.ilike.%${search}%,context_sentence.ilike.%${search}%`);
    }

    // Apply sorting
    const validSortFields = ['confidence_score', 'frequency', 'created_at', 'candidate_term'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'confidence_score';
    const order = sortOrder === 'asc' ? 'asc' : 'desc';
    
    query = query.order(sortField, { ascending: order === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: candidates, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch term candidates', success: false },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format
    const transformedCandidates = candidates?.map(candidate => ({
      id: candidate.id,
      projectId: candidate.project_id,
      candidateTerm: candidate.candidate_term,
      frequency: candidate.frequency,
      confidenceScore: candidate.confidence_score,
      contextSentence: candidate.context_sentence,
      status: candidate.status,
      assignedCategory: candidate.assigned_category,
      extractionMetadata: candidate.extraction_metadata,
      createdBy: candidate.created_by,
      reviewedBy: candidate.reviewed_by,
      createdAt: candidate.created_at,
      updatedAt: candidate.updated_at,
      organizationId: candidate.organization_id,
      project: candidate.project,
      createdByUser: candidate.created_by_user,
      reviewedByUser: candidate.reviewed_by_user,
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: transformedCandidates,
        total: count || 0,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      projectId,
      candidateTerm,
      frequency,
      confidenceScore,
      contextSentence,
      assignedCategory,
      extractionMetadata,
    } = body;

    // Validate required fields
    if (!projectId || !candidateTerm) {
      return NextResponse.json(
        { error: 'Missing required fields: projectId, candidateTerm', success: false },
        { status: 400 }
      );
    }

    // Verify project access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, organization_id')
      .eq('id', projectId)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Create the term candidate
    const { data: candidate, error: candidateError } = await supabase
      .from('term_candidates')
      .insert({
        project_id: projectId,
        candidate_term: candidateTerm,
        frequency: frequency || 1,
        confidence_score: confidenceScore || 0.5,
        context_sentence: contextSentence,
        assigned_category: assignedCategory,
        extraction_metadata: extractionMetadata || {},
        created_by: session.user.id,
        organization_id: session.user.organizationId || project.organization_id,
        status: 'pending',
      })
      .select()
      .single();

    if (candidateError) {
      console.error('Term candidate creation error:', candidateError);
      return NextResponse.json(
        { error: 'Failed to create term candidate', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: candidate.id,
        projectId: candidate.project_id,
        candidateTerm: candidate.candidate_term,
        frequency: candidate.frequency,
        confidenceScore: candidate.confidence_score,
        contextSentence: candidate.context_sentence,
        status: candidate.status,
        assignedCategory: candidate.assigned_category,
        extractionMetadata: candidate.extraction_metadata,
        createdBy: candidate.created_by,
        reviewedBy: candidate.reviewed_by,
        createdAt: candidate.created_at,
        updatedAt: candidate.updated_at,
        organizationId: candidate.organization_id,
      },
      message: 'Term candidate created successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
