import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    // Build base query
    let query = supabase
      .from('term_candidates')
      .select('status, assigned_category, confidence_score, frequency, created_at, project_id');

    // Filter by project if specified
    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data: candidates, error } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch term candidate stats', success: false },
        { status: 500 }
      );
    }

    // Calculate basic statistics
    const total = candidates?.length || 0;
    const pending = candidates?.filter(c => c.status === 'pending').length || 0;
    const approved = candidates?.filter(c => c.status === 'approved').length || 0;
    const rejected = candidates?.filter(c => c.status === 'rejected').length || 0;

    // Calculate by category
    const byCategory: Record<string, number> = {};
    candidates?.forEach(candidate => {
      const category = candidate.assigned_category || 'unassigned';
      byCategory[category] = (byCategory[category] || 0) + 1;
    });

    // Calculate confidence score distribution
    const confidenceDistribution = {
      high: candidates?.filter(c => c.confidence_score >= 0.8).length || 0,
      medium: candidates?.filter(c => c.confidence_score >= 0.5 && c.confidence_score < 0.8).length || 0,
      low: candidates?.filter(c => c.confidence_score < 0.5).length || 0,
    };

    // Calculate frequency distribution
    const frequencyDistribution = {
      high: candidates?.filter(c => c.frequency >= 10).length || 0,
      medium: candidates?.filter(c => c.frequency >= 3 && c.frequency < 10).length || 0,
      low: candidates?.filter(c => c.frequency < 3).length || 0,
    };

    // Calculate by project (if not filtering by specific project)
    const byProject: Record<string, number> = {};
    if (!projectId) {
      candidates?.forEach(candidate => {
        const project = candidate.project_id || 'unknown';
        byProject[project] = (byProject[project] || 0) + 1;
      });
    }

    // Calculate recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentCandidates = candidates?.filter(c => 
      new Date(c.created_at) >= sevenDaysAgo
    ).length || 0;

    // Calculate approval rate
    const reviewedTotal = approved + rejected;
    const approvalRate = reviewedTotal > 0 ? (approved / reviewedTotal) * 100 : 0;

    // Calculate average confidence score
    const avgConfidenceScore = candidates?.length > 0 
      ? candidates.reduce((sum, c) => sum + c.confidence_score, 0) / candidates.length 
      : 0;

    // Calculate average frequency
    const avgFrequency = candidates?.length > 0 
      ? candidates.reduce((sum, c) => sum + c.frequency, 0) / candidates.length 
      : 0;

    // Get top candidates by confidence
    const topCandidatesByConfidence = candidates
      ?.sort((a, b) => b.confidence_score - a.confidence_score)
      .slice(0, 5)
      .map(c => ({
        projectId: c.project_id,
        confidenceScore: c.confidence_score,
        frequency: c.frequency,
        status: c.status,
      })) || [];

    // Get top candidates by frequency
    const topCandidatesByFrequency = candidates
      ?.sort((a, b) => b.frequency - a.frequency)
      .slice(0, 5)
      .map(c => ({
        projectId: c.project_id,
        confidenceScore: c.confidence_score,
        frequency: c.frequency,
        status: c.status,
      })) || [];

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          total,
          pending,
          approved,
          rejected,
          recentCandidates,
          approvalRate: Math.round(approvalRate * 100) / 100,
          avgConfidenceScore: Math.round(avgConfidenceScore * 100) / 100,
          avgFrequency: Math.round(avgFrequency * 100) / 100,
        },
        distributions: {
          byCategory,
          byProject: projectId ? {} : byProject,
          confidence: confidenceDistribution,
          frequency: frequencyDistribution,
        },
        topCandidates: {
          byConfidence: topCandidatesByConfidence,
          byFrequency: topCandidatesByFrequency,
        },
        filters: {
          projectId,
        },
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
