import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { data: candidate, error } = await supabase
      .from('term_candidates')
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages
        ),
        created_by_user:users!term_candidates_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!term_candidates_reviewed_by_fkey(
          id,
          name,
          email
        )
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Term candidate not found', success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: candidate.id,
        projectId: candidate.project_id,
        candidateTerm: candidate.candidate_term,
        frequency: candidate.frequency,
        confidenceScore: candidate.confidence_score,
        contextSentence: candidate.context_sentence,
        status: candidate.status,
        assignedCategory: candidate.assigned_category,
        extractionMetadata: candidate.extraction_metadata,
        createdBy: candidate.created_by,
        reviewedBy: candidate.reviewed_by,
        createdAt: candidate.created_at,
        updatedAt: candidate.updated_at,
        organizationId: candidate.organization_id,
        project: candidate.project,
        createdByUser: candidate.created_by_user,
        reviewedByUser: candidate.reviewed_by_user,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      status,
      assignedCategory,
      reviewedBy,
    } = body;

    // Get the current candidate to verify access
    const { data: currentCandidate, error: fetchError } = await supabase
      .from('term_candidates')
      .select('id, project_id')
      .eq('id', params.id)
      .single();

    if (fetchError || !currentCandidate) {
      return NextResponse.json(
        { error: 'Term candidate not found', success: false },
        { status: 404 }
      );
    }

    // Update the term candidate
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (status !== undefined) {
      updateData.status = status;
      if (status === 'approved' || status === 'rejected') {
        updateData.reviewed_by = session.user.id;
      }
    }

    if (assignedCategory !== undefined) {
      updateData.assigned_category = assignedCategory;
    }

    if (reviewedBy !== undefined) {
      updateData.reviewed_by = reviewedBy;
    }

    const { data: candidate, error } = await supabase
      .from('term_candidates')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages
        ),
        created_by_user:users!term_candidates_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!term_candidates_reviewed_by_fkey(
          id,
          name,
          email
        )
      `)
      .single();

    if (error) {
      console.error('Update error:', error);
      return NextResponse.json(
        { error: 'Failed to update term candidate', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: candidate.id,
        projectId: candidate.project_id,
        candidateTerm: candidate.candidate_term,
        frequency: candidate.frequency,
        confidenceScore: candidate.confidence_score,
        contextSentence: candidate.context_sentence,
        status: candidate.status,
        assignedCategory: candidate.assigned_category,
        extractionMetadata: candidate.extraction_metadata,
        createdBy: candidate.created_by,
        reviewedBy: candidate.reviewed_by,
        createdAt: candidate.created_at,
        updatedAt: candidate.updated_at,
        organizationId: candidate.organization_id,
        project: candidate.project,
        createdByUser: candidate.created_by_user,
        reviewedByUser: candidate.reviewed_by_user,
      },
      message: 'Term candidate updated successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Verify the candidate exists and user has access
    const { data: candidate, error: fetchError } = await supabase
      .from('term_candidates')
      .select('id, project_id')
      .eq('id', params.id)
      .single();

    if (fetchError || !candidate) {
      return NextResponse.json(
        { error: 'Term candidate not found', success: false },
        { status: 404 }
      );
    }

    // Delete the term candidate
    const { error } = await supabase
      .from('term_candidates')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Delete error:', error);
      return NextResponse.json(
        { error: 'Failed to delete term candidate', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Term candidate deleted successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
