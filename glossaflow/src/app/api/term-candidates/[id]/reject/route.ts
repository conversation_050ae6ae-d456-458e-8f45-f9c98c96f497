import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { reason } = body;

    // Get the term candidate to verify it exists and user has access
    const { data: candidate, error: candidateError } = await supabase
      .from('term_candidates')
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages
        )
      `)
      .eq('id', params.id)
      .single();

    if (candidateError || !candidate) {
      return NextResponse.json(
        { error: 'Term candidate not found', success: false },
        { status: 404 }
      );
    }

    // Update the candidate status to rejected
    const updateData: any = {
      status: 'rejected',
      reviewed_by: session.user.id,
      updated_at: new Date().toISOString(),
    };

    // Add rejection reason to metadata if provided
    if (reason) {
      const currentMetadata = candidate.extraction_metadata || {};
      updateData.extraction_metadata = {
        ...currentMetadata,
        rejection_reason: reason,
        rejected_at: new Date().toISOString(),
      };
    }

    const { data: updatedCandidate, error: updateError } = await supabase
      .from('term_candidates')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages
        ),
        reviewed_by_user:users!term_candidates_reviewed_by_fkey(
          id,
          name,
          email
        )
      `)
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to reject term candidate', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedCandidate.id,
        projectId: updatedCandidate.project_id,
        candidateTerm: updatedCandidate.candidate_term,
        frequency: updatedCandidate.frequency,
        confidenceScore: updatedCandidate.confidence_score,
        contextSentence: updatedCandidate.context_sentence,
        status: updatedCandidate.status,
        assignedCategory: updatedCandidate.assigned_category,
        extractionMetadata: updatedCandidate.extraction_metadata,
        createdBy: updatedCandidate.created_by,
        reviewedBy: updatedCandidate.reviewed_by,
        createdAt: updatedCandidate.created_at,
        updatedAt: updatedCandidate.updated_at,
        organizationId: updatedCandidate.organization_id,
        project: updatedCandidate.project,
        reviewedByUser: updatedCandidate.reviewed_by_user,
      },
      message: 'Term candidate rejected successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
