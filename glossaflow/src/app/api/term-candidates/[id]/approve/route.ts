import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      targetTerm,
      targetLanguage,
      category,
      context,
      usageNotes,
      addToGlossary = false,
    } = body;

    // Get the term candidate
    const { data: candidate, error: candidateError } = await supabase
      .from('term_candidates')
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages,
          organization_id
        )
      `)
      .eq('id', params.id)
      .single();

    if (candidateError || !candidate) {
      return NextResponse.json(
        { error: 'Term candidate not found', success: false },
        { status: 404 }
      );
    }

    // Validate required fields for approval
    if (!targetTerm || !targetLanguage || !category) {
      return NextResponse.json(
        { error: 'Missing required fields for approval: targetTerm, targetLanguage, category', success: false },
        { status: 400 }
      );
    }

    // Start a transaction-like operation
    try {
      // 1. Update the candidate status to approved
      const { error: updateError } = await supabase
        .from('term_candidates')
        .update({
          status: 'approved',
          reviewed_by: session.user.id,
          assigned_category: category,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.id);

      if (updateError) {
        throw new Error(`Failed to update candidate: ${updateError.message}`);
      }

      // 2. Create a terminology entry
      const { data: terminologyEntry, error: terminologyError } = await supabase
        .from('terminology_entries')
        .insert({
          source_term: candidate.candidate_term,
          target_term: targetTerm,
          target_language: targetLanguage,
          category,
          context: context || candidate.context_sentence,
          usage_notes: usageNotes,
          project_id: candidate.project_id,
          approval_status: 'approved',
          frequency: candidate.frequency,
          created_by: session.user.id,
          organization_id: candidate.organization_id,
        })
        .select()
        .single();

      if (terminologyError) {
        // If terminology creation fails, revert the candidate status
        await supabase
          .from('term_candidates')
          .update({
            status: 'pending',
            reviewed_by: null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', params.id);

        throw new Error(`Failed to create terminology entry: ${terminologyError.message}`);
      }

      // 3. Optionally add to glossary if requested
      let glossaryEntry = null;
      if (addToGlossary) {
        // Find or create a default glossary for the project
        const { data: existingGlossary } = await supabase
          .from('glossaries')
          .select('id')
          .eq('name', `${candidate.project.name} Glossary`)
          .eq('organization_id', candidate.organization_id)
          .single();

        let glossaryId = existingGlossary?.id;

        if (!glossaryId) {
          // Create a new glossary
          const { data: newGlossary, error: glossaryError } = await supabase
            .from('glossaries')
            .insert({
              name: `${candidate.project.name} Glossary`,
              description: `Auto-generated glossary for ${candidate.project.name}`,
              source_language: candidate.project.source_language,
              target_languages: candidate.project.target_languages,
              created_by: session.user.id,
              organization_id: candidate.organization_id,
            })
            .select('id')
            .single();

          if (glossaryError) {
            console.error('Failed to create glossary:', glossaryError);
          } else {
            glossaryId = newGlossary.id;
          }
        }

        if (glossaryId) {
          const { data: glossaryEntryData, error: glossaryEntryError } = await supabase
            .from('glossary_entries')
            .insert({
              glossary_id: glossaryId,
              terminology_id: terminologyEntry.id,
              added_by: session.user.id,
            })
            .select()
            .single();

          if (glossaryEntryError) {
            console.error('Failed to add to glossary:', glossaryEntryError);
          } else {
            glossaryEntry = glossaryEntryData;
          }
        }
      }

      // Get the updated candidate with relations
      const { data: updatedCandidate } = await supabase
        .from('term_candidates')
        .select(`
          *,
          project:projects(
            id,
            name,
            source_language,
            target_languages
          ),
          reviewed_by_user:users!term_candidates_reviewed_by_fkey(
            id,
            name,
            email
          )
        `)
        .eq('id', params.id)
        .single();

      return NextResponse.json({
        success: true,
        data: {
          candidate: {
            id: updatedCandidate.id,
            projectId: updatedCandidate.project_id,
            candidateTerm: updatedCandidate.candidate_term,
            frequency: updatedCandidate.frequency,
            confidenceScore: updatedCandidate.confidence_score,
            contextSentence: updatedCandidate.context_sentence,
            status: updatedCandidate.status,
            assignedCategory: updatedCandidate.assigned_category,
            extractionMetadata: updatedCandidate.extraction_metadata,
            createdBy: updatedCandidate.created_by,
            reviewedBy: updatedCandidate.reviewed_by,
            createdAt: updatedCandidate.created_at,
            updatedAt: updatedCandidate.updated_at,
            organizationId: updatedCandidate.organization_id,
            project: updatedCandidate.project,
            reviewedByUser: updatedCandidate.reviewed_by_user,
          },
          terminologyEntry: {
            id: terminologyEntry.id,
            sourceTerm: terminologyEntry.source_term,
            targetTerm: terminologyEntry.target_term,
            targetLanguage: terminologyEntry.target_language,
            category: terminologyEntry.category,
            context: terminologyEntry.context,
            usageNotes: terminologyEntry.usage_notes,
            approvalStatus: terminologyEntry.approval_status,
            frequency: terminologyEntry.frequency,
            createdBy: terminologyEntry.created_by,
            createdAt: terminologyEntry.created_at,
            projectId: terminologyEntry.project_id,
            organizationId: terminologyEntry.organization_id,
          },
          glossaryEntry,
        },
        message: 'Term candidate approved and terminology entry created successfully',
      });

    } catch (transactionError) {
      console.error('Transaction error:', transactionError);
      return NextResponse.json(
        { 
          error: 'Failed to approve term candidate', 
          success: false,
          details: transactionError.message 
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
