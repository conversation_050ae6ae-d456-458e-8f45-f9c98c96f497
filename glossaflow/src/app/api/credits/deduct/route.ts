import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

// POST /api/credits/deduct - Deduct credits for AI translation
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      amount,
      projectId,
      chapterId,
      segmentId,
      description,
      metadata = {},
    } = body;

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required', success: false },
        { status: 400 }
      );
    }

    if (!description) {
      return NextResponse.json(
        { error: 'Description is required', success: false },
        { status: 400 }
      );
    }

    const organizationId = session.user.organizationId;
    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID required', success: false },
        { status: 400 }
      );
    }

    // Check current credit balance
    const { data: credits, error: creditsError } = await supabase
      .from('credits')
      .select('balance')
      .eq('organization_id', organizationId)
      .single();

    if (creditsError) {
      console.error('Credits fetch error:', creditsError);
      return NextResponse.json(
        { error: 'Failed to fetch credit balance', success: false },
        { status: 500 }
      );
    }

    const currentBalance = credits?.balance || 0;
    
    // Check if sufficient credits available
    if (currentBalance < amount) {
      return NextResponse.json(
        { 
          error: 'Insufficient credits', 
          success: false,
          data: {
            currentBalance,
            requiredAmount: amount,
            shortfall: amount - currentBalance,
          }
        },
        { status: 402 } // Payment Required
      );
    }

    // Validate project/chapter/segment access if provided
    if (projectId) {
      const { data: projectAccess, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          project_members!inner(user_id)
        `)
        .eq('id', projectId)
        .eq('project_members.user_id', session.user.id)
        .single();

      if (projectError || !projectAccess) {
        return NextResponse.json(
          { error: 'Project not found or access denied', success: false },
          { status: 404 }
        );
      }
    }

    if (chapterId) {
      // Get chapter first
      const { data: chapter, error: chapterError } = await supabase
        .from('chapters')
        .select(`
          id,
          project:projects(
            id,
            created_by,
            project_manager_id
          )
        `)
        .eq('id', chapterId)
        .single();

      if (chapterError || !chapter) {
        return NextResponse.json(
          { error: 'Chapter not found', success: false },
          { status: 404 }
        );
      }

      // Check if user has access to this project
      const projectId = chapter.project.id;
      const userId = session.user.id;

      // Check if user is a project member
      const { data: projectAccess } = await supabase
        .from('project_members')
        .select('project_id')
        .eq('project_id', projectId)
        .eq('user_id', userId)
        .single();

      // Also check if user is creator or project manager
      const { data: projectOwnership } = await supabase
        .from('projects')
        .select('id, created_by, project_manager_id')
        .eq('id', projectId)
        .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`)
        .single();

      if (!projectAccess && !projectOwnership) {
        return NextResponse.json(
          { error: 'Access denied', success: false },
          { status: 403 }
        );
      }
    }

    // Create credit transaction (negative amount for deduction)
    const { data: transaction, error: transactionError } = await supabase
      .from('credit_transactions')
      .insert({
        organization_id: organizationId,
        user_id: session.user.id,
        project_id: projectId || null,
        chapter_id: chapterId || null,
        segment_id: segmentId || null,
        transaction_type: 'ai_translation',
        amount: -amount, // Negative for deduction
        description,
        metadata,
      })
      .select()
      .single();

    if (transactionError) {
      console.error('Transaction creation error:', transactionError);
      return NextResponse.json(
        { error: 'Failed to create transaction', success: false },
        { status: 500 }
      );
    }

    // Get updated balance (the trigger should have updated it)
    const { data: updatedCredits, error: balanceError } = await supabase
      .from('credits')
      .select('balance')
      .eq('organization_id', organizationId)
      .single();

    if (balanceError) {
      console.error('Balance fetch error:', balanceError);
      return NextResponse.json(
        { error: 'Failed to fetch updated balance', success: false },
        { status: 500 }
      );
    }

    // Update chapter actual cost if chapter is provided
    if (chapterId) {
      await supabase
        .from('chapters')
        .update({
          actual_cost: supabase.raw(`actual_cost + ${amount}`),
          updated_at: new Date().toISOString(),
        })
        .eq('id', chapterId);
    }

    return NextResponse.json({
      success: true,
      data: {
        transactionId: transaction.id,
        creditsDeducted: amount,
        remainingBalance: updatedCredits.balance,
      },
      message: 'Credits deducted successfully',
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
