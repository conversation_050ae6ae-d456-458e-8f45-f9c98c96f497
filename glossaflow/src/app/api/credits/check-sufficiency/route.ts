import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

// GET /api/credits/check-sufficiency - Check if user has sufficient credits
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const requiredAmount = parseFloat(searchParams.get('requiredAmount') || '0');
    const organizationId = searchParams.get('organizationId') || session.user.organizationId;

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID required', success: false },
        { status: 400 }
      );
    }

    if (requiredAmount <= 0) {
      return NextResponse.json(
        { error: 'Valid required amount is needed', success: false },
        { status: 400 }
      );
    }

    // Get current credit balance
    const { data: credits, error: creditsError } = await supabase
      .from('credits')
      .select('balance')
      .eq('organization_id', organizationId)
      .single();

    if (creditsError && creditsError.code !== 'PGRST116') { // Not found is OK
      console.error('Credits fetch error:', creditsError);
      return NextResponse.json(
        { error: 'Failed to fetch credit balance', success: false },
        { status: 500 }
      );
    }

    const currentBalance = credits?.balance || 0;
    const hasSufficientCredits = currentBalance >= requiredAmount;
    const shortfall = Math.max(0, requiredAmount - currentBalance);

    return NextResponse.json({
      success: true,
      data: {
        hasSufficientCredits,
        currentBalance,
        requiredAmount,
        shortfall,
      },
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
