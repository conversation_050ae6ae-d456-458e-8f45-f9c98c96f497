import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import type { UpdateChapterRequest } from '@/types';

// GET /api/chapters/[id] - Get a single chapter with segments
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Await params as required by Next.js 15
    const { id: chapterId } = await params;

    // Get chapter first
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select(`
        *,
        project:projects(
          id,
          name,
          source_language,
          target_languages,
          created_by,
          project_manager_id
        )
      `)
      .eq('id', chapterId)
      .single();

    if (chapterError || !chapter) {
      return NextResponse.json(
        { error: 'Chapter not found', success: false },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    const projectId = chapter.project.id;
    const userId = session.user.id;

    // Check if user is a project member
    const { data: projectAccess } = await supabase
      .from('project_members')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .single();

    // Also check if user is creator or project manager
    const { data: projectOwnership } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`)
      .single();

    if (!projectAccess && !projectOwnership) {
      return NextResponse.json(
        { error: 'Access denied', success: false },
        { status: 403 }
      );
    }

    // Get chapter segments
    const { data: segments, error: segmentsError } = await supabase
      .from('translation_segments')
      .select(`
        *,
        assigned_translator:users!assigned_translator_id(id, name, email),
        assigned_reviewer:users!assigned_reviewer_id(id, name, email)
      `)
      .eq('chapter_id', chapterId)
      .order('segment_number');

    if (segmentsError) {
      console.error('Segments fetch error:', segmentsError);
      return NextResponse.json(
        { error: 'Failed to fetch chapter segments', success: false },
        { status: 500 }
      );
    }

    // Calculate progress
    const totalSegments = segments?.length || 0;
    const completedSegments = segments?.filter(s => 
      ['translated', 'reviewed', 'approved'].includes(s.status)
    ).length || 0;
    const reviewedSegments = segments?.filter(s => 
      ['reviewed', 'approved'].includes(s.status)
    ).length || 0;
    const approvedSegments = segments?.filter(s => s.status === 'approved').length || 0;

    const progress = {
      totalSegments,
      completedSegments,
      reviewedSegments,
      approvedSegments,
      progressPercentage: totalSegments > 0 ? (completedSegments / totalSegments) * 100 : 0,
    };

    // Calculate credit requirement
    const estimatedCost = chapter.estimated_cost || 0;
    const actualCost = chapter.actual_cost || 0;
    const creditRequirement = {
      estimated: estimatedCost,
      actual: actualCost,
      remaining: Math.max(0, estimatedCost - actualCost),
    };

    return NextResponse.json({
      success: true,
      data: {
        chapter,
        segments: segments || [],
        progress,
        creditRequirement,
      },
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

// PATCH /api/chapters/[id] - Update a chapter
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const chapterId = params.id;
    const body: UpdateChapterRequest = await request.json();

    // Get chapter first
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select(`
        id,
        project:projects(
          id,
          created_by,
          project_manager_id
        )
      `)
      .eq('id', chapterId)
      .single();

    if (chapterError || !chapter) {
      return NextResponse.json(
        { error: 'Chapter not found', success: false },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    const projectId = chapter.project.id;
    const userId = session.user.id;

    // Check if user is a project member
    const { data: projectAccess } = await supabase
      .from('project_members')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .single();

    // Also check if user is creator or project manager
    const { data: projectOwnership } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`)
      .single();

    if (!projectAccess && !projectOwnership) {
      return NextResponse.json(
        { error: 'Access denied', success: false },
        { status: 404 }
      );
    }

    // Update the chapter
    const { data: updatedChapter, error: updateError } = await supabase
      .from('chapters')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('id', chapterId)
      .select()
      .single();

    if (updateError) {
      console.error('Chapter update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update chapter', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedChapter,
      message: 'Chapter updated successfully',
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

// DELETE /api/chapters/[id] - Delete a chapter
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Await params as required by Next.js 15
    const { id: chapterId } = await params;

    // Get chapter first
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select(`
        id,
        project:projects(
          id,
          created_by,
          project_manager_id
        )
      `)
      .eq('id', chapterId)
      .single();

    if (chapterError || !chapter) {
      return NextResponse.json(
        { error: 'Chapter not found', success: false },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    const projectId = chapter.project.id;
    const userId = session.user.id;

    // Check if user is a project member
    const { data: projectAccess } = await supabase
      .from('project_members')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .single();

    // Also check if user is creator or project manager
    const { data: projectOwnership } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`)
      .single();

    if (!projectAccess && !projectOwnership) {
      return NextResponse.json(
        { error: 'Access denied', success: false },
        { status: 403 }
      );
    }

    // Check if user has permission to delete (project manager or admin)
    const userRole = chapterAccess.project.project_members[0]?.role;
    if (!['admin', 'project_manager'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete chapter', success: false },
        { status: 403 }
      );
    }

    // Check if chapter has segments
    const { data: segments, error: segmentsError } = await supabase
      .from('translation_segments')
      .select('id')
      .eq('chapter_id', chapterId)
      .limit(1);

    if (segmentsError) {
      console.error('Segments check error:', segmentsError);
      return NextResponse.json(
        { error: 'Failed to check chapter segments', success: false },
        { status: 500 }
      );
    }

    if (segments && segments.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete chapter with existing segments', success: false },
        { status: 409 }
      );
    }

    // Delete the chapter
    const { error: deleteError } = await supabase
      .from('chapters')
      .delete()
      .eq('id', chapterId);

    if (deleteError) {
      console.error('Chapter deletion error:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete chapter', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Chapter deleted successfully',
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
