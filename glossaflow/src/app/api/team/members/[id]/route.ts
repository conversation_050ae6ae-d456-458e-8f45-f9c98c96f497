import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { data: member, error } = await supabase
      .from('team_members')
      .select(`
        *,
        user:users!team_members_user_id_fkey(
          id,
          name,
          email,
          avatar_url
        ),
        stats:team_member_stats(
          projects_completed,
          words_translated,
          segments_translated,
          segments_reviewed,
          average_rating,
          current_projects,
          total_hours_worked
        )
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Team member not found', success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: member.id,
        userId: member.user_id,
        organizationId: member.organization_id,
        roleId: member.role_id,
        roleName: member.role_name,
        status: member.status,
        languages: member.languages || [],
        specializations: member.specializations || [],
        bio: member.bio,
        hourlyRate: member.hourly_rate,
        timezone: member.timezone,
        joinedAt: member.joined_at,
        lastActiveAt: member.last_active_at,
        createdAt: member.created_at,
        updatedAt: member.updated_at,
        createdBy: member.created_by,
        user: member.user ? {
          id: member.user.id,
          name: member.user.name,
          email: member.user.email,
          avatar: member.user.avatar_url,
        } : undefined,
        stats: member.stats?.[0] ? {
          projectsCompleted: member.stats[0].projects_completed || 0,
          wordsTranslated: member.stats[0].words_translated || 0,
          segmentsTranslated: member.stats[0].segments_translated || 0,
          segmentsReviewed: member.stats[0].segments_reviewed || 0,
          averageRating: member.stats[0].average_rating || 0,
          currentProjects: member.stats[0].current_projects || 0,
          totalHoursWorked: member.stats[0].total_hours_worked || 0,
        } : undefined,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      roleId,
      status,
      languages,
      specializations,
      bio,
      hourlyRate,
      timezone,
    } = body;

    // Build update object
    const updateData: any = {};
    if (roleId !== undefined) {
      // Get role name
      const { data: role, error: roleError } = await supabase
        .from('team_roles')
        .select('name')
        .eq('id', roleId)
        .single();

      if (roleError || !role) {
        return NextResponse.json(
          { error: 'Invalid role ID', success: false },
          { status: 400 }
        );
      }

      updateData.role_id = roleId;
      updateData.role_name = role.name;
    }
    if (status !== undefined) updateData.status = status;
    if (languages !== undefined) updateData.languages = languages;
    if (specializations !== undefined) updateData.specializations = specializations;
    if (bio !== undefined) updateData.bio = bio;
    if (hourlyRate !== undefined) updateData.hourly_rate = hourlyRate;
    if (timezone !== undefined) updateData.timezone = timezone;

    const { data: member, error } = await supabase
      .from('team_members')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        user:users!team_members_user_id_fkey(
          id,
          name,
          email,
          avatar_url
        ),
        stats:team_member_stats(
          projects_completed,
          words_translated,
          segments_translated,
          segments_reviewed,
          average_rating,
          current_projects,
          total_hours_worked
        )
      `)
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to update team member', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: member.id,
        userId: member.user_id,
        organizationId: member.organization_id,
        roleId: member.role_id,
        roleName: member.role_name,
        status: member.status,
        languages: member.languages || [],
        specializations: member.specializations || [],
        bio: member.bio,
        hourlyRate: member.hourly_rate,
        timezone: member.timezone,
        joinedAt: member.joined_at,
        lastActiveAt: member.last_active_at,
        createdAt: member.created_at,
        updatedAt: member.updated_at,
        createdBy: member.created_by,
        user: member.user ? {
          id: member.user.id,
          name: member.user.name,
          email: member.user.email,
          avatar: member.user.avatar_url,
        } : undefined,
        stats: member.stats?.[0] ? {
          projectsCompleted: member.stats[0].projects_completed || 0,
          wordsTranslated: member.stats[0].words_translated || 0,
          segmentsTranslated: member.stats[0].segments_translated || 0,
          segmentsReviewed: member.stats[0].segments_reviewed || 0,
          averageRating: member.stats[0].average_rating || 0,
          currentProjects: member.stats[0].current_projects || 0,
          totalHoursWorked: member.stats[0].total_hours_worked || 0,
        } : undefined,
      },
      message: 'Team member updated successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { error } = await supabase
      .from('team_members')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to delete team member', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Team member removed successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
