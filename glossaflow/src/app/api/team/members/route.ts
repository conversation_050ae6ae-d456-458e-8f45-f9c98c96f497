import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const status = searchParams.get('status');
    const language = searchParams.get('language');
    const specialization = searchParams.get('specialization');

    const offset = (page - 1) * limit;

    // Build the query with joins to get user info and stats
    let query = supabase
      .from('team_members')
      .select(`
        *,
        stats:team_member_stats(
          projects_completed,
          words_translated,
          segments_translated,
          segments_reviewed,
          average_rating,
          current_projects,
          total_hours_worked
        )
      `);

    // Apply filters
    if (search) {
      // Search in user name and email through the joined table
      query = query.or(`users.name.ilike.%${search}%,users.email.ilike.%${search}%`);
    }
    if (role && role !== 'all') {
      query = query.eq('role_name', role);
    }
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }
    if (language && language !== 'all') {
      query = query.contains('languages', [language]);
    }
    if (specialization && specialization !== 'all') {
      query = query.contains('specializations', [specialization]);
    }

    // Get total count
    const { count } = await supabase
      .from('team_members')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: teamMembers, error } = await query
      .range(offset, offset + limit - 1)
      .order('joined_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch team members', success: false },
        { status: 500 }
      );
    }

    // Get user information for each team member
    const userIds = teamMembers?.map(member => member.user_id) || [];
    const { data: users } = await supabase.auth.admin.listUsers();

    // Create a map of user data
    const userMap = new Map();
    users?.users?.forEach(user => {
      userMap.set(user.id, {
        id: user.id,
        name: user.user_metadata?.name || user.user_metadata?.full_name,
        email: user.email,
        avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture,
      });
    });

    // Transform the data to match the expected format
    const transformedTeamMembers = teamMembers?.map(member => ({
      id: member.id,
      userId: member.user_id,
      organizationId: member.organization_id,
      roleId: member.role_id,
      roleName: member.role_name,
      status: member.status,
      languages: member.languages || [],
      specializations: member.specializations || [],
      bio: member.bio,
      hourlyRate: member.hourly_rate,
      timezone: member.timezone,
      joinedAt: member.joined_at,
      lastActiveAt: member.last_active_at,
      createdAt: member.created_at,
      updatedAt: member.updated_at,
      createdBy: member.created_by,
      user: userMap.get(member.user_id) || {
        id: member.user_id,
        name: 'Unknown User',
        email: '<EMAIL>',
        avatar: null,
      },
      stats: member.stats?.[0] ? {
        projectsCompleted: member.stats[0].projects_completed || 0,
        wordsTranslated: member.stats[0].words_translated || 0,
        segmentsTranslated: member.stats[0].segments_translated || 0,
        segmentsReviewed: member.stats[0].segments_reviewed || 0,
        averageRating: member.stats[0].average_rating || 0,
        currentProjects: member.stats[0].current_projects || 0,
        totalHoursWorked: member.stats[0].total_hours_worked || 0,
      } : {
        projectsCompleted: 0,
        wordsTranslated: 0,
        segmentsTranslated: 0,
        segmentsReviewed: 0,
        averageRating: 0,
        currentProjects: 0,
        totalHoursWorked: 0,
      },
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: transformedTeamMembers,
        total: count || 0,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      userId,
      roleId,
      languages,
      specializations,
      bio,
      hourlyRate,
      timezone,
    } = body;

    // Validate required fields
    if (!userId || !roleId) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and roleId', success: false },
        { status: 400 }
      );
    }

    // Get role name
    const { data: role, error: roleError } = await supabase
      .from('team_roles')
      .select('name')
      .eq('id', roleId)
      .single();

    if (roleError || !role) {
      return NextResponse.json(
        { error: 'Invalid role ID', success: false },
        { status: 400 }
      );
    }

    // Create the team member
    const { data: member, error: memberError } = await supabase
      .from('team_members')
      .insert({
        user_id: userId,
        organization_id: session.user.organizationId || null,
        role_id: roleId,
        role_name: role.name,
        status: 'active',
        languages: languages || [],
        specializations: specializations || [],
        bio,
        hourly_rate: hourlyRate,
        timezone: timezone || 'UTC',
        created_by: session.user.id,
      })
      .select(`
        *,
        user:users!team_members_user_id_fkey(
          id,
          name,
          email,
          avatar_url
        )
      `)
      .single();

    if (memberError) {
      console.error('Team member creation error:', memberError);
      return NextResponse.json(
        { error: 'Failed to create team member', success: false },
        { status: 500 }
      );
    }

    // Create initial stats record
    await supabase
      .from('team_member_stats')
      .insert({
        team_member_id: member.id,
      });

    return NextResponse.json({
      success: true,
      data: {
        id: member.id,
        userId: member.user_id,
        organizationId: member.organization_id,
        roleId: member.role_id,
        roleName: member.role_name,
        status: member.status,
        languages: member.languages || [],
        specializations: member.specializations || [],
        bio: member.bio,
        hourlyRate: member.hourly_rate,
        timezone: member.timezone,
        joinedAt: member.joined_at,
        lastActiveAt: member.last_active_at,
        createdAt: member.created_at,
        updatedAt: member.updated_at,
        createdBy: member.created_by,
        user: member.user ? {
          id: member.user.id,
          name: member.user.name,
          email: member.user.email,
          avatar: member.user.avatar_url,
        } : undefined,
      },
      message: 'Team member created successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
