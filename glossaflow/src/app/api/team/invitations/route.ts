import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    const offset = (page - 1) * limit;

    // Build the query
    let query = supabase
      .from('team_invitations')
      .select(`
        *,
        invited_by_user:users!team_invitations_invited_by_fkey(
          id,
          name,
          email
        )
      `);

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    // Get total count
    const { count } = await supabase
      .from('team_invitations')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: invitations, error } = await query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch team invitations', success: false },
        { status: 500 }
      );
    }

    // Get user information for invited_by users
    const userIds = invitations?.map(inv => inv.invited_by) || [];
    const { data: users } = await supabase.auth.admin.listUsers();
    
    // Create a map of user data
    const userMap = new Map();
    users?.users?.forEach(user => {
      userMap.set(user.id, {
        id: user.id,
        name: user.user_metadata?.name || user.user_metadata?.full_name,
        email: user.email,
      });
    });

    // Transform the data to match the expected format
    const transformedInvitations = invitations?.map(invitation => ({
      id: invitation.id,
      email: invitation.email,
      organizationId: invitation.organization_id,
      roleId: invitation.role_id,
      roleName: invitation.role_name,
      invitedBy: invitation.invited_by,
      status: invitation.status,
      token: invitation.token,
      expiresAt: invitation.expires_at,
      acceptedAt: invitation.accepted_at,
      createdAt: invitation.created_at,
      updatedAt: invitation.updated_at,
      invitedByUser: userMap.get(invitation.invited_by) || {
        id: invitation.invited_by,
        name: 'Unknown User',
        email: '<EMAIL>',
      },
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: transformedInvitations,
        total: count || 0,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { email, roleId, message } = body;

    // Validate required fields
    if (!email || !roleId) {
      return NextResponse.json(
        { error: 'Missing required fields: email and roleId', success: false },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format', success: false },
        { status: 400 }
      );
    }

    // Check if user is already a team member
    const { data: existingMember } = await supabase
      .from('team_members')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('organization_id', session.user.organizationId || null)
      .single();

    if (existingMember) {
      return NextResponse.json(
        { error: 'User is already a team member', success: false },
        { status: 400 }
      );
    }

    // Check if there's already a pending invitation
    const { data: existingInvitation } = await supabase
      .from('team_invitations')
      .select('id')
      .eq('email', email)
      .eq('organization_id', session.user.organizationId || null)
      .eq('status', 'pending')
      .single();

    if (existingInvitation) {
      return NextResponse.json(
        { error: 'Invitation already sent to this email', success: false },
        { status: 400 }
      );
    }

    // Get role name
    const { data: role, error: roleError } = await supabase
      .from('team_roles')
      .select('name')
      .eq('id', roleId)
      .single();

    if (roleError || !role) {
      return NextResponse.json(
        { error: 'Invalid role ID', success: false },
        { status: 400 }
      );
    }

    // Generate invitation token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

    // Create the invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('team_invitations')
      .insert({
        email,
        organization_id: session.user.organizationId || null,
        role_id: roleId,
        role_name: role.name,
        invited_by: session.user.id,
        status: 'pending',
        token,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single();

    if (invitationError) {
      console.error('Invitation creation error:', invitationError);
      return NextResponse.json(
        { error: 'Failed to create invitation', success: false },
        { status: 500 }
      );
    }

    // TODO: Send invitation email
    // This would typically integrate with an email service like SendGrid, Resend, etc.
    console.log(`Invitation email would be sent to ${email} with token ${token}`);

    return NextResponse.json({
      success: true,
      data: {
        id: invitation.id,
        email: invitation.email,
        organizationId: invitation.organization_id,
        roleId: invitation.role_id,
        roleName: invitation.role_name,
        invitedBy: invitation.invited_by,
        status: invitation.status,
        token: invitation.token,
        expiresAt: invitation.expires_at,
        acceptedAt: invitation.accepted_at,
        createdAt: invitation.created_at,
        updatedAt: invitation.updated_at,
      },
      message: 'Team invitation sent successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
