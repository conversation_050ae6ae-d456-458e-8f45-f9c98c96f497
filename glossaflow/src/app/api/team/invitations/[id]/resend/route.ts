import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get the existing invitation
    const { data: invitation, error: fetchError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('id', params.id)
      .single();

    if (fetchError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found', success: false },
        { status: 404 }
      );
    }

    // Check if invitation is still pending
    if (invitation.status !== 'pending') {
      return NextResponse.json(
        { error: 'Can only resend pending invitations', success: false },
        { status: 400 }
      );
    }

    // Generate new token and extend expiration
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

    // Update the invitation
    const { data: updatedInvitation, error: updateError } = await supabase
      .from('team_invitations')
      .update({
        token,
        expires_at: expiresAt.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .select()
      .single();

    if (updateError) {
      console.error('Database error:', updateError);
      return NextResponse.json(
        { error: 'Failed to resend invitation', success: false },
        { status: 500 }
      );
    }

    // TODO: Send invitation email
    // This would typically integrate with an email service
    console.log(`Invitation email would be resent to ${invitation.email} with new token ${token}`);

    return NextResponse.json({
      success: true,
      data: {
        id: updatedInvitation.id,
        email: updatedInvitation.email,
        organizationId: updatedInvitation.organization_id,
        roleId: updatedInvitation.role_id,
        roleName: updatedInvitation.role_name,
        invitedBy: updatedInvitation.invited_by,
        status: updatedInvitation.status,
        token: updatedInvitation.token,
        expiresAt: updatedInvitation.expires_at,
        acceptedAt: updatedInvitation.accepted_at,
        createdAt: updatedInvitation.created_at,
        updatedAt: updatedInvitation.updated_at,
      },
      message: 'Invitation resent successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
