import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { token, userData } = body;

    if (!token) {
      return NextResponse.json(
        { error: 'Missing invitation token', success: false },
        { status: 400 }
      );
    }

    // Find the invitation by token
    const { data: invitation, error: invitationError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .eq('status', 'pending')
      .single();

    if (invitationError || !invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation', success: false },
        { status: 404 }
      );
    }

    // Check if invitation has expired
    const now = new Date();
    const expiresAt = new Date(invitation.expires_at);
    if (now > expiresAt) {
      return NextResponse.json(
        { error: 'Invitation has expired', success: false },
        { status: 400 }
      );
    }

    // Check if the user's email matches the invitation
    if (session.user.email !== invitation.email) {
      return NextResponse.json(
        { error: 'Email does not match invitation', success: false },
        { status: 400 }
      );
    }

    // Check if user is already a team member
    const { data: existingMember } = await supabase
      .from('team_members')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('organization_id', invitation.organization_id)
      .single();

    if (existingMember) {
      return NextResponse.json(
        { error: 'User is already a team member', success: false },
        { status: 400 }
      );
    }

    // Create team member record
    const { data: teamMember, error: memberError } = await supabase
      .from('team_members')
      .insert({
        user_id: session.user.id,
        organization_id: invitation.organization_id,
        role_id: invitation.role_id,
        role_name: invitation.role_name,
        status: 'active',
        languages: [],
        specializations: [],
        timezone: 'UTC',
        joined_at: new Date().toISOString(),
        created_by: invitation.invited_by,
      })
      .select()
      .single();

    if (memberError) {
      console.error('Team member creation error:', memberError);
      return NextResponse.json(
        { error: 'Failed to create team member', success: false },
        { status: 500 }
      );
    }

    // Update invitation status
    const { error: updateError } = await supabase
      .from('team_invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', invitation.id);

    if (updateError) {
      console.error('Invitation update error:', updateError);
      // Don't fail the request if invitation update fails
    }

    // Get user information
    const { data: users } = await supabase.auth.admin.listUsers();
    const user = users?.users?.find(u => u.id === session.user.id);

    // Transform the team member data
    const transformedTeamMember = {
      id: teamMember.id,
      userId: teamMember.user_id,
      organizationId: teamMember.organization_id,
      roleId: teamMember.role_id,
      roleName: teamMember.role_name,
      status: teamMember.status,
      languages: teamMember.languages || [],
      specializations: teamMember.specializations || [],
      bio: teamMember.bio,
      hourlyRate: teamMember.hourly_rate,
      timezone: teamMember.timezone,
      joinedAt: teamMember.joined_at,
      lastActiveAt: teamMember.last_active_at,
      createdAt: teamMember.created_at,
      updatedAt: teamMember.updated_at,
      createdBy: teamMember.created_by,
      user: user ? {
        id: user.id,
        name: user.user_metadata?.name || user.user_metadata?.full_name,
        email: user.email,
        avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture,
      } : {
        id: session.user.id,
        name: session.user.name || 'Unknown User',
        email: session.user.email || '<EMAIL>',
        avatar: session.user.image,
      },
      stats: {
        projectsCompleted: 0,
        wordsTranslated: 0,
        segmentsTranslated: 0,
        segmentsReviewed: 0,
        averageRating: 0,
        currentProjects: 0,
        totalHoursWorked: 0,
      },
    };

    return NextResponse.json({
      success: true,
      data: transformedTeamMember,
      message: 'Invitation accepted successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
