import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { data: entry, error } = await supabase
      .from('terminology_entries')
      .update({
        approval_status: 'approved',
        reviewed_by: session.user.id,
      })
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to approve terminology entry', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: entry.id,
        sourceTerm: entry.source_term,
        targetTerm: entry.target_term,
        targetLanguage: entry.target_language,
        category: entry.category,
        context: entry.context,
        usageNotes: entry.usage_notes,
        approvalStatus: entry.approval_status,
        frequency: entry.frequency || 0,
        createdBy: entry.created_by,
        reviewedBy: entry.reviewed_by,
        lastUsed: entry.last_used,
        createdAt: entry.created_at,
        updatedAt: entry.updated_at,
        organizationId: entry.organization_id,
        projectId: entry.project_id,
      },
      message: 'Terminology entry approved successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
