import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get projects where user is a member or creator/manager
    const userId = session.user.id;

    // Get projects where user is a member
    const { data: memberProjects } = await supabase
      .from('projects')
      .select(`
        id,
        status,
        budget,
        spent,
        total_segments,
        completed_segments,
        project_members!inner(user_id)
      `)
      .eq('project_members.user_id', userId);

    // Get projects where user is creator or project manager
    const { data: ownedProjects } = await supabase
      .from('projects')
      .select(`
        id,
        status,
        budget,
        spent,
        total_segments,
        completed_segments
      `)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`);

    // Combine and deduplicate projects
    const allProjects = [...(memberProjects || []), ...(ownedProjects || [])];
    const uniqueProjects = allProjects.filter((project, index, self) =>
      index === self.findIndex(p => p.id === project.id)
    );

    const userProjects = uniqueProjects;

    if (projectsError) {
      console.error('Database error:', projectsError);
      return NextResponse.json(
        { error: 'Failed to fetch project stats', success: false },
        { status: 500 }
      );
    }

    // Calculate statistics
    const totalProjects = userProjects?.length || 0;
    const activeProjects = userProjects?.filter(p => 
      ['in_progress', 'review'].includes(p.status)
    ).length || 0;
    const completedProjects = userProjects?.filter(p => 
      p.status === 'completed'
    ).length || 0;

    const totalSegments = userProjects?.reduce((sum, p) => 
      sum + (p.total_segments || 0), 0
    ) || 0;
    const completedSegments = userProjects?.reduce((sum, p) => 
      sum + (p.completed_segments || 0), 0
    ) || 0;

    const totalBudget = userProjects?.reduce((sum, p) => 
      sum + (p.budget || 0), 0
    ) || 0;
    const spentBudget = userProjects?.reduce((sum, p) => 
      sum + (p.spent || 0), 0
    ) || 0;

    return NextResponse.json({
      success: true,
      data: {
        totalProjects,
        activeProjects,
        completedProjects,
        totalSegments,
        completedSegments,
        totalBudget,
        spentBudget,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
