import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const search = searchParams.get('search');
    const sourceLanguage = searchParams.get('sourceLanguage');
    const targetLanguage = searchParams.get('targetLanguage');

    const offset = (page - 1) * limit;

    // Build the query - simplified to avoid schema cache issues
    let query = supabase
      .from('projects')
      .select('*');

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (priority) {
      query = query.eq('priority', priority);
    }
    if (sourceLanguage) {
      query = query.eq('source_language', sourceLanguage);
    }
    if (targetLanguage) {
      query = query.contains('target_languages', [targetLanguage]);
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Get total count
    const { count } = await supabase
      .from('projects')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: projects, error } = await query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch projects', success: false },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format
    const transformedProjects = projects?.map(project => ({
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      priority: project.priority,
      sourceLanguage: project.source_language,
      targetLanguages: project.target_languages,
      createdAt: project.created_at,
      updatedAt: project.updated_at,
      dueDate: project.due_date,
      budget: project.budget,
      spent: project.spent || 0,
      totalSegments: project.total_segments || 0,
      completedSegments: project.completed_segments || 0,
      reviewedSegments: project.reviewed_segments || 0,
      approvedSegments: project.approved_segments || 0,
      createdBy: project.created_by,
      organizationId: project.organization_id,
      teamMembers: [], // Simplified - will fetch separately if needed
      files: [], // Simplified - will fetch separately if needed
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: transformedProjects,
        total: count || 0,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    // Get user's organization (if any)
    const { data: orgMembership } = await supabase
      .from('organization_members')
      .select('organization_id')
      .eq('user_id', userId)
      .single();

    const body = await request.json();
    const {
      name,
      description,
      sourceLanguage,
      targetLanguages,
      dueDate,
      budget,
      priority,
      teamMembers,
    } = body;

    // Create the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name,
        description,
        source_language: sourceLanguage,
        target_languages: targetLanguages,
        due_date: dueDate,
        budget,
        priority,
        status: 'draft',
        created_by: userId,
        organization_id: orgMembership?.organization_id || null,
        total_segments: 0,
        completed_segments: 0,
        reviewed_segments: 0,
        spent: 0,
      })
      .select()
      .single();

    if (projectError) {
      console.error('Project creation error:', projectError);
      return NextResponse.json(
        { error: 'Failed to create project', success: false },
        { status: 500 }
      );
    }

    // Add team members if provided
    if (teamMembers && teamMembers.length > 0) {
      const memberInserts = teamMembers.map((member: any) => ({
        project_id: project.id,
        user_id: member.userId,
        role: member.role,
        languages: member.languages,
        assigned_at: new Date().toISOString(),
      }));

      const { error: membersError } = await supabase
        .from('project_members')
        .insert(memberInserts);

      if (membersError) {
        console.error('Team members creation error:', membersError);
        // Don't fail the entire request, just log the error
      }
    }

    // Add the creator as a project manager
    await supabase
      .from('project_members')
      .insert({
        project_id: project.id,
        user_id: userId,
        role: 'project_manager',
        assigned_at: new Date().toISOString(),
      });

    return NextResponse.json({
      success: true,
      data: {
        id: project.id,
        name: project.name,
        description: project.description,
        status: project.status,
        priority: project.priority,
        sourceLanguage: project.source_language,
        targetLanguages: project.target_languages,
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        dueDate: project.due_date,
        budget: project.budget,
        spent: project.spent || 0,
        totalSegments: project.total_segments || 0,
        completedSegments: project.completed_segments || 0,
        reviewedSegments: project.reviewed_segments || 0,
        approvedSegments: 0,
        createdBy: project.created_by,
        organizationId: project.organization_id,
        teamMembers: [],
        files: [],
      },
      message: 'Project created successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
