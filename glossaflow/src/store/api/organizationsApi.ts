import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  Organization,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  GetOrganizationsResponse,
  GetOrganizationResponse,
  InviteUserRequest,
} from '@/types';
import { RootState } from '../index';

export const organizationsApi = createApi({
  reducerPath: 'organizationsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/organizations',
    prepareHeaders: (headers, { getState }) => {
      // Add authentication headers if needed
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Organization', 'Member', 'Subscription'],
  endpoints: (builder) => ({
    getOrganizations: builder.query<GetOrganizationsResponse, void>({
      query: () => '',
      providesTags: ['Organization'],
    }),
    createOrganization: builder.mutation<Organization, CreateOrganizationRequest>({
      query: (body) => ({
        url: '',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Organization'],
    }),
    getOrganization: builder.query<GetOrganizationResponse, string>({
      query: (orgId) => `/${orgId}`,
      providesTags: (result, error, orgId) => [
        { type: 'Organization', id: orgId },
        'Member',
        'Subscription',
      ],
    }),
    updateOrganization: builder.mutation<
      Organization,
      { orgId: string; data: UpdateOrganizationRequest }
    >({
      query: ({ orgId, data }) => ({
        url: `/${orgId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { orgId }) => [
        { type: 'Organization', id: orgId },
      ],
    }),
    inviteUser: builder.mutation<
      void,
      { orgId: string; data: InviteUserRequest }
    >({
      query: ({ orgId, data }) => ({
        url: `/${orgId}/invite`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Member'],
    }),
    removeMember: builder.mutation<
      void,
      { orgId: string; memberId: string }
    >({
      query: ({ orgId, memberId }) => ({
        url: `/${orgId}/members/${memberId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Member'],
    }),
    updateMemberRole: builder.mutation<
      void,
      { orgId: string; memberId: string; role: string }
    >({
      query: ({ orgId, memberId, role }) => ({
        url: `/${orgId}/members/${memberId}`,
        method: 'PUT',
        body: { role },
      }),
      invalidatesTags: ['Member'],
    }),
  }),
});

export const {
  useGetOrganizationsQuery,
  useCreateOrganizationMutation,
  useGetOrganizationQuery,
  useUpdateOrganizationMutation,
  useInviteUserMutation,
  useRemoveMemberMutation,
  useUpdateMemberRoleMutation,
} = organizationsApi;
