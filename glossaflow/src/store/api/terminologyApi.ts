import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  TerminologyEntry,
  CreateTerminologyRequest,
  UpdateTerminologyRequest,
  GetTerminologyResponse,
} from '@/types';

interface GetTerminologyParams {
  projectId: string;
  search?: string;
  category?: string;
  language?: string;
  status?: string;
  page?: number;
  limit?: number;
}

interface ImportTerminologyRequest {
  file: File;
  format: 'csv' | 'tmx' | 'excel';
  mapping: {
    sourceTerm: string;
    targetTerm: string;
    targetLanguage: string;
    category?: string;
    context?: string;
    usageNotes?: string;
  };
  overwriteExisting: boolean;
}

interface ImportTerminologyResponse {
  imported: number;
  errors: Array<{
    row: number;
    error: string;
  }>;
  duplicates: Array<{
    sourceTerm: string;
    targetTerm: string;
  }>;
}

export const terminologyApi = createApi({
  reducerPath: 'terminologyApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/terminology',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Terminology', 'TerminologyStats'],
  endpoints: (builder) => ({
    getTerminology: builder.query<GetTerminologyResponse, GetTerminologyParams>({
      query: ({ projectId, ...params }) => ({
        url: `/projects/${projectId}`,
        params,
      }),
      providesTags: ['Terminology'],
    }),
    createTerminologyEntry: builder.mutation<
      TerminologyEntry,
      { projectId: string; data: CreateTerminologyRequest }
    >({
      query: ({ projectId, data }) => ({
        url: `/projects/${projectId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Terminology', 'TerminologyStats'],
    }),
    updateTerminologyEntry: builder.mutation<
      TerminologyEntry,
      { entryId: string; data: UpdateTerminologyRequest }
    >({
      query: ({ entryId, data }) => ({
        url: `/${entryId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Terminology'],
    }),
    deleteTerminologyEntry: builder.mutation<void, string>({
      query: (entryId) => ({
        url: `/${entryId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Terminology', 'TerminologyStats'],
    }),
    approveTerminologyEntry: builder.mutation<
      TerminologyEntry,
      { entryId: string; approved: boolean }
    >({
      query: ({ entryId, approved }) => ({
        url: `/${entryId}/approve`,
        method: 'PUT',
        body: { approved },
      }),
      invalidatesTags: ['Terminology'],
    }),
    importTerminology: builder.mutation<
      ImportTerminologyResponse,
      { projectId: string; data: FormData }
    >({
      query: ({ projectId, data }) => ({
        url: `/projects/${projectId}/import`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Terminology', 'TerminologyStats'],
    }),
    exportTerminology: builder.mutation<
      { downloadUrl: string; expiresAt: string },
      { projectId: string; format: string; language?: string }
    >({
      query: ({ projectId, format, language }) => ({
        url: `/projects/${projectId}/export`,
        method: 'POST',
        body: { format, language },
      }),
    }),
    searchTerminology: builder.query<
      Array<{
        entry: TerminologyEntry;
        confidence: number;
        context: string;
      }>,
      { projectId: string; text: string; targetLanguage: string }
    >({
      query: ({ projectId, text, targetLanguage }) => ({
        url: `/projects/${projectId}/search`,
        params: { text, targetLanguage },
      }),
    }),
    getTerminologyStats: builder.query<
      {
        totalEntries: number;
        approvedEntries: number;
        pendingEntries: number;
        rejectedEntries: number;
        categoriesCount: Record<string, number>;
        languagesCount: Record<string, number>;
      },
      string
    >({
      query: (projectId) => `/projects/${projectId}/stats`,
      providesTags: ['TerminologyStats'],
    }),
    bulkApproveTerminology: builder.mutation<
      void,
      { entryIds: string[]; approved: boolean }
    >({
      query: ({ entryIds, approved }) => ({
        url: '/bulk-approve',
        method: 'PUT',
        body: { entryIds, approved },
      }),
      invalidatesTags: ['Terminology'],
    }),
    bulkDeleteTerminology: builder.mutation<void, string[]>({
      query: (entryIds) => ({
        url: '/bulk-delete',
        method: 'DELETE',
        body: { entryIds },
      }),
      invalidatesTags: ['Terminology', 'TerminologyStats'],
    }),
  }),
});

export const {
  useGetTerminologyQuery,
  useCreateTerminologyEntryMutation,
  useUpdateTerminologyEntryMutation,
  useDeleteTerminologyEntryMutation,
  useApproveTerminologyEntryMutation,
  useImportTerminologyMutation,
  useExportTerminologyMutation,
  useSearchTerminologyQuery,
  useGetTerminologyStatsQuery,
  useBulkApproveTerminologyMutation,
  useBulkDeleteTerminologyMutation,
} = terminologyApi;
