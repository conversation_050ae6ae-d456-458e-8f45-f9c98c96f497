import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  Project,
  CreateProjectRequest,
  UpdateProjectRequest,
  GetProjectsResponse,
  GetProjectResponse,
  GetSegmentsResponse,
  SourceDocument,
} from '@/types';

interface GetProjectsParams {
  orgId?: string;
  status?: string;
  page?: number;
  limit?: number;
}

interface GetSegmentsParams {
  projectId: string;
  page?: number;
  limit?: number;
  status?: string;
  translatorId?: string;
  reviewerId?: string;
}

export const projectsApi = createApi({
  reducerPath: 'projectsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/projects',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Project', 'Segment', 'Document', 'Terminology'],
  endpoints: (builder) => ({
    getProjects: builder.query<GetProjectsResponse, GetProjectsParams>({
      query: (params) => ({
        url: '',
        params,
      }),
      providesTags: ['Project'],
    }),
    createProject: builder.mutation<Project, CreateProjectRequest>({
      query: (body) => ({
        url: '',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Project'],
    }),
    getProject: builder.query<GetProjectResponse, string>({
      query: (projectId) => `/${projectId}`,
      providesTags: (result, error, projectId) => [
        { type: 'Project', id: projectId },
        'Document',
        'Terminology',
      ],
    }),
    updateProject: builder.mutation<
      Project,
      { projectId: string; data: UpdateProjectRequest }
    >({
      query: ({ projectId, data }) => ({
        url: `/${projectId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { projectId }) => [
        { type: 'Project', id: projectId },
      ],
    }),
    deleteProject: builder.mutation<void, string>({
      query: (projectId) => ({
        url: `/${projectId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Project'],
    }),
    uploadDocuments: builder.mutation<
      SourceDocument[],
      { projectId: string; data: FormData }
    >({
      query: ({ projectId, data }) => ({
        url: `/${projectId}/documents`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Document', 'Segment'],
    }),
    getSegments: builder.query<GetSegmentsResponse, GetSegmentsParams>({
      query: ({ projectId, ...params }) => ({
        url: `/${projectId}/segments`,
        params,
      }),
      providesTags: ['Segment'],
    }),
    assignTranslator: builder.mutation<
      void,
      { projectId: string; segmentIds: string[]; translatorId: string }
    >({
      query: ({ projectId, segmentIds, translatorId }) => ({
        url: `/${projectId}/assign-translator`,
        method: 'POST',
        body: { segmentIds, translatorId },
      }),
      invalidatesTags: ['Segment'],
    }),
    assignReviewer: builder.mutation<
      void,
      { projectId: string; segmentIds: string[]; reviewerId: string }
    >({
      query: ({ projectId, segmentIds, reviewerId }) => ({
        url: `/${projectId}/assign-reviewer`,
        method: 'POST',
        body: { segmentIds, reviewerId },
      }),
      invalidatesTags: ['Segment'],
    }),
    getProjectStats: builder.query<
      {
        totalSegments: number;
        completedSegments: number;
        pendingSegments: number;
        reviewedSegments: number;
        approvedSegments: number;
      },
      string
    >({
      query: (projectId) => `/${projectId}/stats`,
      providesTags: (result, error, projectId) => [
        { type: 'Project', id: `${projectId}-stats` },
      ],
    }),
  }),
});

export const {
  useGetProjectsQuery,
  useCreateProjectMutation,
  useGetProjectQuery,
  useUpdateProjectMutation,
  useDeleteProjectMutation,
  useUploadDocumentsMutation,
  useGetSegmentsQuery,
  useAssignTranslatorMutation,
  useAssignReviewerMutation,
  useGetProjectStatsQuery,
} = projectsApi;
