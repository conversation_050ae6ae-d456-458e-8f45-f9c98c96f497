import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  TranslationSegment,
  UpdateSegmentRequest,
  GetSegmentResponse,
  Comment,
  Review,
  CreateCommentRequest,
  CreateReviewRequest,
} from '@/types';

export const translationApi = createApi({
  reducerPath: 'translationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/segments',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Segment', 'Comment', 'Review'],
  endpoints: (builder) => ({
    getSegment: builder.query<GetSegmentResponse, string>({
      query: (segmentId) => `/${segmentId}`,
      providesTags: (result, error, segmentId) => [
        { type: 'Segment', id: segmentId },
        'Comment',
        'Review',
      ],
    }),
    updateSegment: builder.mutation<
      TranslationSegment,
      { segmentId: string; data: UpdateSegmentRequest }
    >({
      query: ({ segmentId, data }) => ({
        url: `/${segmentId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { segmentId }) => [
        { type: 'Segment', id: segmentId },
      ],
      // Optimistic update for better UX
      async onQueryStarted({ segmentId, data }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          translationApi.util.updateQueryData('getSegment', segmentId, (draft) => {
            Object.assign(draft.segment, data);
          })
        );
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
    }),
    createComment: builder.mutation<
      Comment,
      { segmentId: string; data: CreateCommentRequest }
    >({
      query: ({ segmentId, data }) => ({
        url: `/${segmentId}/comments`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Comment'],
    }),
    updateComment: builder.mutation<
      Comment,
      { commentId: string; data: { content: string; resolved?: boolean } }
    >({
      query: ({ commentId, data }) => ({
        url: `/comments/${commentId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Comment'],
    }),
    deleteComment: builder.mutation<void, string>({
      query: (commentId) => ({
        url: `/comments/${commentId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Comment'],
    }),
    createReview: builder.mutation<
      Review,
      { segmentId: string; data: CreateReviewRequest }
    >({
      query: ({ segmentId, data }) => ({
        url: `/${segmentId}/reviews`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Review'],
    }),
    getSegmentHistory: builder.query<
      Array<{
        id: string;
        segmentId: string;
        previousText: string;
        newText: string;
        changedBy: string;
        changeType: string;
        timestamp: string;
      }>,
      string
    >({
      query: (segmentId) => `/${segmentId}/history`,
    }),
    bulkUpdateSegments: builder.mutation<
      void,
      { segmentIds: string[]; data: UpdateSegmentRequest }
    >({
      query: ({ segmentIds, data }) => ({
        url: '/bulk-update',
        method: 'PUT',
        body: { segmentIds, data },
      }),
      invalidatesTags: ['Segment'],
    }),
    getTranslationMemoryMatches: builder.query<
      Array<{
        sourceText: string;
        targetText: string;
        similarity: number;
        origin: string;
        lastUsed: string;
      }>,
      { projectId: string; sourceText: string; targetLanguage: string }
    >({
      query: ({ projectId, sourceText, targetLanguage }) => ({
        url: '/translation-memory',
        params: { projectId, sourceText, targetLanguage },
      }),
    }),
  }),
});

export const {
  useGetSegmentQuery,
  useUpdateSegmentMutation,
  useCreateCommentMutation,
  useUpdateCommentMutation,
  useDeleteCommentMutation,
  useCreateReviewMutation,
  useGetSegmentHistoryQuery,
  useBulkUpdateSegmentsMutation,
  useGetTranslationMemoryMatchesQuery,
} = translationApi;
