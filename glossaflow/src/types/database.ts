// Database types for GlossaFlow
export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string;
          name: string;
          slug: string;
          industry: string | null;
          team_size: string | null;
          subscription_tier: string;
          stripe_customer_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          industry?: string | null;
          team_size?: string | null;
          subscription_tier?: string;
          stripe_customer_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          industry?: string | null;
          team_size?: string | null;
          subscription_tier?: string;
          stripe_customer_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          email: string;
          name: string | null;
          avatar_url: string | null;
          email_verified: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name?: string | null;
          avatar_url?: string | null;
          email_verified?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string | null;
          avatar_url?: string | null;
          email_verified?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      organization_members: {
        Row: {
          id: string;
          organization_id: string;
          user_id: string;
          role: UserRole;
          invited_by: string | null;
          invited_at: string;
          joined_at: string | null;
          status: MemberStatus;
        };
        Insert: {
          id?: string;
          organization_id: string;
          user_id: string;
          role: UserRole;
          invited_by?: string | null;
          invited_at?: string;
          joined_at?: string | null;
          status?: MemberStatus;
        };
        Update: {
          id?: string;
          organization_id?: string;
          user_id?: string;
          role?: UserRole;
          invited_by?: string | null;
          invited_at?: string;
          joined_at?: string | null;
          status?: MemberStatus;
        };
      };
      projects: {
        Row: {
          id: string;
          organization_id: string;
          name: string;
          description: string | null;
          source_language: string;
          target_languages: string[];
          document_type: string;
          priority: Priority;
          deadline: string | null;
          status: ProjectStatus;
          created_by: string | null;
          project_manager_id: string | null;
          style_guide: any | null;
          workflow_config: any | null;
          created_at: string;
          updated_at: string;
          due_date: string | null;
          budget: number | null;
          spent: number | null;
          total_segments: number | null;
          completed_segments: number | null;
          reviewed_segments: number | null;
          approved_segments: number | null;
          total_chapters: number | null;
          completed_chapters: number | null;
        };
        Insert: {
          id?: string;
          organization_id: string;
          name: string;
          description?: string | null;
          source_language: string;
          target_languages: string[];
          document_type: string;
          priority?: Priority;
          deadline?: string | null;
          status?: ProjectStatus;
          created_by?: string | null;
          project_manager_id?: string | null;
          style_guide?: any | null;
          workflow_config?: any | null;
          created_at?: string;
          updated_at?: string;
          due_date?: string | null;
          budget?: number | null;
          spent?: number | null;
          total_segments?: number | null;
          completed_segments?: number | null;
          reviewed_segments?: number | null;
          approved_segments?: number | null;
          total_chapters?: number | null;
          completed_chapters?: number | null;
        };
        Update: {
          id?: string;
          organization_id?: string;
          name?: string;
          description?: string | null;
          source_language?: string;
          target_languages?: string[];
          document_type?: string;
          priority?: Priority;
          deadline?: string | null;
          status?: ProjectStatus;
          created_by?: string | null;
          project_manager_id?: string | null;
          style_guide?: any | null;
          workflow_config?: any | null;
          created_at?: string;
          updated_at?: string;
          due_date?: string | null;
          budget?: number | null;
          spent?: number | null;
          total_segments?: number | null;
          completed_segments?: number | null;
          reviewed_segments?: number | null;
          approved_segments?: number | null;
          total_chapters?: number | null;
          completed_chapters?: number | null;
        };
      };
      translation_segments: {
        Row: {
          id: string;
          project_id: string;
          document_id: string;
          chapter_id: string | null;
          segment_number: number;
          source_text: string;
          target_text: string | null;
          target_language: string;
          status: SegmentStatus;
          assigned_translator_id: string | null;
          assigned_reviewer_id: string | null;
          word_count: number | null;
          character_count: number | null;
          translation_started_at: string | null;
          translation_completed_at: string | null;
          review_completed_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          document_id: string;
          chapter_id?: string | null;
          segment_number: number;
          source_text: string;
          target_text?: string | null;
          target_language: string;
          status?: SegmentStatus;
          assigned_translator_id?: string | null;
          assigned_reviewer_id?: string | null;
          word_count?: number | null;
          character_count?: number | null;
          translation_started_at?: string | null;
          translation_completed_at?: string | null;
          review_completed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          document_id?: string;
          chapter_id?: string | null;
          segment_number?: number;
          source_text?: string;
          target_text?: string | null;
          target_language?: string;
          status?: SegmentStatus;
          assigned_translator_id?: string | null;
          assigned_reviewer_id?: string | null;
          word_count?: number | null;
          character_count?: number | null;
          translation_started_at?: string | null;
          translation_completed_at?: string | null;
          review_completed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      terminology_entries: {
        Row: {
          id: string;
          project_id: string;
          source_term: string;
          target_term: string;
          target_language: string;
          category: string;
          context: string | null;
          usage_notes: string | null;
          approval_status: ApprovalStatus;
          frequency: number;
          created_by: string | null;
          reviewed_by: string | null;
          last_used_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          source_term: string;
          target_term: string;
          target_language: string;
          category?: string;
          context?: string | null;
          usage_notes?: string | null;
          approval_status?: ApprovalStatus;
          frequency?: number;
          created_by?: string | null;
          reviewed_by?: string | null;
          last_used_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          source_term?: string;
          target_term?: string;
          target_language?: string;
          category?: string;
          context?: string | null;
          usage_notes?: string | null;
          approval_status?: ApprovalStatus;
          frequency?: number;
          created_by?: string | null;
          reviewed_by?: string | null;
          last_used_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      chapters: {
        Row: {
          id: string;
          project_id: string;
          chapter_number: number;
          title: string;
          description: string | null;
          source_word_count: number;
          target_word_count: number;
          estimated_cost: number;
          actual_cost: number;
          status: ChapterStatus;
          progress_percentage: number;
          total_segments: number;
          completed_segments: number;
          reviewed_segments: number;
          approved_segments: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          chapter_number: number;
          title: string;
          description?: string | null;
          source_word_count?: number;
          target_word_count?: number;
          estimated_cost?: number;
          actual_cost?: number;
          status?: ChapterStatus;
          progress_percentage?: number;
          total_segments?: number;
          completed_segments?: number;
          reviewed_segments?: number;
          approved_segments?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          chapter_number?: number;
          title?: string;
          description?: string | null;
          source_word_count?: number;
          target_word_count?: number;
          estimated_cost?: number;
          actual_cost?: number;
          status?: ChapterStatus;
          progress_percentage?: number;
          total_segments?: number;
          completed_segments?: number;
          reviewed_segments?: number;
          approved_segments?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      credits: {
        Row: {
          id: string;
          organization_id: string;
          balance: number;
          total_purchased: number;
          total_spent: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          balance?: number;
          total_purchased?: number;
          total_spent?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          balance?: number;
          total_purchased?: number;
          total_spent?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      credit_transactions: {
        Row: {
          id: string;
          organization_id: string;
          user_id: string | null;
          project_id: string | null;
          chapter_id: string | null;
          segment_id: string | null;
          transaction_type: CreditTransactionType;
          amount: number;
          description: string | null;
          metadata: any;
          created_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          user_id?: string | null;
          project_id?: string | null;
          chapter_id?: string | null;
          segment_id?: string | null;
          transaction_type: CreditTransactionType;
          amount: number;
          description?: string | null;
          metadata?: any;
          created_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          user_id?: string | null;
          project_id?: string | null;
          chapter_id?: string | null;
          segment_id?: string | null;
          transaction_type?: CreditTransactionType;
          amount?: number;
          description?: string | null;
          metadata?: any;
          created_at?: string;
        };
      };
    };
  };
}

// Enum types
export type UserRole = 'admin' | 'project_manager' | 'senior_translator' | 'translator' | 'reviewer' | 'client';
export type MemberStatus = 'pending' | 'active' | 'suspended';
export type Priority = 'low' | 'medium' | 'high' | 'urgent';
export type ProjectStatus = 'draft' | 'active' | 'paused' | 'completed' | 'archived';
export type SegmentStatus = 'pending' | 'in_progress' | 'translated' | 'reviewed' | 'approved' | 'rejected';
export type ApprovalStatus = 'pending' | 'approved' | 'rejected';
export type DocumentType = 'novel' | 'game' | 'technical' | 'marketing' | 'general';
export type CommentType = 'general' | 'suggestion' | 'issue' | 'approval';
export type ReviewType = 'linguistic' | 'cultural' | 'technical' | 'final';
export type ReviewStatus = 'pending' | 'approved' | 'rejected' | 'needs_revision';
export type ChapterStatus = 'pending' | 'in_progress' | 'translated' | 'reviewed' | 'approved';
export type CreditTransactionType = 'purchase' | 'ai_translation' | 'refund' | 'bonus';
