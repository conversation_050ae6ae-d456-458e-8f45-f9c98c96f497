-- Create team management tables
-- This migration creates tables for team members, roles, invitations, and related functionality

-- Create team_roles table for role definitions
CREATE TABLE IF NOT EXISTS team_roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  permissions JSONB DEFAULT '{}',
  is_system_role BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_members table
CREATE TABLE IF NOT EXISTS team_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID,
  role_id UUID REFERENCES team_roles(id) ON DELETE SET NULL,
  role_name TEXT, -- Denormalized for easier queries
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'suspended')),
  languages TEXT[] DEFAULT '{}',
  specializations TEXT[] DEFAULT '{}',
  bio TEXT,
  hourly_rate DECIMAL(10,2),
  timezone TEXT DEFAULT 'UTC',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  UNIQUE(user_id, organization_id)
);

-- Create team_invitations table for pending invites
CREATE TABLE IF NOT EXISTS team_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL,
  organization_id UUID,
  role_id UUID REFERENCES team_roles(id) ON DELETE SET NULL,
  role_name TEXT,
  invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
  token TEXT UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_member_stats table for performance tracking
CREATE TABLE IF NOT EXISTS team_member_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  team_member_id UUID REFERENCES team_members(id) ON DELETE CASCADE,
  projects_completed INTEGER DEFAULT 0,
  words_translated INTEGER DEFAULT 0,
  segments_translated INTEGER DEFAULT 0,
  segments_reviewed INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0.0,
  current_projects INTEGER DEFAULT 0,
  total_hours_worked DECIMAL(10,2) DEFAULT 0.0,
  last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_member_id)
);

-- Create team_member_languages junction table for better language management
CREATE TABLE IF NOT EXISTS team_member_languages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  team_member_id UUID REFERENCES team_members(id) ON DELETE CASCADE,
  language_code TEXT NOT NULL,
  language_name TEXT NOT NULL,
  proficiency_level TEXT CHECK (proficiency_level IN ('native', 'fluent', 'advanced', 'intermediate', 'beginner')),
  is_source_language BOOLEAN DEFAULT false,
  is_target_language BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_member_id, language_code)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_team_members_organization_id ON team_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_team_members_role_id ON team_members(role_id);
CREATE INDEX IF NOT EXISTS idx_team_members_status ON team_members(status);
CREATE INDEX IF NOT EXISTS idx_team_members_joined_at ON team_members(joined_at);
CREATE INDEX IF NOT EXISTS idx_team_members_last_active ON team_members(last_active_at);

CREATE INDEX IF NOT EXISTS idx_team_invitations_email ON team_invitations(email);
CREATE INDEX IF NOT EXISTS idx_team_invitations_organization_id ON team_invitations(organization_id);
CREATE INDEX IF NOT EXISTS idx_team_invitations_status ON team_invitations(status);
CREATE INDEX IF NOT EXISTS idx_team_invitations_token ON team_invitations(token);
CREATE INDEX IF NOT EXISTS idx_team_invitations_expires_at ON team_invitations(expires_at);

CREATE INDEX IF NOT EXISTS idx_team_member_stats_team_member_id ON team_member_stats(team_member_id);
CREATE INDEX IF NOT EXISTS idx_team_member_languages_team_member_id ON team_member_languages(team_member_id);
CREATE INDEX IF NOT EXISTS idx_team_member_languages_language_code ON team_member_languages(language_code);

-- Create updated_at trigger functions
CREATE TRIGGER update_team_roles_updated_at 
  BEFORE UPDATE ON team_roles 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_members_updated_at 
  BEFORE UPDATE ON team_members 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_invitations_updated_at 
  BEFORE UPDATE ON team_invitations 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_member_stats_updated_at 
  BEFORE UPDATE ON team_member_stats 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE team_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_member_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_member_languages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for team_roles
CREATE POLICY "Anyone can view team roles" ON team_roles
  FOR SELECT USING (true);

CREATE POLICY "Only admins can manage team roles" ON team_roles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      WHERE tm.user_id = auth.uid()
      AND tm.role_name IN ('Admin', 'Organization Admin')
      AND tm.status = 'active'
    )
  );

-- Create RLS policies for team_members
CREATE POLICY "Users can view team members in their organization" ON team_members
  FOR SELECT USING (
    organization_id IN (
      SELECT tm.organization_id FROM team_members tm
      WHERE tm.user_id = auth.uid()
      AND tm.status = 'active'
    ) OR
    user_id = auth.uid()
  );

CREATE POLICY "Users can update their own team member profile" ON team_members
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can manage team members in their organization" ON team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      WHERE tm.user_id = auth.uid()
      AND tm.organization_id = team_members.organization_id
      AND tm.role_name IN ('Admin', 'Organization Admin', 'Project Manager')
      AND tm.status = 'active'
    )
  );

-- Create RLS policies for team_invitations
CREATE POLICY "Users can view invitations for their organization" ON team_invitations
  FOR SELECT USING (
    organization_id IN (
      SELECT tm.organization_id FROM team_members tm
      WHERE tm.user_id = auth.uid()
      AND tm.role_name IN ('Admin', 'Organization Admin', 'Project Manager')
      AND tm.status = 'active'
    )
  );

CREATE POLICY "Admins can manage invitations for their organization" ON team_invitations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      WHERE tm.user_id = auth.uid()
      AND tm.organization_id = team_invitations.organization_id
      AND tm.role_name IN ('Admin', 'Organization Admin', 'Project Manager')
      AND tm.status = 'active'
    )
  );

-- Create RLS policies for team_member_stats
CREATE POLICY "Users can view stats for team members in their organization" ON team_member_stats
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      WHERE tm.id = team_member_stats.team_member_id
      AND tm.organization_id IN (
        SELECT tm2.organization_id FROM team_members tm2
        WHERE tm2.user_id = auth.uid()
        AND tm2.status = 'active'
      )
    )
  );

CREATE POLICY "System can update team member stats" ON team_member_stats
  FOR ALL USING (true);

-- Create RLS policies for team_member_languages
CREATE POLICY "Users can view languages for team members in their organization" ON team_member_languages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      WHERE tm.id = team_member_languages.team_member_id
      AND tm.organization_id IN (
        SELECT tm2.organization_id FROM team_members tm2
        WHERE tm2.user_id = auth.uid()
        AND tm2.status = 'active'
      )
    )
  );

CREATE POLICY "Users can manage their own language settings" ON team_member_languages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_members tm
      WHERE tm.id = team_member_languages.team_member_id
      AND tm.user_id = auth.uid()
    )
  );

-- Insert default team roles
INSERT INTO team_roles (name, description, permissions, is_system_role) VALUES
('Admin', 'Full system administration access', '{"all": true}', true),
('Organization Admin', 'Organization-level administration', '{"organization": true, "team": true, "projects": true}', true),
('Project Manager', 'Project and team management', '{"projects": true, "team_view": true, "assign_tasks": true}', true),
('Lead Translator', 'Senior translator with review capabilities', '{"translate": true, "review": true, "terminology": true}', true),
('Senior Reviewer', 'Quality assurance and review', '{"review": true, "approve": true, "terminology": true}', true),
('Translator', 'Translation work', '{"translate": true, "terminology_view": true}', true),
('Reviewer', 'Review and quality assurance', '{"review": true, "terminology_view": true}', true),
('Client', 'Client access for project viewing', '{"view_projects": true}', true)
ON CONFLICT (name) DO NOTHING;

-- Insert sample team members (only if users exist)
DO $$
DECLARE
    sample_user_id UUID;
    admin_role_id UUID;
    translator_role_id UUID;
    reviewer_role_id UUID;
    pm_role_id UUID;
    lead_role_id UUID;
    sample_org_id UUID := gen_random_uuid();
BEGIN
    -- Get a sample user ID
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;

    -- Get role IDs
    SELECT id INTO admin_role_id FROM team_roles WHERE name = 'Admin';
    SELECT id INTO translator_role_id FROM team_roles WHERE name = 'Translator';
    SELECT id INTO reviewer_role_id FROM team_roles WHERE name = 'Senior Reviewer';
    SELECT id INTO pm_role_id FROM team_roles WHERE name = 'Project Manager';
    SELECT id INTO lead_role_id FROM team_roles WHERE name = 'Lead Translator';

    -- Only insert if we have a user
    IF sample_user_id IS NOT NULL THEN
        -- Insert sample team members
        INSERT INTO team_members (
          user_id,
          organization_id,
          role_id,
          role_name,
          status,
          languages,
          specializations,
          bio,
          hourly_rate,
          timezone,
          joined_at,
          last_active_at,
          created_by
        ) VALUES
        (
          sample_user_id,
          sample_org_id,
          lead_role_id,
          'Lead Translator',
          'active',
          ARRAY['English', 'Japanese', 'Korean'],
          ARRAY['Technical', 'Software'],
          'Experienced technical translator specializing in software localization',
          75.00,
          'Asia/Tokyo',
          '2023-06-15'::timestamp,
          NOW() - INTERVAL '2 hours',
          sample_user_id
        );

        -- Insert team member stats for the sample user
        INSERT INTO team_member_stats (
          team_member_id,
          projects_completed,
          words_translated,
          segments_translated,
          segments_reviewed,
          average_rating,
          current_projects,
          total_hours_worked
        )
        SELECT
          tm.id,
          24,
          125000,
          2500,
          1200,
          4.9,
          3,
          480.5
        FROM team_members tm
        WHERE tm.user_id = sample_user_id
        AND tm.organization_id = sample_org_id;

        -- Insert language proficiencies
        INSERT INTO team_member_languages (
          team_member_id,
          language_code,
          language_name,
          proficiency_level,
          is_source_language,
          is_target_language
        )
        SELECT
          tm.id,
          lang.code,
          lang.name,
          lang.proficiency,
          lang.is_source,
          lang.is_target
        FROM team_members tm,
        (VALUES
          ('en', 'English', 'native', true, false),
          ('ja', 'Japanese', 'fluent', false, true),
          ('ko', 'Korean', 'advanced', false, true)
        ) AS lang(code, name, proficiency, is_source, is_target)
        WHERE tm.user_id = sample_user_id
        AND tm.organization_id = sample_org_id;

        -- Insert a sample invitation
        INSERT INTO team_invitations (
          email,
          organization_id,
          role_id,
          role_name,
          invited_by,
          status,
          token,
          expires_at
        ) VALUES (
          '<EMAIL>',
          sample_org_id,
          translator_role_id,
          'Translator',
          sample_user_id,
          'pending',
          encode(gen_random_bytes(32), 'hex'),
          NOW() + INTERVAL '7 days'
        );
    END IF;
END $$;
