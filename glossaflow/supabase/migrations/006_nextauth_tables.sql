-- NextAuth.js required tables for Supabase adapter
-- This migration adds the tables required by @next-auth/supabase-adapter

-- Accounts table for OAuth providers
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    provider VARCHAR(255) NOT NULL,
    provider_account_id VARCHAR(255) NOT NULL,
    refresh_token TEXT,
    access_token TEXT,
    expires_at BIGINT,
    token_type VARCHAR(255),
    scope VARCHAR(255),
    id_token TEXT,
    session_state VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider, provider_account_id)
);

-- Sessions table for user sessions
CREATE TABLE IF NOT EXISTS sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    expires TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Verification tokens for email verification, password reset, etc.
CREATE TABLE IF NOT EXISTS verification_tokens (
    identifier VARCHAR(255) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (identifier, token)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS accounts_user_id_idx ON accounts(user_id);
CREATE INDEX IF NOT EXISTS sessions_user_id_idx ON sessions(user_id);
CREATE INDEX IF NOT EXISTS sessions_session_token_idx ON sessions(session_token);
CREATE INDEX IF NOT EXISTS verification_tokens_token_idx ON verification_tokens(token);

-- Update the existing users table to be compatible with NextAuth
-- Add columns that NextAuth expects if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS image TEXT;

-- Update existing email_verified boolean to timestamp for NextAuth compatibility
-- This is a one-time migration to convert boolean to timestamp
DO $$
BEGIN
    -- Check if email_verified is boolean type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'email_verified' 
        AND data_type = 'boolean'
    ) THEN
        -- Add temporary column
        ALTER TABLE users ADD COLUMN email_verified_temp TIMESTAMP WITH TIME ZONE;
        
        -- Convert boolean to timestamp
        UPDATE users 
        SET email_verified_temp = CASE 
            WHEN email_verified = true THEN created_at 
            ELSE NULL 
        END;
        
        -- Drop old column and rename new one
        ALTER TABLE users DROP COLUMN email_verified;
        ALTER TABLE users RENAME COLUMN email_verified_temp TO email_verified;
    END IF;
END $$;

-- RLS Policies for NextAuth tables
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_tokens ENABLE ROW LEVEL SECURITY;

-- Accounts policies
CREATE POLICY "Users can view their own accounts" ON accounts
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own accounts" ON accounts
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own accounts" ON accounts
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own accounts" ON accounts
    FOR DELETE USING (user_id = auth.uid());

-- Sessions policies
CREATE POLICY "Users can view their own sessions" ON sessions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own sessions" ON sessions
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own sessions" ON sessions
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own sessions" ON sessions
    FOR DELETE USING (user_id = auth.uid());

-- Verification tokens policies (more permissive for auth flows)
CREATE POLICY "Anyone can view verification tokens" ON verification_tokens
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert verification tokens" ON verification_tokens
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can delete verification tokens" ON verification_tokens
    FOR DELETE USING (true);

-- Grant necessary permissions to authenticated users
GRANT ALL ON accounts TO authenticated;
GRANT ALL ON sessions TO authenticated;
GRANT ALL ON verification_tokens TO authenticated;

-- Grant permissions to service role for NextAuth operations
GRANT ALL ON accounts TO service_role;
GRANT ALL ON sessions TO service_role;
GRANT ALL ON verification_tokens TO service_role;
GRANT ALL ON users TO service_role;

-- Comments for documentation
COMMENT ON TABLE accounts IS 'NextAuth.js accounts table for OAuth providers';
COMMENT ON TABLE sessions IS 'NextAuth.js sessions table for user sessions';
COMMENT ON TABLE verification_tokens IS 'NextAuth.js verification tokens for email verification and password reset';

COMMENT ON COLUMN users.email_verified IS 'Timestamp when email was verified (NextAuth.js compatibility)';
COMMENT ON COLUMN users.image IS 'User profile image URL (NextAuth.js compatibility)';
