-- GlossaFlow Database Triggers and Functions
-- This migration creates automated functions and triggers for the platform

-- Function to update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at 
    BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_segments_updated_at 
    BEFORE UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_terminology_updated_at 
    BEFORE UPDATE ON terminology_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at 
    BEFORE UPDATE ON comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at 
    BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update terminology frequency on usage
CREATE OR REPLACE FUNCTION update_terminology_frequency()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update if target_text changed and is not null
    IF NEW.target_text IS NOT NULL AND (OLD.target_text IS NULL OR NEW.target_text != OLD.target_text) THEN
        UPDATE terminology_entries 
        SET 
            frequency = frequency + 1, 
            last_used_at = NOW()
        WHERE project_id = NEW.project_id 
        AND target_language = NEW.target_language
        AND NEW.target_text ILIKE '%' || target_term || '%';
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_terminology_frequency_trigger 
    AFTER UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION update_terminology_frequency();

-- Function to create review notifications
CREATE OR REPLACE FUNCTION create_review_notification()
RETURNS TRIGGER AS $$
BEGIN
    -- Create notification when segment is submitted for review
    IF NEW.assigned_reviewer_id IS NOT NULL AND 
       OLD.status != 'translated' AND NEW.status = 'translated' THEN
        INSERT INTO notifications (user_id, type, title, message, data)
        VALUES (
            NEW.assigned_reviewer_id,
            'review_requested',
            'New Review Request',
            'A translation segment has been submitted for your review',
            jsonb_build_object(
                'segment_id', NEW.id, 
                'project_id', NEW.project_id,
                'translator_id', NEW.assigned_translator_id
            )
        );
    END IF;
    
    -- Create notification when review is completed
    IF OLD.status != 'approved' AND NEW.status = 'approved' AND NEW.assigned_translator_id IS NOT NULL THEN
        INSERT INTO notifications (user_id, type, title, message, data)
        VALUES (
            NEW.assigned_translator_id,
            'review_approved',
            'Translation Approved',
            'Your translation has been approved',
            jsonb_build_object(
                'segment_id', NEW.id, 
                'project_id', NEW.project_id,
                'reviewer_id', NEW.assigned_reviewer_id
            )
        );
    END IF;
    
    -- Create notification when review is rejected
    IF OLD.status != 'rejected' AND NEW.status = 'rejected' AND NEW.assigned_translator_id IS NOT NULL THEN
        INSERT INTO notifications (user_id, type, title, message, data)
        VALUES (
            NEW.assigned_translator_id,
            'review_rejected',
            'Translation Needs Revision',
            'Your translation requires revision',
            jsonb_build_object(
                'segment_id', NEW.id, 
                'project_id', NEW.project_id,
                'reviewer_id', NEW.assigned_reviewer_id
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER review_notification_trigger
    AFTER UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION create_review_notification();

-- Function to update project progress when segments change
CREATE OR REPLACE FUNCTION update_project_progress()
RETURNS TRIGGER AS $$
DECLARE
    project_uuid UUID;
BEGIN
    -- Get project ID from the segment
    IF TG_OP = 'DELETE' THEN
        project_uuid := OLD.project_id;
    ELSE
        project_uuid := NEW.project_id;
    END IF;
    
    -- Update project updated_at timestamp to trigger cache invalidation
    UPDATE projects 
    SET updated_at = NOW() 
    WHERE id = project_uuid;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER update_project_progress_trigger
    AFTER INSERT OR UPDATE OR DELETE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION update_project_progress();

-- Function to validate segment assignments
CREATE OR REPLACE FUNCTION validate_segment_assignment()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if assigned translator is a member of the project's organization
    IF NEW.assigned_translator_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM organization_members om
            JOIN projects p ON p.organization_id = om.organization_id
            WHERE p.id = NEW.project_id 
            AND om.user_id = NEW.assigned_translator_id 
            AND om.status = 'active'
            AND om.role IN ('translator', 'senior_translator', 'project_manager', 'admin')
        ) THEN
            RAISE EXCEPTION 'Assigned translator must be an active member of the project organization';
        END IF;
    END IF;
    
    -- Check if assigned reviewer is a member of the project's organization
    IF NEW.assigned_reviewer_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM organization_members om
            JOIN projects p ON p.organization_id = om.organization_id
            WHERE p.id = NEW.project_id 
            AND om.user_id = NEW.assigned_reviewer_id 
            AND om.status = 'active'
            AND om.role IN ('reviewer', 'senior_translator', 'project_manager', 'admin')
        ) THEN
            RAISE EXCEPTION 'Assigned reviewer must be an active member of the project organization';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER validate_segment_assignment_trigger
    BEFORE INSERT OR UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION validate_segment_assignment();

-- Function to automatically set word and character counts
CREATE OR REPLACE FUNCTION update_segment_counts()
RETURNS TRIGGER AS $$
BEGIN
    -- Update word count for source text
    IF NEW.source_text IS NOT NULL THEN
        NEW.word_count := array_length(string_to_array(trim(NEW.source_text), ' '), 1);
        NEW.character_count := length(NEW.source_text);
    END IF;
    
    -- Set translation timestamps
    IF OLD.target_text IS NULL AND NEW.target_text IS NOT NULL THEN
        NEW.translation_started_at := COALESCE(NEW.translation_started_at, NOW());
    END IF;
    
    IF OLD.status != 'translated' AND NEW.status = 'translated' THEN
        NEW.translation_completed_at := NOW();
    END IF;
    
    IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
        NEW.review_completed_at := NOW();
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_segment_counts_trigger
    BEFORE INSERT OR UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION update_segment_counts();

-- Function to create organization admin when organization is created
CREATE OR REPLACE FUNCTION create_organization_admin()
RETURNS TRIGGER AS $$
BEGIN
    -- Add the creator as an admin of the organization
    IF NEW.created_by IS NOT NULL THEN
        INSERT INTO organization_members (organization_id, user_id, role, status, joined_at)
        VALUES (NEW.id, NEW.created_by, 'admin', 'active', NOW())
        ON CONFLICT (organization_id, user_id) DO NOTHING;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Note: This trigger would be created after we add created_by to organizations table
-- CREATE TRIGGER create_organization_admin_trigger
--     AFTER INSERT ON organizations
--     FOR EACH ROW EXECUTE FUNCTION create_organization_admin();

-- Function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    DELETE FROM notifications 
    WHERE created_at < NOW() - INTERVAL '30 days' 
    AND read = true;
END;
$$ language 'plpgsql';

-- Function to get project statistics
CREATE OR REPLACE FUNCTION get_project_stats(project_uuid UUID)
RETURNS TABLE(
    total_segments INTEGER,
    pending_segments INTEGER,
    in_progress_segments INTEGER,
    translated_segments INTEGER,
    reviewed_segments INTEGER,
    approved_segments INTEGER,
    rejected_segments INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_segments,
        COUNT(*) FILTER (WHERE status = 'pending')::INTEGER as pending_segments,
        COUNT(*) FILTER (WHERE status = 'in_progress')::INTEGER as in_progress_segments,
        COUNT(*) FILTER (WHERE status = 'translated')::INTEGER as translated_segments,
        COUNT(*) FILTER (WHERE status = 'reviewed')::INTEGER as reviewed_segments,
        COUNT(*) FILTER (WHERE status = 'approved')::INTEGER as approved_segments,
        COUNT(*) FILTER (WHERE status = 'rejected')::INTEGER as rejected_segments
    FROM translation_segments 
    WHERE project_id = project_uuid;
END;
$$ language 'plpgsql';
