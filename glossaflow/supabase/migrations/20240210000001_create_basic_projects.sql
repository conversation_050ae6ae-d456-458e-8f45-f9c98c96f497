-- Drop existing tables if they exist to start fresh
DROP TABLE IF EXISTS project_files CASCADE;
DROP TABLE IF EXISTS project_members CASCADE;
DROP TABLE IF EXISTS projects CASCADE;

-- Create projects table
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'review', 'completed', 'on_hold')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  source_language TEXT NOT NULL,
  target_languages TEXT[] NOT NULL DEFAULT '{}',
  document_type TEXT NOT NULL DEFAULT 'general' CHECK (document_type IN ('novel', 'game', 'technical', 'marketing', 'general')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  due_date TIMESTAMP WITH TIME ZONE,
  budget DECIMAL(10,2),
  spent DECIMAL(10,2) DEFAULT 0,
  total_segments INTEGER DEFAULT 0,
  completed_segments INTEGER DEFAULT 0,
  reviewed_segments INTEGER DEFAULT 0,
  approved_segments INTEGER DEFAULT 0,
  total_chapters INTEGER DEFAULT 0,
  completed_chapters INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID
);

-- Create project_members table
CREATE TABLE project_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('translator', 'reviewer', 'project_manager', 'admin')),
  languages TEXT[] DEFAULT '{}',
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, user_id)
);

-- Create project_files table
CREATE TABLE project_files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  original_name TEXT NOT NULL,
  size BIGINT NOT NULL,
  type TEXT NOT NULL,
  url TEXT NOT NULL,
  segments INTEGER DEFAULT 0,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_priority ON projects(priority);
CREATE INDEX idx_projects_created_at ON projects(created_at);
CREATE INDEX idx_project_members_project_id ON project_members(project_id);
CREATE INDEX idx_project_members_user_id ON project_members(user_id);
CREATE INDEX idx_project_files_project_id ON project_files(project_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for projects table
DROP TRIGGER IF EXISTS update_projects_updated_at ON projects;
CREATE TRIGGER update_projects_updated_at 
  BEFORE UPDATE ON projects 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_files ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view projects they are members of" ON projects;
DROP POLICY IF EXISTS "Users can create projects" ON projects;
DROP POLICY IF EXISTS "Project managers can update projects" ON projects;
DROP POLICY IF EXISTS "Project managers can delete projects" ON projects;

-- Create RLS policies for projects
CREATE POLICY "Users can view projects they are members of" ON projects
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_members 
      WHERE project_members.project_id = projects.id 
      AND project_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Project managers can update projects" ON projects
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM project_members 
      WHERE project_members.project_id = projects.id 
      AND project_members.user_id = auth.uid()
      AND project_members.role IN ('project_manager', 'admin')
    )
  );

CREATE POLICY "Project managers can delete projects" ON projects
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM project_members 
      WHERE project_members.project_id = projects.id 
      AND project_members.user_id = auth.uid()
      AND project_members.role IN ('project_manager', 'admin')
    )
  );

-- Drop existing policies for project_members if they exist
DROP POLICY IF EXISTS "Users can view project members for projects they belong to" ON project_members;
DROP POLICY IF EXISTS "Project managers can manage project members" ON project_members;

-- Create RLS policies for project_members
CREATE POLICY "Users can view project members for projects they belong to" ON project_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_members pm 
      WHERE pm.project_id = project_members.project_id 
      AND pm.user_id = auth.uid()
    )
  );

CREATE POLICY "Project managers can manage project members" ON project_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM project_members pm 
      WHERE pm.project_id = project_members.project_id 
      AND pm.user_id = auth.uid()
      AND pm.role IN ('project_manager', 'admin')
    )
  );

-- Drop existing policies for project_files if they exist
DROP POLICY IF EXISTS "Users can view files for projects they belong to" ON project_files;
DROP POLICY IF EXISTS "Project members can upload files" ON project_files;
DROP POLICY IF EXISTS "Project managers can manage files" ON project_files;

-- Create RLS policies for project_files
CREATE POLICY "Users can view files for projects they belong to" ON project_files
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_members 
      WHERE project_members.project_id = project_files.project_id 
      AND project_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Project members can upload files" ON project_files
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM project_members 
      WHERE project_members.project_id = project_files.project_id 
      AND project_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Project managers can manage files" ON project_files
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM project_members 
      WHERE project_members.project_id = project_files.project_id 
      AND project_members.user_id = auth.uid()
      AND project_members.role IN ('project_manager', 'admin')
    )
  );

-- Insert some sample data for development (only if there are users)
DO $$
DECLARE
    sample_user_id UUID;
BEGIN
    -- Get a sample user ID
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;
    
    -- Only insert if we have a user
    IF sample_user_id IS NOT NULL THEN
        -- Insert sample projects
        INSERT INTO projects (
          name, 
          description, 
          status, 
          priority, 
          source_language, 
          target_languages,
          total_segments,
          completed_segments,
          created_by
        ) VALUES 
        (
          'Mobile App Localization',
          'Complete localization of our mobile application for Japanese market',
          'in_progress',
          'high',
          'English',
          ARRAY['Japanese', 'Korean'],
          1250,
          938,
          sample_user_id
        ),
        (
          'Website Translation',
          'Marketing website translation for European expansion',
          'review',
          'medium',
          'English',
          ARRAY['Spanish', 'French', 'German'],
          850,
          765,
          sample_user_id
        ),
        (
          'Documentation Update',
          'Technical documentation translation for product release',
          'completed',
          'low',
          'English',
          ARRAY['French'],
          450,
          450,
          sample_user_id
        );

        -- Add project members for the sample projects
        INSERT INTO project_members (project_id, user_id, role, languages)
        SELECT 
          p.id,
          sample_user_id,
          'project_manager',
          ARRAY[p.source_language]
        FROM projects p
        WHERE p.created_by = sample_user_id;
    END IF;
END $$;
