-- GlossaFlow Database Indexes
-- This migration creates performance indexes for the translation management platform

-- User and organization lookups
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_org_members_org_user ON organization_members(organization_id, user_id);
CREATE INDEX idx_org_members_user_role ON organization_members(user_id, role);
CREATE INDEX idx_org_members_status ON organization_members(status) WHERE status = 'active';

-- Project and content queries
CREATE INDEX idx_projects_org_status ON projects(organization_id, status);
CREATE INDEX idx_projects_deadline ON projects(deadline) WHERE deadline IS NOT NULL;
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_manager ON projects(project_manager_id);

-- Source documents
CREATE INDEX idx_documents_project ON source_documents(project_id);
CREATE INDEX idx_documents_upload_status ON source_documents(upload_status);

-- Translation segments (most frequently queried)
CREATE INDEX idx_segments_project_status ON translation_segments(project_id, status);
CREATE INDEX idx_segments_project_lang ON translation_segments(project_id, target_language);
CREATE INDEX idx_segments_translator ON translation_segments(assigned_translator_id) WHERE assigned_translator_id IS NOT NULL;
CREATE INDEX idx_segments_reviewer ON translation_segments(assigned_reviewer_id) WHERE assigned_reviewer_id IS NOT NULL;
CREATE INDEX idx_segments_document ON translation_segments(document_id);
CREATE INDEX idx_segments_status ON translation_segments(status);
CREATE INDEX idx_segments_updated_at ON translation_segments(updated_at);

-- Terminology searches (critical for performance)
CREATE INDEX idx_terminology_project_lang ON terminology_entries(project_id, target_language);
CREATE INDEX idx_terminology_project_status ON terminology_entries(project_id, approval_status);
CREATE INDEX idx_terminology_source_term ON terminology_entries USING gin(to_tsvector('english', source_term));
CREATE INDEX idx_terminology_target_term ON terminology_entries USING gin(to_tsvector('english', target_term));
CREATE INDEX idx_terminology_category ON terminology_entries(category);
CREATE INDEX idx_terminology_frequency ON terminology_entries(frequency DESC);
CREATE INDEX idx_terminology_last_used ON terminology_entries(last_used_at DESC) WHERE last_used_at IS NOT NULL;

-- Term usage examples
CREATE INDEX idx_term_examples_entry ON term_usage_examples(terminology_entry_id);
CREATE INDEX idx_term_examples_segment ON term_usage_examples(segment_id);

-- Collaboration features
CREATE INDEX idx_comments_segment ON comments(segment_id);
CREATE INDEX idx_comments_user ON comments(user_id);
CREATE INDEX idx_comments_parent ON comments(parent_comment_id) WHERE parent_comment_id IS NOT NULL;
CREATE INDEX idx_comments_resolved ON comments(resolved, created_at);

CREATE INDEX idx_reviews_segment_reviewer ON reviews(segment_id, reviewer_id);
CREATE INDEX idx_reviews_segment_status ON reviews(segment_id, status);
CREATE INDEX idx_reviews_reviewer ON reviews(reviewer_id);
CREATE INDEX idx_reviews_type ON reviews(review_type);

-- Notifications
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, read, created_at);
CREATE INDEX idx_notifications_type ON notifications(type);

-- Billing and subscriptions
CREATE INDEX idx_subscriptions_org ON subscriptions(organization_id);
CREATE INDEX idx_subscriptions_stripe ON subscriptions(stripe_subscription_id) WHERE stripe_subscription_id IS NOT NULL;
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_period_end ON subscriptions(current_period_end);

CREATE INDEX idx_usage_tracking_org_period ON usage_tracking(organization_id, period_start, period_end);
CREATE INDEX idx_usage_tracking_metric ON usage_tracking(metric_type);

-- Composite indexes for common query patterns
CREATE INDEX idx_segments_translator_status ON translation_segments(assigned_translator_id, status) WHERE assigned_translator_id IS NOT NULL;
CREATE INDEX idx_segments_reviewer_status ON translation_segments(assigned_reviewer_id, status) WHERE assigned_reviewer_id IS NOT NULL;
CREATE INDEX idx_projects_org_manager ON projects(organization_id, project_manager_id);
CREATE INDEX idx_terminology_project_category ON terminology_entries(project_id, category, approval_status);

-- Full-text search indexes
CREATE INDEX idx_projects_name_search ON projects USING gin(to_tsvector('english', name));
CREATE INDEX idx_projects_description_search ON projects USING gin(to_tsvector('english', description)) WHERE description IS NOT NULL;
CREATE INDEX idx_segments_source_search ON translation_segments USING gin(to_tsvector('english', source_text));
CREATE INDEX idx_segments_target_search ON translation_segments USING gin(to_tsvector('english', target_text)) WHERE target_text IS NOT NULL;
