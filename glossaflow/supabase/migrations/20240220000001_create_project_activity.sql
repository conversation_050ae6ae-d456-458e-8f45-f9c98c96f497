-- Create project activity table for tracking project events
CREATE TABLE IF NOT EXISTS project_activity (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  action_type TEXT NOT NULL CHECK (action_type IN (
    'project_created',
    'project_updated', 
    'project_deleted',
    'member_added',
    'member_removed',
    'file_uploaded',
    'file_deleted',
    'translation_started',
    'translation_completed',
    'review_completed',
    'terminology_added',
    'terminology_updated',
    'segment_assigned',
    'segment_completed',
    'comment_added',
    'status_changed'
  )),
  action_description TEXT NOT NULL,
  target_type TEXT, -- 'file', 'segment', 'terminology', 'member', etc.
  target_id UUID, -- ID of the target object
  target_name TEXT, -- Name/title of the target object
  metadata JSONB, -- Additional data about the action
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_project_activity_project_id ON project_activity(project_id);
CREATE INDEX idx_project_activity_user_id ON project_activity(user_id);
CREATE INDEX idx_project_activity_created_at ON project_activity(created_at DESC);
CREATE INDEX idx_project_activity_action_type ON project_activity(action_type);

-- Enable RLS
ALTER TABLE project_activity ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view project activity for their organization projects" ON project_activity
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p
      JOIN organization_members om ON p.organization_id = om.organization_id
      JOIN auth.users u ON om.user_id = u.id
      WHERE p.id = project_activity.project_id
      AND u.id = auth.uid()
    )
  );

CREATE POLICY "Users can insert project activity for their organization projects" ON project_activity
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects p
      JOIN organization_members om ON p.organization_id = om.organization_id
      JOIN auth.users u ON om.user_id = u.id
      WHERE p.id = project_activity.project_id
      AND u.id = auth.uid()
    )
  );

-- Function to automatically log project activity
CREATE OR REPLACE FUNCTION log_project_activity(
  p_project_id UUID,
  p_user_id UUID,
  p_action_type TEXT,
  p_action_description TEXT,
  p_target_type TEXT DEFAULT NULL,
  p_target_id UUID DEFAULT NULL,
  p_target_name TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO project_activity (
    project_id,
    user_id,
    action_type,
    action_description,
    target_type,
    target_id,
    target_name,
    metadata
  ) VALUES (
    p_project_id,
    p_user_id,
    p_action_type,
    p_action_description,
    p_target_type,
    p_target_id,
    p_target_name,
    p_metadata
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
