-- GlossaFlow Seed Data
-- This migration creates initial seed data for development and testing

-- Insert demo organization
INSERT INTO organizations (id, name, slug, industry, team_size, subscription_tier) 
VALUES (
    '00000000-0000-0000-0000-000000000001',
    'Demo Translation Agency',
    'demo-agency',
    'publishing',
    '6-20',
    'professional'
) ON CONFLICT (id) DO NOTHING;

-- Insert demo users
INSERT INTO users (id, email, name, email_verified) VALUES
    ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'Demo Admin', true),
    ('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'Project Manager', true),
    ('00000000-0000-0000-0000-000000000003', '<EMAIL>', 'Senior Translator', true),
    ('00000000-0000-0000-0000-000000000004', '<EMAIL>', 'Quality Reviewer', true),
    ('00000000-0000-0000-0000-000000000005', '<EMAIL>', 'Junior Translator', true)
ON CONFLICT (id) DO NOTHING;

-- Insert organization members
INSERT INTO organization_members (organization_id, user_id, role, status, joined_at) VALUES
    ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'admin', 'active', NOW()),
    ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'project_manager', 'active', NOW()),
    ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000003', 'senior_translator', 'active', NOW()),
    ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000004', 'reviewer', 'active', NOW()),
    ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000005', 'translator', 'active', NOW())
ON CONFLICT (organization_id, user_id) DO NOTHING;

-- Insert demo projects
INSERT INTO projects (id, organization_id, name, description, source_language, target_languages, document_type, priority, status, created_by, project_manager_id) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        'Fantasy Novel Translation',
        'Translation of a popular fantasy novel series from English to Japanese',
        'en',
        ARRAY['ja'],
        'novel',
        'high',
        'active',
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002'
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000001',
        'Game UI Localization',
        'Localization of mobile game interface and story content',
        'en',
        ARRAY['ko', 'zh'],
        'game',
        'medium',
        'active',
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002'
    ),
    (
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000001',
        'Technical Documentation',
        'Translation of software documentation and user manuals',
        'en',
        ARRAY['de', 'fr'],
        'technical',
        'low',
        'completed',
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert demo source documents
INSERT INTO source_documents (id, project_id, filename, file_path, file_size, mime_type, upload_status, segment_count, uploaded_by) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        'chapter_01.txt',
        'projects/fantasy-novel/chapter_01.txt',
        15420,
        'text/plain',
        'completed',
        45,
        '00000000-0000-0000-0000-000000000002'
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002',
        'ui_strings.json',
        'projects/game-ui/ui_strings.json',
        8750,
        'application/json',
        'completed',
        120,
        '00000000-0000-0000-0000-000000000002'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert demo translation segments
INSERT INTO translation_segments (id, project_id, document_id, segment_number, source_text, target_text, target_language, status, assigned_translator_id, assigned_reviewer_id, word_count, character_count) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        1,
        'The ancient dragon stirred in its lair, sensing the approach of the young hero.',
        '古代の竜は巣穴で身動きし、若い英雄の接近を感じ取った。',
        'ja',
        'approved',
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000004',
        13,
        78
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        2,
        'With scales that shimmered like emeralds, the beast was a sight to behold.',
        'エメラルドのように輝く鱗を持つその獣は、見る者を魅了する光景だった。',
        'ja',
        'reviewed',
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000004',
        12,
        67
    ),
    (
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        3,
        'The hero gripped his enchanted sword, its blade glowing with magical energy.',
        NULL,
        'ja',
        'in_progress',
        '00000000-0000-0000-0000-000000000005',
        '00000000-0000-0000-0000-000000000004',
        11,
        73
    ),
    (
        '00000000-0000-0000-0000-000000000004',
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002',
        1,
        'Start Game',
        '게임 시작',
        'ko',
        'approved',
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000004',
        2,
        10
    ),
    (
        '00000000-0000-0000-0000-000000000005',
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000002',
        2,
        'Settings',
        '설정',
        'ko',
        'approved',
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000004',
        1,
        8
    )
ON CONFLICT (id) DO NOTHING;

-- Insert demo terminology entries
INSERT INTO terminology_entries (id, project_id, source_term, target_term, target_language, category, context, usage_notes, approval_status, frequency, created_by) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000001',
        'dragon',
        '竜',
        'ja',
        'character',
        'Mythical creature, main antagonist',
        'Use 竜 (ryuu) instead of ドラゴン for fantasy context',
        'approved',
        5,
        '00000000-0000-0000-0000-000000000003'
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000001',
        'hero',
        '英雄',
        'ja',
        'character',
        'Main protagonist',
        'Formal term for the main character',
        'approved',
        8,
        '00000000-0000-0000-0000-000000000003'
    ),
    (
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000001',
        'enchanted sword',
        '魔法の剣',
        'ja',
        'item',
        'Magical weapon',
        'Standard translation for magical weapons',
        'approved',
        3,
        '00000000-0000-0000-0000-000000000003'
    ),
    (
        '00000000-0000-0000-0000-000000000004',
        '00000000-0000-0000-0000-000000000002',
        'Start Game',
        '게임 시작',
        'ko',
        'ui',
        'Main menu button',
        'Standard UI translation',
        'approved',
        1,
        '00000000-0000-0000-0000-000000000003'
    )
ON CONFLICT (id) DO NOTHING;

-- Insert demo comments
INSERT INTO comments (segment_id, user_id, content, comment_type) VALUES
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000004',
        'Great translation! The nuance is well captured.',
        'approval'
    ),
    (
        '00000000-0000-0000-0000-000000000003',
        '00000000-0000-0000-0000-000000000002',
        'Please ensure consistency with the terminology for "enchanted sword"',
        'suggestion'
    )
ON CONFLICT DO NOTHING;

-- Insert demo reviews
INSERT INTO reviews (segment_id, reviewer_id, review_type, status, feedback, quality_score) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000004',
        'linguistic',
        'approved',
        'Excellent translation with proper terminology usage',
        5
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        '00000000-0000-0000-0000-000000000004',
        'cultural',
        'approved',
        'Cultural adaptation is appropriate for the target audience',
        4
    )
ON CONFLICT DO NOTHING;

-- Insert demo notifications
INSERT INTO notifications (user_id, type, title, message, data) VALUES
    (
        '00000000-0000-0000-0000-000000000005',
        'assignment',
        'New Translation Assignment',
        'You have been assigned to translate segment 3 in Fantasy Novel Translation',
        '{"segment_id": "00000000-0000-0000-0000-000000000003", "project_id": "00000000-0000-0000-0000-000000000001"}'::jsonb
    ),
    (
        '00000000-0000-0000-0000-000000000003',
        'review_approved',
        'Translation Approved',
        'Your translation for segment 1 has been approved',
        '{"segment_id": "00000000-0000-0000-0000-000000000001", "project_id": "00000000-0000-0000-0000-000000000001"}'::jsonb
    )
ON CONFLICT DO NOTHING;
