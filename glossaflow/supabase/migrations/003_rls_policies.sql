-- GlossaFlow Row Level Security Policies
-- This migration enables <PERSON><PERSON> and creates security policies for all tables

-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE source_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE terminology_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE term_usage_examples ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Organization policies
CREATE POLICY "Users can view organizations they belong to" ON organizations
    FOR SELECT USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Organization admins can update their organization" ON organizations
    FOR UPDATE USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role = 'admin' AND status = 'active'
        )
    );

CREATE POLICY "Organization admins can insert organizations" ON organizations
    FOR INSERT WITH CHECK (true); -- Will be restricted by application logic

-- Organization members policies
CREATE POLICY "Users can view members of their organizations" ON organization_members
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Organization admins can manage members" ON organization_members
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role IN ('admin', 'project_manager') AND status = 'active'
        )
    );

-- Project policies
CREATE POLICY "Users can view projects in their organization" ON projects
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Project managers can manage projects" ON projects
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() 
            AND role IN ('admin', 'project_manager') 
            AND status = 'active'
        )
    );

-- Source documents policies
CREATE POLICY "Users can view documents in accessible projects" ON source_documents
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Project managers can manage documents" ON source_documents
    FOR ALL USING (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() 
            AND om.role IN ('admin', 'project_manager') 
            AND om.status = 'active'
        )
    );

-- Translation segments policies
CREATE POLICY "Users can view segments in accessible projects" ON translation_segments
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Translators can update assigned segments" ON translation_segments
    FOR UPDATE USING (
        assigned_translator_id = auth.uid() OR
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() 
            AND om.role IN ('admin', 'project_manager') 
            AND om.status = 'active'
        )
    );

CREATE POLICY "Project managers can insert segments" ON translation_segments
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() 
            AND om.role IN ('admin', 'project_manager') 
            AND om.status = 'active'
        )
    );

-- Terminology entries policies
CREATE POLICY "Users can view terminology in accessible projects" ON terminology_entries
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Translators can create terminology entries" ON terminology_entries
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() 
            AND om.role IN ('admin', 'project_manager', 'senior_translator', 'translator') 
            AND om.status = 'active'
        )
    );

CREATE POLICY "Translators can update terminology entries" ON terminology_entries
    FOR UPDATE USING (
        created_by = auth.uid() OR
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() 
            AND om.role IN ('admin', 'project_manager', 'senior_translator') 
            AND om.status = 'active'
        )
    );

-- Term usage examples policies
CREATE POLICY "Users can view term examples in accessible projects" ON term_usage_examples
    FOR SELECT USING (
        terminology_entry_id IN (
            SELECT te.id FROM terminology_entries te
            JOIN projects p ON te.project_id = p.id
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

-- Comments policies
CREATE POLICY "Users can view comments on accessible segments" ON comments
    FOR SELECT USING (
        segment_id IN (
            SELECT ts.id FROM translation_segments ts
            JOIN projects p ON ts.project_id = p.id
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Users can create comments on accessible segments" ON comments
    FOR INSERT WITH CHECK (
        segment_id IN (
            SELECT ts.id FROM translation_segments ts
            JOIN projects p ON ts.project_id = p.id
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING (user_id = auth.uid());

-- Reviews policies
CREATE POLICY "Users can view reviews on accessible segments" ON reviews
    FOR SELECT USING (
        segment_id IN (
            SELECT ts.id FROM translation_segments ts
            JOIN projects p ON ts.project_id = p.id
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Reviewers can create reviews" ON reviews
    FOR INSERT WITH CHECK (
        reviewer_id = auth.uid() AND
        segment_id IN (
            SELECT ts.id FROM translation_segments ts
            JOIN projects p ON ts.project_id = p.id
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() 
            AND om.role IN ('admin', 'project_manager', 'reviewer') 
            AND om.status = 'active'
        )
    );

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Subscriptions policies
CREATE POLICY "Organization members can view their subscription" ON subscriptions
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Organization admins can manage subscriptions" ON subscriptions
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role = 'admin' AND status = 'active'
        )
    );

-- Usage tracking policies
CREATE POLICY "Organization members can view their usage" ON usage_tracking
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );
