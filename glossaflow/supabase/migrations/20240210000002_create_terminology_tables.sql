-- Create terminology tables (check if columns exist first)
CREATE TABLE IF NOT EXISTS terminology_entries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  source_term TEXT NOT NULL,
  target_term TEXT NOT NULL,
  target_language TEXT NOT NULL,
  category TEXT NOT NULL DEFAULT 'general',
  context TEXT,
  usage_notes TEXT,
  approval_status TEXT NOT NULL DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
  frequency INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  last_used TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'terminology_entries' AND column_name = 'organization_id') THEN
    ALTER TABLE terminology_entries ADD COLUMN organization_id UUID;
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'terminology_entries' AND column_name = 'project_id') THEN
    ALTER TABLE terminology_entries ADD COLUMN project_id UUID REFERENCES projects(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create glossaries table
CREATE TABLE IF NOT EXISTS glossaries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  source_language TEXT NOT NULL,
  target_languages TEXT[] NOT NULL DEFAULT '{}',
  is_public BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  organization_id UUID
);

-- Create glossary_entries junction table
CREATE TABLE IF NOT EXISTS glossary_entries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  glossary_id UUID REFERENCES glossaries(id) ON DELETE CASCADE,
  terminology_id UUID REFERENCES terminology_entries(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  added_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  UNIQUE(glossary_id, terminology_id)
);

-- Create translation_memory table
CREATE TABLE IF NOT EXISTS translation_memory (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  source_text TEXT NOT NULL,
  target_text TEXT NOT NULL,
  source_language TEXT NOT NULL,
  target_language TEXT NOT NULL,
  context TEXT,
  quality_score DECIMAL(3,2) DEFAULT 1.0 CHECK (quality_score >= 0 AND quality_score <= 1),
  usage_count INTEGER DEFAULT 1,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  organization_id UUID
);

-- Create term_candidates table for automated term extraction
CREATE TABLE IF NOT EXISTS term_candidates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
  candidate_term TEXT NOT NULL,
  frequency INTEGER DEFAULT 1 CHECK (frequency > 0),
  confidence_score DECIMAL(3,2) DEFAULT 0.5 CHECK (confidence_score >= 0 AND confidence_score <= 1),
  context_sentence TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  assigned_category TEXT,
  extraction_metadata JSONB DEFAULT '{}',
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  organization_id UUID,
  UNIQUE(project_id, candidate_term)
);

-- Create terminology_usage_log table for tracking usage
CREATE TABLE IF NOT EXISTS terminology_usage_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  terminology_id UUID REFERENCES terminology_entries(id) ON DELETE CASCADE,
  used_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  context TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_terminology_entries_source_term ON terminology_entries(source_term);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_target_term ON terminology_entries(target_term);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_target_language ON terminology_entries(target_language);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_category ON terminology_entries(category);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_approval_status ON terminology_entries(approval_status);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_created_by ON terminology_entries(created_by);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_project_id ON terminology_entries(project_id);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_organization_id ON terminology_entries(organization_id);
CREATE INDEX IF NOT EXISTS idx_terminology_entries_created_at ON terminology_entries(created_at);

CREATE INDEX IF NOT EXISTS idx_glossaries_created_by ON glossaries(created_by);
CREATE INDEX IF NOT EXISTS idx_glossaries_organization_id ON glossaries(organization_id);
CREATE INDEX IF NOT EXISTS idx_glossary_entries_glossary_id ON glossary_entries(glossary_id);
CREATE INDEX IF NOT EXISTS idx_glossary_entries_terminology_id ON glossary_entries(terminology_id);

CREATE INDEX IF NOT EXISTS idx_translation_memory_source_language ON translation_memory(source_language);
CREATE INDEX IF NOT EXISTS idx_translation_memory_target_language ON translation_memory(target_language);
CREATE INDEX IF NOT EXISTS idx_translation_memory_source_text ON translation_memory USING gin(to_tsvector('english', source_text));
CREATE INDEX IF NOT EXISTS idx_translation_memory_target_text ON translation_memory USING gin(to_tsvector('english', target_text));
CREATE INDEX IF NOT EXISTS idx_translation_memory_project_id ON translation_memory(project_id);
CREATE INDEX IF NOT EXISTS idx_translation_memory_organization_id ON translation_memory(organization_id);

CREATE INDEX IF NOT EXISTS idx_terminology_usage_log_terminology_id ON terminology_usage_log(terminology_id);
CREATE INDEX IF NOT EXISTS idx_terminology_usage_log_used_at ON terminology_usage_log(used_at);

-- Create indexes for term_candidates table
CREATE INDEX IF NOT EXISTS idx_term_candidates_project_id ON term_candidates(project_id);
CREATE INDEX IF NOT EXISTS idx_term_candidates_status ON term_candidates(status);
CREATE INDEX IF NOT EXISTS idx_term_candidates_confidence_score ON term_candidates(confidence_score DESC);
CREATE INDEX IF NOT EXISTS idx_term_candidates_frequency ON term_candidates(frequency DESC);
CREATE INDEX IF NOT EXISTS idx_term_candidates_candidate_term ON term_candidates(candidate_term);
CREATE INDEX IF NOT EXISTS idx_term_candidates_assigned_category ON term_candidates(assigned_category);
CREATE INDEX IF NOT EXISTS idx_term_candidates_created_by ON term_candidates(created_by);
CREATE INDEX IF NOT EXISTS idx_term_candidates_organization_id ON term_candidates(organization_id);
CREATE INDEX IF NOT EXISTS idx_term_candidates_created_at ON term_candidates(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_term_candidates_text_search ON term_candidates USING gin(to_tsvector('english', candidate_term || ' ' || COALESCE(context_sentence, '')));

-- Create updated_at trigger functions
CREATE TRIGGER update_terminology_entries_updated_at 
  BEFORE UPDATE ON terminology_entries 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_glossaries_updated_at 
  BEFORE UPDATE ON glossaries 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_translation_memory_updated_at
  BEFORE UPDATE ON translation_memory
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_term_candidates_updated_at
  BEFORE UPDATE ON term_candidates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE terminology_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE glossaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE glossary_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_memory ENABLE ROW LEVEL SECURITY;
ALTER TABLE terminology_usage_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE term_candidates ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for terminology_entries
CREATE POLICY "Users can view terminology entries in their organization" ON terminology_entries
  FOR SELECT USING (
    organization_id IS NULL OR 
    organization_id IN (
      SELECT organization_id FROM auth.users WHERE id = auth.uid()
    ) OR
    created_by = auth.uid()
  );

CREATE POLICY "Users can create terminology entries" ON terminology_entries
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own terminology entries" ON terminology_entries
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM project_members pm
      WHERE pm.project_id = terminology_entries.project_id
      AND pm.user_id = auth.uid()
      AND pm.role IN ('project_manager', 'admin')
    )
  );

CREATE POLICY "Users can delete their own terminology entries" ON terminology_entries
  FOR DELETE USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM project_members pm
      WHERE pm.project_id = terminology_entries.project_id
      AND pm.user_id = auth.uid()
      AND pm.role IN ('project_manager', 'admin')
    )
  );

-- Create RLS policies for glossaries
CREATE POLICY "Users can view public glossaries or their own" ON glossaries
  FOR SELECT USING (
    is_public = true OR
    created_by = auth.uid() OR
    organization_id IN (
      SELECT organization_id FROM auth.users WHERE id = auth.uid()
    )
  );

CREATE POLICY "Users can create glossaries" ON glossaries
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own glossaries" ON glossaries
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Users can delete their own glossaries" ON glossaries
  FOR DELETE USING (created_by = auth.uid());

-- Create RLS policies for glossary_entries
CREATE POLICY "Users can view glossary entries for accessible glossaries" ON glossary_entries
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM glossaries g
      WHERE g.id = glossary_entries.glossary_id
      AND (
        g.is_public = true OR
        g.created_by = auth.uid() OR
        g.organization_id IN (
          SELECT organization_id FROM auth.users WHERE id = auth.uid()
        )
      )
    )
  );

CREATE POLICY "Users can manage glossary entries for their glossaries" ON glossary_entries
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM glossaries g
      WHERE g.id = glossary_entries.glossary_id
      AND g.created_by = auth.uid()
    )
  );

-- Create RLS policies for translation_memory
CREATE POLICY "Users can view translation memory in their organization" ON translation_memory
  FOR SELECT USING (
    organization_id IS NULL OR
    organization_id IN (
      SELECT organization_id FROM auth.users WHERE id = auth.uid()
    ) OR
    created_by = auth.uid()
  );

CREATE POLICY "Users can create translation memory entries" ON translation_memory
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own translation memory entries" ON translation_memory
  FOR UPDATE USING (created_by = auth.uid());

-- Create RLS policies for terminology_usage_log
CREATE POLICY "Users can view terminology usage for accessible entries" ON terminology_usage_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM terminology_entries te
      WHERE te.id = terminology_usage_log.terminology_id
      AND (
        te.organization_id IS NULL OR
        te.organization_id IN (
          SELECT organization_id FROM auth.users WHERE id = auth.uid()
        ) OR
        te.created_by = auth.uid()
      )
    )
  );

CREATE POLICY "Users can log terminology usage" ON terminology_usage_log
  FOR INSERT WITH CHECK (auth.uid() = used_by);

-- Create RLS policies for term_candidates
CREATE POLICY "Users can view term candidates for accessible projects" ON term_candidates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = term_candidates.project_id
      AND (
        p.organization_id IS NULL OR
        p.organization_id IN (
          SELECT organization_id FROM auth.users WHERE id = auth.uid()
        ) OR
        p.created_by = auth.uid()
      )
    )
  );

CREATE POLICY "Users can create term candidates for accessible projects" ON term_candidates
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = term_candidates.project_id
      AND (
        p.organization_id IS NULL OR
        p.organization_id IN (
          SELECT organization_id FROM auth.users WHERE id = auth.uid()
        ) OR
        p.created_by = auth.uid()
      )
    )
  );

CREATE POLICY "Users can update term candidates for accessible projects" ON term_candidates
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = term_candidates.project_id
      AND (
        p.organization_id IS NULL OR
        p.organization_id IN (
          SELECT organization_id FROM auth.users WHERE id = auth.uid()
        ) OR
        p.created_by = auth.uid()
      )
    )
  );

CREATE POLICY "Users can delete term candidates for accessible projects" ON term_candidates
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = term_candidates.project_id
      AND (
        p.organization_id IS NULL OR
        p.organization_id IN (
          SELECT organization_id FROM auth.users WHERE id = auth.uid()
        ) OR
        p.created_by = auth.uid()
      )
    )
  );

-- Insert sample terminology data
DO $$
DECLARE
    sample_user_id UUID;
BEGIN
    -- Get a sample user ID
    SELECT id INTO sample_user_id FROM auth.users LIMIT 1;
    
    -- Only insert if we have a user
    IF sample_user_id IS NOT NULL THEN
        -- Insert sample terminology entries
        INSERT INTO terminology_entries (
          source_term,
          target_term,
          target_language,
          category,
          context,
          usage_notes,
          approval_status,
          frequency,
          created_by
        ) VALUES 
        (
          'User Interface',
          'ユーザーインターフェース',
          'Japanese',
          'technical',
          'Software development context',
          'Use this term for all UI-related content',
          'approved',
          45,
          sample_user_id
        ),
        (
          'Dashboard',
          'ダッシュボード',
          'Japanese',
          'technical',
          'Main application interface',
          'Preferred over control panel',
          'approved',
          32,
          sample_user_id
        ),
        (
          'Settings',
          '設定',
          'Japanese',
          'general',
          'Configuration options',
          'Standard translation for settings',
          'pending',
          28,
          sample_user_id
        ),
        (
          'Project',
          'プロジェクト',
          'Japanese',
          'general',
          'Translation project context',
          'Use katakana form',
          'approved',
          67,
          sample_user_id
        );

        -- Insert sample glossary
        INSERT INTO glossaries (
          name,
          description,
          source_language,
          target_languages,
          is_public,
          created_by
        ) VALUES (
          'Technical Terms',
          'Common technical terminology for software applications',
          'English',
          ARRAY['Japanese', 'Korean'],
          true,
          sample_user_id
        );

        -- Link terminology to glossary
        INSERT INTO glossary_entries (glossary_id, terminology_id, added_by)
        SELECT 
          g.id,
          te.id,
          sample_user_id
        FROM glossaries g
        CROSS JOIN terminology_entries te
        WHERE g.name = 'Technical Terms'
        AND te.category = 'technical'
        AND te.created_by = sample_user_id;
    END IF;
END $$;
