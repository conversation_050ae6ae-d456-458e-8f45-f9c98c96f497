-- Create chapters table for organizing translation segments
CREATE TABLE chapters (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
  chapter_number INTEGER NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  source_word_count INTEGER DEFAULT 0,
  target_word_count INTEGER DEFAULT 0,
  estimated_cost DECIMAL(10,2) DEFAULT 0, -- Credits required for AI translation
  actual_cost DECIMAL(10,2) DEFAULT 0, -- Actual credits spent
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'translated', 'reviewed', 'approved')),
  progress_percentage DECIMAL(5,2) DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  total_segments INTEGER DEFAULT 0,
  completed_segments INTEGER DEFAULT 0,
  reviewed_segments INTEGER DEFAULT 0,
  approved_segments INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, chapter_number)
);

-- Create credits table for tracking user/organization credits
CREATE TABLE credits (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  balance DECIMAL(10,2) DEFAULT 0 CHECK (balance >= 0),
  total_purchased DECIMAL(10,2) DEFAULT 0,
  total_spent DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(organization_id)
);

-- Create credit_transactions table for tracking credit usage
CREATE TABLE credit_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL,
  segment_id UUID REFERENCES translation_segments(id) ON DELETE SET NULL,
  transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('purchase', 'ai_translation', 'refund', 'bonus')),
  amount DECIMAL(10,2) NOT NULL, -- Positive for credits added, negative for credits spent
  description TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add chapter_id to translation_segments table
ALTER TABLE translation_segments 
ADD COLUMN chapter_id UUID REFERENCES chapters(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX idx_chapters_project_id ON chapters(project_id);
CREATE INDEX idx_chapters_status ON chapters(status);
CREATE INDEX idx_chapters_project_number ON chapters(project_id, chapter_number);

CREATE INDEX idx_credits_organization_id ON credits(organization_id);

CREATE INDEX idx_credit_transactions_organization_id ON credit_transactions(organization_id);
CREATE INDEX idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX idx_credit_transactions_project_id ON credit_transactions(project_id);
CREATE INDEX idx_credit_transactions_chapter_id ON credit_transactions(chapter_id);
CREATE INDEX idx_credit_transactions_type ON credit_transactions(transaction_type);
CREATE INDEX idx_credit_transactions_created_at ON credit_transactions(created_at);

CREATE INDEX idx_translation_segments_chapter_id ON translation_segments(chapter_id);

-- Create function to update chapter progress
CREATE OR REPLACE FUNCTION update_chapter_progress()
RETURNS TRIGGER AS $$
BEGIN
  -- Update chapter progress when segment status changes
  IF NEW.chapter_id IS NOT NULL THEN
    UPDATE chapters 
    SET 
      completed_segments = (
        SELECT COUNT(*) 
        FROM translation_segments 
        WHERE chapter_id = NEW.chapter_id 
        AND status IN ('translated', 'reviewed', 'approved')
      ),
      reviewed_segments = (
        SELECT COUNT(*) 
        FROM translation_segments 
        WHERE chapter_id = NEW.chapter_id 
        AND status IN ('reviewed', 'approved')
      ),
      approved_segments = (
        SELECT COUNT(*) 
        FROM translation_segments 
        WHERE chapter_id = NEW.chapter_id 
        AND status = 'approved'
      ),
      progress_percentage = CASE 
        WHEN total_segments > 0 THEN 
          ROUND((
            SELECT COUNT(*) 
            FROM translation_segments 
            WHERE chapter_id = NEW.chapter_id 
            AND status IN ('translated', 'reviewed', 'approved')
          )::DECIMAL / total_segments * 100, 2)
        ELSE 0
      END,
      updated_at = NOW()
    WHERE id = NEW.chapter_id;
    
    -- Update chapter status based on progress
    UPDATE chapters 
    SET status = CASE 
      WHEN progress_percentage = 100 THEN 'translated'
      WHEN progress_percentage > 0 THEN 'in_progress'
      ELSE 'pending'
    END
    WHERE id = NEW.chapter_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for chapter progress updates
CREATE TRIGGER trigger_update_chapter_progress
  AFTER INSERT OR UPDATE OF status ON translation_segments
  FOR EACH ROW
  EXECUTE FUNCTION update_chapter_progress();

-- Create function to update project chapter counts
CREATE OR REPLACE FUNCTION update_project_chapter_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update project chapter counts when chapter status changes
  UPDATE projects 
  SET 
    total_chapters = (
      SELECT COUNT(*) 
      FROM chapters 
      WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
    ),
    completed_chapters = (
      SELECT COUNT(*) 
      FROM chapters 
      WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
      AND status IN ('translated', 'reviewed', 'approved')
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.project_id, OLD.project_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for project chapter count updates
CREATE TRIGGER trigger_update_project_chapter_counts
  AFTER INSERT OR UPDATE OF status OR DELETE ON chapters
  FOR EACH ROW
  EXECUTE FUNCTION update_project_chapter_counts();

-- Create function to update credit balance
CREATE OR REPLACE FUNCTION update_credit_balance()
RETURNS TRIGGER AS $$
BEGIN
  -- Update credit balance when transaction is added
  INSERT INTO credits (organization_id, balance, total_purchased, total_spent)
  VALUES (
    NEW.organization_id,
    NEW.amount,
    CASE WHEN NEW.amount > 0 THEN NEW.amount ELSE 0 END,
    CASE WHEN NEW.amount < 0 THEN ABS(NEW.amount) ELSE 0 END
  )
  ON CONFLICT (organization_id) 
  DO UPDATE SET
    balance = credits.balance + NEW.amount,
    total_purchased = credits.total_purchased + CASE WHEN NEW.amount > 0 THEN NEW.amount ELSE 0 END,
    total_spent = credits.total_spent + CASE WHEN NEW.amount < 0 THEN ABS(NEW.amount) ELSE 0 END,
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for credit balance updates
CREATE TRIGGER trigger_update_credit_balance
  AFTER INSERT ON credit_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_credit_balance();

-- Insert default credit records for existing organizations
INSERT INTO credits (organization_id, balance, total_purchased, total_spent)
SELECT id, 100.00, 100.00, 0 -- Give 100 free credits to start
FROM organizations
ON CONFLICT (organization_id) DO NOTHING;
