import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Gemini AI Integration
interface GeminiConfig {
  apiKey: string;
  baseUrl?: string;
  model?: string;
}

interface ExtractedTerm {
  term: string;
  definition: string;
  confidence: number;
  category: string;
  context: string;
}

class GeminiAIService {
  private config: GeminiConfig;
  private baseUrl: string;

  constructor(config: GeminiConfig) {
    this.config = {
      model: 'gemini-2.0-flash',
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      ...config,
    };
    this.baseUrl = this.config.baseUrl!;
  }

  async extractTerminology(text: string, sourceLanguage: string, maxTerms: number = 20): Promise<ExtractedTerm[]> {
    const prompt = this.buildTermExtractionPrompt(text, sourceLanguage, maxTerms);

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseTermExtractionResponse(response);
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error(`Failed to extract terminology: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildTermExtractionPrompt(text: string, sourceLanguage: string, maxTerms: number): string {
    return `You are a professional terminology extraction expert. Extract the most important technical terms, concepts, and domain-specific vocabulary from the following text.

**Instructions:**
1. Extract up to ${maxTerms} terms that are:
   - Technical or domain-specific
   - Important for understanding the content
   - Likely to need consistent translation
   - Not common everyday words

2. For each term, provide:
   - The exact term as it appears
   - A clear, concise definition
   - Confidence score (0.0-1.0)
   - Category (technical, business, legal, medical, etc.)
   - Context sentence where it appears

3. Focus on:
   - Compound terms and phrases
   - Technical jargon
   - Proper nouns and brand names
   - Industry-specific concepts
   - Acronyms and abbreviations

**Source Language:** ${sourceLanguage}

**Text to analyze:**
${text}

**Response Format (JSON):**
{
  "terms": [
    {
      "term": "exact term",
      "definition": "clear definition",
      "confidence": 0.95,
      "category": "technical",
      "context": "sentence containing the term"
    }
  ]
}

Respond only with valid JSON.`;
  }

  private async callGeminiAPI(prompt: string): Promise<string> {
    const url = `${this.baseUrl}/models/${this.config.model}:generateContent`;

    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': this.config.apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Invalid response format from Gemini API');
    }

    return data.candidates[0].content.parts[0].text;
  }

  private parseTermExtractionResponse(response: string): ExtractedTerm[] {
    try {
      const parsed = JSON.parse(response);

      if (!parsed.terms || !Array.isArray(parsed.terms)) {
        throw new Error('Invalid response format: missing terms array');
      }

      return parsed.terms.map((term: any) => ({
        term: term.term || '',
        definition: term.definition || '',
        confidence: Math.min(Math.max(term.confidence || 0.5, 0), 1),
        category: term.category || 'general',
        context: term.context || '',
      }));
    } catch (error) {
      console.error('Failed to parse Gemini response:', response);
      throw new Error('Failed to parse AI response');
    }
  }
}

// Types for term extraction
interface TermCandidate {
  candidate_term: string;
  frequency: number;
  confidence_score: number;
  context_sentence: string;
  extraction_metadata: {
    tfidf_score: number;
    pattern_type: string;
    pos_tags: string[];
    sentence_position: number;
  };
}

interface ExtractionRequest {
  project_id: string;
  source_text: string;
  source_language: string;
  user_id: string;
  organization_id?: string;
}

// TF-IDF calculation utilities
class TFIDFCalculator {
  private documents: string[] = [];
  private vocabulary: Set<string> = new Set();
  private termFrequency: Map<string, Map<string, number>> = new Map();
  private documentFrequency: Map<string, number> = new Map();

  constructor(documents: string[]) {
    this.documents = documents;
    this.buildVocabulary();
    this.calculateTermFrequencies();
    this.calculateDocumentFrequencies();
  }

  private buildVocabulary() {
    this.documents.forEach(doc => {
      const terms = this.tokenize(doc);
      terms.forEach(term => this.vocabulary.add(term));
    });
  }

  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2 && !this.isStopWord(term));
  }

  private isStopWord(term: string): boolean {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your',
      'his', 'her', 'its', 'our', 'their', 'from', 'up', 'about', 'into', 'through', 'during',
      'before', 'after', 'above', 'below', 'between', 'among', 'all', 'any', 'both', 'each',
      'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same',
      'so', 'than', 'too', 'very', 'just', 'now', 'here', 'there', 'when', 'where', 'why', 'how'
    ]);
    return stopWords.has(term);
  }

  private calculateTermFrequencies() {
    this.documents.forEach((doc, docIndex) => {
      const terms = this.tokenize(doc);
      const termCount = new Map<string, number>();
      
      terms.forEach(term => {
        termCount.set(term, (termCount.get(term) || 0) + 1);
      });

      this.termFrequency.set(docIndex.toString(), termCount);
    });
  }

  private calculateDocumentFrequencies() {
    this.vocabulary.forEach(term => {
      let docCount = 0;
      this.documents.forEach((doc, docIndex) => {
        const termCount = this.termFrequency.get(docIndex.toString());
        if (termCount && termCount.has(term)) {
          docCount++;
        }
      });
      this.documentFrequency.set(term, docCount);
    });
  }

  calculateTFIDF(term: string, docIndex: number): number {
    const termCount = this.termFrequency.get(docIndex.toString());
    if (!termCount || !termCount.has(term)) return 0;

    const tf = termCount.get(term)! / Array.from(termCount.values()).reduce((a, b) => a + b, 0);
    const df = this.documentFrequency.get(term) || 0;
    const idf = df > 0 ? Math.log(this.documents.length / df) : 0;

    return tf * idf;
  }

  getTopTerms(docIndex: number, limit: number = 50): Array<{term: string, score: number}> {
    const scores: Array<{term: string, score: number}> = [];
    
    this.vocabulary.forEach(term => {
      const score = this.calculateTFIDF(term, docIndex);
      if (score > 0) {
        scores.push({ term, score });
      }
    });

    return scores
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }
}

// Linguistic pattern extraction
class LinguisticPatternExtractor {
  private patterns = [
    // Adjective + Noun patterns
    { regex: /\b([A-Z][a-z]+)\s+([A-Z][a-z]+)\b/g, type: 'proper_noun_phrase' },
    { regex: /\b([a-z]+(?:ive|al|ic|ous|ful|less|able))\s+([a-z]+(?:tion|sion|ment|ness|ity|ism))\b/gi, type: 'adjective_noun' },
    { regex: /\b([a-z]+(?:ing|ed))\s+([a-z]+(?:er|or|ist|ian))\b/gi, type: 'participle_agent' },
    
    // Compound technical terms
    { regex: /\b([a-z]+)\s+([a-z]+)\s+(system|process|method|approach|technique|algorithm|framework|model|protocol)\b/gi, type: 'technical_compound' },
    { regex: /\b([a-z]+)\s+(interface|component|module|service|engine|manager|controller|handler)\b/gi, type: 'software_term' },
    
    // Domain-specific patterns
    { regex: /\b([a-z]+)\s+(analysis|evaluation|assessment|measurement|calculation|estimation)\b/gi, type: 'analytical_term' },
    { regex: /\b(data|information|content|knowledge)\s+([a-z]+(?:ing|ment|tion|sion))\b/gi, type: 'data_term' }
  ];

  extractPatterns(text: string): Array<{term: string, type: string, context: string}> {
    const results: Array<{term: string, type: string, context: string}> = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    sentences.forEach(sentence => {
      this.patterns.forEach(pattern => {
        let match;
        while ((match = pattern.regex.exec(sentence)) !== null) {
          const term = match[0].trim();
          if (term.length > 3 && term.length < 50) {
            results.push({
              term: term.toLowerCase(),
              type: pattern.type,
              context: sentence.trim()
            });
          }
        }
        pattern.regex.lastIndex = 0; // Reset regex
      });
    });

    return results;
  }
}

// Main extraction function with AI integration
async function extractTermCandidates(request: ExtractionRequest): Promise<TermCandidate[]> {
  const { source_text, project_id, source_language } = request;

  // Split text into sentences for context
  const sentences = source_text.split(/[.!?]+/).filter(s => s.trim().length > 10);

  // Initialize extractors
  const tfidfCalculator = new TFIDFCalculator([source_text]);
  const patternExtractor = new LinguisticPatternExtractor();

  // Combine and score candidates
  const candidateMap = new Map<string, TermCandidate>();

  // Try AI extraction first (if API key is available)
  const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
  let aiTerms: ExtractedTerm[] = [];

  if (geminiApiKey && source_text.length > 100) {
    try {
      console.log('Using Gemini AI for term extraction...');
      const geminiService = new GeminiAIService({ apiKey: geminiApiKey });
      aiTerms = await geminiService.extractTerminology(source_text, source_language, 30);
      console.log(`Gemini AI extracted ${aiTerms.length} terms`);
    } catch (error) {
      console.warn('AI extraction failed, falling back to traditional methods:', error);
    }
  }

  // Process AI-extracted terms (highest priority)
  aiTerms.forEach((aiTerm) => {
    const frequency = (source_text.toLowerCase().match(new RegExp(`\\b${aiTerm.term.toLowerCase()}\\b`, 'g')) || []).length;
    const contextSentence = aiTerm.context || sentences.find(s => s.toLowerCase().includes(aiTerm.term.toLowerCase())) || '';

    candidateMap.set(aiTerm.term.toLowerCase(), {
      candidate_term: aiTerm.term,
      frequency,
      confidence_score: aiTerm.confidence * 0.95, // AI terms get high confidence
      context_sentence: contextSentence.substring(0, 500),
      extraction_metadata: {
        tfidf_score: 0,
        pattern_type: `ai_${aiTerm.category}`,
        pos_tags: [],
        sentence_position: sentences.findIndex(s => s.toLowerCase().includes(aiTerm.term.toLowerCase())),
        ai_definition: aiTerm.definition,
        ai_category: aiTerm.category
      }
    });
  });

  // Get TF-IDF terms (fallback and supplement)
  const tfidfTerms = tfidfCalculator.getTopTerms(0, 50);

  // Get pattern-based terms
  const patternTerms = patternExtractor.extractPatterns(source_text);
  
  // Process TF-IDF terms (supplement AI results)
  tfidfTerms.forEach(({ term, score }) => {
    const existing = candidateMap.get(term.toLowerCase());

    if (existing) {
      // Boost confidence for terms found by both AI and TF-IDF
      existing.confidence_score = Math.min(existing.confidence_score + 0.2, 1.0);
      existing.extraction_metadata.tfidf_score = score;
      existing.extraction_metadata.pattern_type = `${existing.extraction_metadata.pattern_type},tfidf`;
    } else {
      // Add TF-IDF terms not found by AI
      const frequency = (source_text.toLowerCase().match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
      const contextSentence = sentences.find(s => s.toLowerCase().includes(term)) || '';

      candidateMap.set(term.toLowerCase(), {
        candidate_term: term,
        frequency,
        confidence_score: Math.min(score * 0.6, 0.8), // Lower confidence for non-AI terms
        context_sentence: contextSentence.substring(0, 500),
        extraction_metadata: {
          tfidf_score: score,
          pattern_type: 'tfidf',
          pos_tags: [],
          sentence_position: sentences.findIndex(s => s.toLowerCase().includes(term))
        }
      });
    }
  });

  // Process pattern-based terms (supplement AI results)
  patternTerms.forEach(({ term, type, context }) => {
    const existing = candidateMap.get(term.toLowerCase());

    if (existing) {
      // Boost confidence for terms found by multiple methods
      existing.confidence_score = Math.min(existing.confidence_score + 0.15, 1.0);
      existing.extraction_metadata.pattern_type = `${existing.extraction_metadata.pattern_type},${type}`;
    } else {
      // Add pattern terms not found by AI or TF-IDF
      const frequency = (source_text.toLowerCase().match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;

      candidateMap.set(term.toLowerCase(), {
        candidate_term: term,
        frequency,
        confidence_score: 0.5, // Base confidence for pattern-only matches
        context_sentence: context.substring(0, 500),
        extraction_metadata: {
          tfidf_score: 0,
          pattern_type: type,
          pos_tags: [],
          sentence_position: sentences.findIndex(s => s.toLowerCase().includes(term))
        }
      });
    }
  });
  
  // Filter and sort candidates with AI-aware logic
  return Array.from(candidateMap.values())
    .filter(candidate => {
      const isAITerm = candidate.extraction_metadata.pattern_type?.includes('ai_');
      const minFrequency = isAITerm ? 1 : 2; // AI terms can have lower frequency
      const minConfidence = isAITerm ? 0.3 : 0.4; // AI terms can have lower confidence threshold

      return candidate.frequency >= minFrequency &&
             candidate.confidence_score >= minConfidence &&
             candidate.candidate_term.length >= 3 &&
             candidate.candidate_term.length <= 50 &&
             !candidate.candidate_term.match(/^\d+$/) && // Exclude pure numbers
             !candidate.candidate_term.match(/^[a-z]{1,2}$/) // Exclude single/double letters
    })
    .sort((a, b) => {
      // Prioritize AI terms, then by confidence score
      const aIsAI = a.extraction_metadata.pattern_type?.includes('ai_') ? 1 : 0;
      const bIsAI = b.extraction_metadata.pattern_type?.includes('ai_') ? 1 : 0;

      if (aIsAI !== bIsAI) {
        return bIsAI - aIsAI; // AI terms first
      }

      return b.confidence_score - a.confidence_score;
    })
    .slice(0, 60); // Increased limit for better AI results
}

serve(async (req) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { project_id, source_text, source_language, user_id, organization_id } = await req.json() as ExtractionRequest;

    if (!project_id || !source_text || !user_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: project_id, source_text, user_id' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify project access
    const { data: project, error: projectError } = await supabaseClient
      .from('projects')
      .select('id, organization_id')
      .eq('id', project_id)
      .single();

    if (projectError || !project) {
      return new Response(
        JSON.stringify({ error: 'Project not found or access denied' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Extract term candidates
    const candidates = await extractTermCandidates({
      project_id,
      source_text,
      source_language: source_language || 'en',
      user_id,
      organization_id: organization_id || project.organization_id
    });

    // Insert candidates into database
    const candidateInserts = candidates.map(candidate => ({
      project_id,
      candidate_term: candidate.candidate_term,
      frequency: candidate.frequency,
      confidence_score: candidate.confidence_score,
      context_sentence: candidate.context_sentence,
      extraction_metadata: candidate.extraction_metadata,
      created_by: user_id,
      organization_id: organization_id || project.organization_id
    }));

    const { data: insertedCandidates, error: insertError } = await supabaseClient
      .from('term_candidates')
      .upsert(candidateInserts, { 
        onConflict: 'project_id,candidate_term',
        ignoreDuplicates: false 
      })
      .select();

    if (insertError) {
      console.error('Insert error:', insertError);
      return new Response(
        JSON.stringify({ error: 'Failed to save term candidates', details: insertError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        candidates_extracted: candidates.length,
        candidates_saved: insertedCandidates?.length || 0,
        candidates: insertedCandidates 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Function error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
