# Term Candidate Extraction System

## Overview

The Term Candidate Extraction system is a comprehensive AI-powered feature that automatically analyzes source documents and suggests terminology for approval before translation begins. This proactive terminology management dramatically improves translation accuracy by building comprehensive glossaries upfront.

## Features

### 🤖 AI-Powered Extraction
- **Hybrid TF-IDF + Linguistic Pattern Analysis**: Combines statistical analysis with linguistic pattern matching
- **Confidence Scoring**: Each candidate receives a confidence score based on multiple factors
- **Context Preservation**: Maintains original context sentences for better understanding
- **Multi-language Support**: Configurable for different source languages

### 📊 Smart Filtering
- **Frequency Analysis**: Prioritizes terms that appear multiple times
- **Pattern Recognition**: Identifies technical compounds, proper nouns, and domain-specific terms
- **Stop Word Filtering**: Automatically excludes common words
- **Quality Thresholds**: Filters out low-confidence candidates

### 🎯 Review Interface
- **Sortable Table**: Review candidates by confidence, frequency, or creation date
- **Batch Operations**: Approve or reject multiple candidates efficiently
- **Category Assignment**: Pre-assign categories before approval
- **Context Display**: View usage context for informed decisions

### 🔄 Seamless Integration
- **Project Integration**: Extract terms directly from project documents
- **Terminology Database**: Approved terms automatically added to terminology entries
- **Glossary Management**: Optional automatic glossary creation
- **Workflow Integration**: Fits naturally into existing translation workflows

## Architecture

### Database Schema

```sql
-- Term candidates table
CREATE TABLE term_candidates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
  candidate_term TEXT NOT NULL,
  frequency INTEGER DEFAULT 1 CHECK (frequency > 0),
  confidence_score DECIMAL(3,2) DEFAULT 0.5 CHECK (confidence_score >= 0 AND confidence_score <= 1),
  context_sentence TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  assigned_category TEXT,
  extraction_metadata JSONB DEFAULT '{}',
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  organization_id UUID,
  UNIQUE(project_id, candidate_term)
);
```

### API Endpoints

#### Core Operations
- `GET /api/term-candidates` - List candidates with filtering and pagination
- `POST /api/term-candidates` - Create new candidate manually
- `GET /api/term-candidates/[id]` - Get specific candidate
- `PUT /api/term-candidates/[id]` - Update candidate status/category
- `DELETE /api/term-candidates/[id]` - Delete candidate

#### Extraction & Actions
- `POST /api/term-candidates/extract` - Trigger extraction from text
- `GET /api/term-candidates/extract?projectId=X` - Get extraction status
- `POST /api/term-candidates/[id]/approve` - Approve and create terminology entry
- `POST /api/term-candidates/[id]/reject` - Reject with optional reason

#### Analytics
- `GET /api/term-candidates/stats` - Get statistics and analytics

### Supabase Edge Function

The extraction engine runs as a Supabase Edge Function (`extract-term-candidates`) that:

1. **Validates Input**: Checks project access and text quality
2. **Processes Text**: Applies TF-IDF and pattern analysis
3. **Scores Candidates**: Combines multiple scoring methods
4. **Filters Results**: Removes low-quality candidates
5. **Stores Data**: Saves candidates to database with metadata

## Usage Guide

### 1. Extracting Term Candidates

#### From Project Interface
```typescript
// Trigger extraction from project page
const extractionResult = await extractTermCandidates({
  projectId: 'project-123',
  sourceText: documentContent,
  sourceLanguage: 'en',
  clearExisting: false
});
```

#### From Term Suggestions Page
1. Navigate to **Dashboard > Term Suggestions**
2. Click **Extract Terms** button
3. Select project and input method (text or file upload)
4. Configure extraction options
5. Click **Extract Terms**

### 2. Reviewing Candidates

#### Filtering and Sorting
- **Status Filter**: View pending, approved, or rejected candidates
- **Search**: Find specific terms or contexts
- **Sort Options**: By confidence score, frequency, or date
- **Project Filter**: Focus on specific projects

#### Actions Available
- **Approve**: Opens detailed form to create terminology entry
- **Reject**: Mark as rejected with optional reason
- **Assign Category**: Pre-categorize before approval
- **Delete**: Remove candidate permanently

### 3. Approving Candidates

When approving a candidate:

1. **Review Information**: Check term, frequency, and confidence
2. **Provide Translation**: Enter target term and language
3. **Set Category**: Choose appropriate terminology category
4. **Add Context**: Enhance or modify context information
5. **Usage Notes**: Add special instructions
6. **Glossary Option**: Optionally add to project glossary

### 4. Integration Points

#### Project Management
- **TermExtractionWidget**: Shows extraction status and quick actions
- **Document Upload**: Trigger extraction on new documents
- **Project Dashboard**: View pending candidates count

#### Terminology Management
- **TermCandidatesWidget**: Display pending candidates in terminology interface
- **TermEditor**: Enhanced to support candidate-based creation
- **Glossary Integration**: Automatic glossary population

## Configuration

### Extraction Parameters

```typescript
interface ExtractionConfig {
  // Minimum term frequency to consider
  minFrequency: number; // default: 2
  
  // Minimum confidence score threshold
  minConfidence: number; // default: 0.4
  
  // Maximum number of candidates to return
  maxCandidates: number; // default: 50
  
  // TF-IDF weight in final score
  tfidfWeight: number; // default: 0.7
  
  // Pattern matching boost
  patternBoost: number; // default: 0.3
}
```

### Pattern Types

The system recognizes several linguistic patterns:

- **proper_noun_phrase**: "Google Cloud Platform"
- **adjective_noun**: "machine learning"
- **technical_compound**: "neural network algorithm"
- **software_term**: "user interface component"
- **analytical_term**: "data analysis"
- **data_term**: "information processing"

## Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run specific test suites
npm run test:api          # API endpoint tests
npm run test:extraction   # Extraction algorithm tests
npm run test:components   # React component tests
npm run test:integration  # End-to-end workflow tests

# Run with coverage
npm run test:coverage
```

### Test Coverage

- **API Routes**: Complete CRUD operations and error handling
- **Extraction Algorithm**: TF-IDF calculation and pattern matching
- **React Components**: User interactions and state management
- **Integration**: Full workflow from extraction to approval

## Performance Considerations

### Optimization Strategies

1. **Database Indexing**: Optimized indexes for common queries
2. **Pagination**: Efficient pagination for large candidate sets
3. **Caching**: RTK Query caching for frequently accessed data
4. **Batch Operations**: Support for bulk approve/reject actions

### Scalability

- **Edge Function**: Serverless extraction scales automatically
- **Database**: Partitioning by project for large datasets
- **Frontend**: Virtual scrolling for large candidate lists
- **API**: Rate limiting and request optimization

## Monitoring and Analytics

### Key Metrics

- **Extraction Success Rate**: Percentage of successful extractions
- **Approval Rate**: Ratio of approved to total candidates
- **Confidence Accuracy**: Correlation between confidence and approval
- **Processing Time**: Average extraction duration
- **User Engagement**: Review and approval activity

### Dashboard Analytics

- **Overview Stats**: Total, pending, approved, rejected counts
- **Confidence Distribution**: High/medium/low confidence breakdown
- **Frequency Analysis**: Term occurrence patterns
- **Category Distribution**: Terms by category
- **Project Activity**: Extraction activity by project

## Troubleshooting

### Common Issues

1. **Low Extraction Quality**
   - Check source text quality and length
   - Verify language settings
   - Review confidence thresholds

2. **Performance Issues**
   - Monitor Edge Function execution time
   - Check database query performance
   - Optimize text preprocessing

3. **Integration Problems**
   - Verify API authentication
   - Check project permissions
   - Validate database connections

### Debug Mode

Enable debug logging in development:

```typescript
// In extraction function
console.log('Extraction metadata:', {
  documentLength: sourceText.length,
  vocabularySize: vocabulary.size,
  candidatesFound: candidates.length,
  processingTime: Date.now() - startTime
});
```

## Future Enhancements

### Planned Features

- **Machine Learning Models**: Advanced NLP models for better extraction
- **Multi-document Analysis**: Cross-document term correlation
- **Translation Memory Integration**: Leverage existing translations
- **Custom Pattern Definition**: User-defined extraction patterns
- **Collaborative Review**: Team-based candidate review workflows
- **API Integrations**: Connect with external terminology databases

### Roadmap

- **Q1 2024**: Enhanced ML models and pattern recognition
- **Q2 2024**: Multi-document analysis and correlation
- **Q3 2024**: Advanced analytics and reporting
- **Q4 2024**: API integrations and enterprise features
