const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createSampleProject() {
  try {
    console.log('Creating sample project...');

    // First, get or create a sample user from auth.users
    const { data: authUsers } = await supabase.auth.admin.listUsers();

    let sampleUserId = null;
    if (authUsers?.users?.length > 0) {
      sampleUserId = authUsers.users[0].id;
      console.log('Using existing auth user:', authUsers.users[0].email);
    } else {
      console.log('No auth users found. Please create a user through the auth system first.');
      return;
    }

    // Create a sample project (using only basic columns)
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name: 'Sample Translation Project',
        description: 'A sample project for testing the GlossaFlow platform',
        status: 'in_progress',
        priority: 'medium',
        source_language: 'English',
        target_languages: ['Japanese', 'Spanish'],
        total_segments: 100,
        completed_segments: 25,
        created_by: sampleUserId
      })
      .select()
      .single();

    if (projectError) {
      console.error('Error creating project:', projectError);
      return;
    }

    console.log('Created project:', project.name, 'with ID:', project.id);

    // Add user as project member
    const { error: memberError } = await supabase
      .from('project_members')
      .insert({
        project_id: project.id,
        user_id: sampleUserId,
        role: 'project_manager'
      });

    if (memberError) {
      console.error('Error adding project member:', memberError);
    } else {
      console.log('Added user as project member');
    }

    // Create sample chapters
    const chapters = [
      {
        project_id: project.id,
        chapter_number: 1,
        title: 'Introduction',
        description: 'The opening chapter',
        source_word_count: 1500,
        estimated_cost: 15,
        status: 'in_progress',
        progress_percentage: 60,
        total_segments: 30,
        completed_segments: 18,
        reviewed_segments: 10,
        approved_segments: 8
      },
      {
        project_id: project.id,
        chapter_number: 2,
        title: 'The Journey Begins',
        description: 'Our heroes start their adventure',
        source_word_count: 2200,
        estimated_cost: 22,
        status: 'pending',
        progress_percentage: 0,
        total_segments: 44,
        completed_segments: 0,
        reviewed_segments: 0,
        approved_segments: 0
      },
      {
        project_id: project.id,
        chapter_number: 3,
        title: 'First Challenge',
        description: 'The first obstacle appears',
        source_word_count: 1800,
        estimated_cost: 18,
        status: 'pending',
        progress_percentage: 0,
        total_segments: 36,
        completed_segments: 0,
        reviewed_segments: 0,
        approved_segments: 0
      }
    ];

    const { data: createdChapters, error: chaptersError } = await supabase
      .from('chapters')
      .insert(chapters)
      .select();

    if (chaptersError) {
      console.error('Error creating chapters:', chaptersError);
    } else {
      console.log('Created', createdChapters.length, 'chapters');
    }

    // Create some sample terminology
    const terminology = [
      {
        project_id: project.id,
        source_term: 'magic sword',
        target_term: '魔法の剣',
        target_language: 'Japanese',
        category: 'weapons',
        context: 'A legendary weapon with mystical powers',
        approval_status: 'approved',
        created_by: sampleUserId
      },
      {
        project_id: project.id,
        source_term: 'ancient castle',
        target_term: '古城',
        target_language: 'Japanese',
        category: 'locations',
        context: 'An old fortress from medieval times',
        approval_status: 'approved',
        created_by: sampleUserId
      },
      {
        project_id: project.id,
        source_term: 'hero',
        target_term: 'héroe',
        target_language: 'Spanish',
        category: 'characters',
        context: 'The main protagonist of the story',
        approval_status: 'pending',
        created_by: sampleUserId
      }
    ];

    const { data: createdTerms, error: termsError } = await supabase
      .from('terminology_entries')
      .insert(terminology)
      .select();

    if (termsError) {
      console.error('Error creating terminology:', termsError);
    } else {
      console.log('Created', createdTerms.length, 'terminology entries');
    }

    console.log('\n✅ Sample project created successfully!');
    console.log('Project ID:', project.id);
    console.log('You can now test the project details page at:');
    console.log(`http://localhost:4000/dashboard/projects/${project.id}`);

  } catch (error) {
    console.error('Error creating sample project:', error);
  }
}

createSampleProject();
