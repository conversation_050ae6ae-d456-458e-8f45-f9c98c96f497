#!/bin/bash

# GlossaFlow Supabase Setup Script
# Run this after completing Supabase CLI authentication

set -e

echo "🚀 Setting up Supabase for GlossaFlow"
echo "====================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if logged in
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please run: supabase login"
    exit 1
fi

print_status "Supabase CLI authenticated"

# Create project
echo
print_info "Creating Supabase project..."
echo "Project name suggestions: glossaflow-prod, glossaflow-dev, glossaflow-main"
read -p "Enter project name: " PROJECT_NAME

echo "Database password requirements: min 8 characters, include letters and numbers"
read -p "Enter database password: " -s DB_PASSWORD
echo

print_info "Creating project: $PROJECT_NAME"
supabase projects create "$PROJECT_NAME" --db-password "$DB_PASSWORD" --region us-east-1

# Get project reference
print_info "Getting project details..."
PROJECT_REF=$(supabase projects list | grep "$PROJECT_NAME" | awk '{print $1}')

if [ -z "$PROJECT_REF" ]; then
    echo "❌ Could not find project reference. Please check project creation."
    exit 1
fi

print_status "Project created with reference: $PROJECT_REF"

# Initialize local Supabase
print_info "Initializing local Supabase..."
supabase init

# Link project
print_info "Linking to project..."
supabase link --project-ref "$PROJECT_REF"

# Deploy migrations
print_info "Deploying database schema..."
supabase db push

# Get API keys
print_info "Getting API keys..."
supabase projects api-keys --project-ref "$PROJECT_REF" > temp_keys.txt

PROJECT_URL="https://$PROJECT_REF.supabase.co"
ANON_KEY=$(grep "anon" temp_keys.txt | awk '{print $2}')
SERVICE_KEY=$(grep "service_role" temp_keys.txt | awk '{print $2}')

rm temp_keys.txt

# Update environment file
print_info "Updating environment variables..."

# Create backup of existing .env.local
if [ -f .env.local ]; then
    cp .env.local .env.local.backup
fi

# Update Supabase configuration
sed -i '' "s|NEXT_PUBLIC_SUPABASE_URL=.*|NEXT_PUBLIC_SUPABASE_URL=$PROJECT_URL|" .env.local
sed -i '' "s|NEXT_PUBLIC_SUPABASE_ANON_KEY=.*|NEXT_PUBLIC_SUPABASE_ANON_KEY=$ANON_KEY|" .env.local
sed -i '' "s|SUPABASE_SERVICE_ROLE_KEY=.*|SUPABASE_SERVICE_ROLE_KEY=$SERVICE_KEY|" .env.local

print_status "Supabase setup complete!"
echo
echo "Project Details:"
echo "- URL: $PROJECT_URL"
echo "- Reference: $PROJECT_REF"
echo "- Environment variables updated in .env.local"
echo
print_warning "Next steps:"
echo "1. Configure authentication providers in Supabase dashboard"
echo "2. Set up Google Cloud OAuth credentials"
echo "3. Test the authentication flow"
echo
echo "Supabase Dashboard: https://supabase.com/dashboard/project/$PROJECT_REF"
