#!/bin/bash

# GlossaFlow Complete Backend Setup Script
# Orchestrates the complete backend infrastructure setup

set -e

echo "🚀 GlossaFlow Complete Backend Infrastructure Setup"
echo "=================================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI not found. Install with: npm install -g supabase"
        exit 1
    fi
    
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud CLI not found. Install from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    if ! command -v gsutil &> /dev/null; then
        print_error "gsutil not found. Install Google Cloud SDK."
        exit 1
    fi
    
    print_status "All prerequisites available"
}

# Generate NextAuth secret
generate_nextauth_secret() {
    print_info "Generating NextAuth secret..."
    
    if command -v openssl &> /dev/null; then
        NEXTAUTH_SECRET=$(openssl rand -base64 32)
    else
        # Fallback for systems without openssl
        NEXTAUTH_SECRET=$(head -c 32 /dev/urandom | base64)
    fi
    
    # Update environment file
    sed -i '' "s|NEXTAUTH_SECRET=.*|NEXTAUTH_SECRET=$NEXTAUTH_SECRET|" .env.local
    
    print_status "NextAuth secret generated"
}

# Main setup flow
main() {
    echo
    print_info "This script will set up the complete backend infrastructure for GlossaFlow:"
    echo "1. Supabase project with database schema"
    echo "2. Google Cloud OAuth credentials"
    echo "3. Google Cloud Storage bucket"
    echo "4. Environment variable configuration"
    echo
    
    read -p "Continue with setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
    
    check_prerequisites
    
    # Step 1: Supabase Setup
    echo
    print_info "=== STEP 1: Supabase Setup ==="
    
    if ! supabase projects list &> /dev/null; then
        print_warning "Supabase authentication required."
        echo "Please run: supabase login"
        echo "Then run this script again."
        exit 1
    fi
    
    print_info "Running Supabase setup..."
    ./scripts/setup-supabase.sh
    
    # Step 2: Google Cloud Setup
    echo
    print_info "=== STEP 2: Google Cloud Setup ==="
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        print_warning "Google Cloud authentication required."
        echo "Please run: gcloud auth login"
        echo "Then run this script again."
        exit 1
    fi
    
    print_info "Running Google Cloud setup..."
    ./scripts/setup-google-cloud.sh
    
    # Step 3: Generate secrets
    echo
    print_info "=== STEP 3: Generate Secrets ==="
    generate_nextauth_secret
    
    # Step 4: Final configuration
    echo
    print_info "=== STEP 4: Final Configuration ==="
    
    # Add .gitignore entries for security
    if ! grep -q "storage-service-account.json" .gitignore 2>/dev/null; then
        echo "" >> .gitignore
        echo "# Google Cloud Service Account Key" >> .gitignore
        echo "storage-service-account.json" >> .gitignore
        print_status "Added service account key to .gitignore"
    fi
    
    # Create environment backup
    if [ -f .env.local ]; then
        cp .env.local .env.local.$(date +%Y%m%d_%H%M%S)
        print_status "Created backup of environment file"
    fi
    
    echo
    print_status "🎉 Backend infrastructure setup complete!"
    echo
    print_info "Summary of what was created:"
    echo "✅ Supabase project with database schema deployed"
    echo "✅ Google Cloud OAuth credentials configured"
    echo "✅ Google Cloud Storage bucket created"
    echo "✅ Service account for storage access"
    echo "✅ Environment variables updated"
    echo "✅ NextAuth secret generated"
    echo
    print_warning "Manual steps still required:"
    echo "1. Configure Google OAuth in Supabase dashboard"
    echo "2. Set up OAuth consent screen (if not done)"
    echo "3. Test authentication flow"
    echo
    print_info "Next steps:"
    echo "1. Start the development server: npm run dev"
    echo "2. Visit http://localhost:4000"
    echo "3. Test Google OAuth login"
    echo "4. Verify database operations"
    echo
    print_warning "Security reminders:"
    echo "- Keep storage-service-account.json secure"
    echo "- Never commit .env.local to version control"
    echo "- Regularly rotate API keys and secrets"
}

# Make sure we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "supabase" ]; then
    print_error "Please run this script from the GlossaFlow project root directory"
    exit 1
fi

# Make sure other scripts are executable
chmod +x scripts/setup-supabase.sh
chmod +x scripts/setup-google-cloud.sh

# Run main function
main "$@"
