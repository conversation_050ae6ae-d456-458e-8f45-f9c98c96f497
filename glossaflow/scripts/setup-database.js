#!/usr/bin/env node

/**
 * GlossaFlow Database Setup Script
 * 
 * This script sets up the database schema by running all migrations
 * in the correct order.
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: path.join(process.cwd(), '.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const MIGRATIONS_DIR = path.join(process.cwd(), 'supabase', 'migrations');

async function runMigration(filename) {
  const filePath = path.join(MIGRATIONS_DIR, filename);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  Migration file not found: ${filename}`);
    return false;
  }
  
  console.log(`📄 Running migration: ${filename}`);
  
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement + ';' });
        
        if (error) {
          // Try direct query if RPC fails
          const { error: directError } = await supabase
            .from('_temp')
            .select('*')
            .limit(0); // This will fail but allows us to execute raw SQL
          
          if (directError) {
            console.error(`❌ Error executing statement: ${statement.substring(0, 100)}...`);
            console.error(error);
            return false;
          }
        }
      }
    }
    
    console.log(`✅ Migration completed: ${filename}`);
    return true;
  } catch (error) {
    console.error(`❌ Error running migration ${filename}:`, error.message);
    return false;
  }
}

async function setupDatabase() {
  console.log('🚀 Setting up GlossaFlow database...\n');
  
  // Check connection
  console.log('🔍 Testing database connection...');
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      process.exit(1);
    }
    
    console.log('✅ Database connection successful\n');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
  
  // Create migrations directory if it doesn't exist
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    console.log('📁 Creating migrations directory...');
    fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
  }
  
  // List of migrations in order
  const migrations = [
    '001_initial_schema.sql',
    '002_indexes.sql',
    '003_rls_policies.sql',
    '004_triggers_functions.sql',
    '005_seed_data.sql'
  ];
  
  console.log('📋 Running migrations...\n');
  
  let successCount = 0;
  
  for (const migration of migrations) {
    const success = await runMigration(migration);
    if (success) {
      successCount++;
    } else {
      console.error(`❌ Migration failed: ${migration}`);
      console.error('Stopping migration process.');
      break;
    }
  }
  
  console.log(`\n📊 Migration Summary:`);
  console.log(`✅ Successful: ${successCount}/${migrations.length}`);
  
  if (successCount === migrations.length) {
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. 🚀 Start the development server: npm run dev');
    console.log('2. 🌐 Open http://localhost:3000 in your browser');
    console.log('3. 👤 Sign in with demo credentials:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Password: (set up in Supabase Auth)');
  } else {
    console.log('\n❌ Database setup incomplete. Please check the errors above.');
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function executeSQLDirect(sql) {
  try {
    // This is a workaround since Supabase client doesn't expose raw SQL execution
    // In a real setup, you would use the Supabase CLI or direct PostgreSQL connection
    console.log('⚠️  Note: For production setup, use Supabase CLI to run migrations:');
    console.log('   supabase db reset');
    console.log('   supabase db push');
    
    return true;
  } catch (error) {
    console.error('Error executing SQL:', error);
    return false;
  }
}

async function checkMigrationFiles() {
  console.log('🔍 Checking migration files...\n');
  
  const migrations = [
    '001_initial_schema.sql',
    '002_indexes.sql', 
    '003_rls_policies.sql',
    '004_triggers_functions.sql',
    '005_seed_data.sql'
  ];
  
  let allFilesExist = true;
  
  for (const migration of migrations) {
    const filePath = path.join(MIGRATIONS_DIR, migration);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${migration}`);
    } else {
      console.log(`❌ ${migration} - Missing`);
      allFilesExist = false;
    }
  }
  
  if (!allFilesExist) {
    console.log('\n❌ Some migration files are missing. Please ensure all migration files are present.');
    return false;
  }
  
  console.log('\n✅ All migration files found!');
  return true;
}

async function main() {
  const filesExist = await checkMigrationFiles();
  
  if (!filesExist) {
    process.exit(1);
  }
  
  console.log('\n📝 Database Setup Instructions:');
  console.log('');
  console.log('For the most reliable setup, use the Supabase CLI:');
  console.log('');
  console.log('1. Install Supabase CLI: npm install -g supabase');
  console.log('2. Login to Supabase: supabase login');
  console.log('3. Link your project: supabase link --project-ref YOUR_PROJECT_ID');
  console.log('4. Run migrations: supabase db push');
  console.log('');
  console.log('Alternatively, you can copy and paste the SQL from each migration file');
  console.log('into the Supabase SQL Editor in the following order:');
  console.log('');
  console.log('1. 001_initial_schema.sql');
  console.log('2. 002_indexes.sql');
  console.log('3. 003_rls_policies.sql');
  console.log('4. 004_triggers_functions.sql');
  console.log('5. 005_seed_data.sql');
  console.log('');
  console.log('Migration files are located in: supabase/migrations/');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { setupDatabase, runMigration };
