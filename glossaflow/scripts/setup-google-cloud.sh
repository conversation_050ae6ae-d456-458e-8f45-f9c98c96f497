#!/bin/bash

# GlossaFlow Google Cloud Setup Script
# Sets up OAuth and Cloud Storage

set -e

echo "🚀 Setting up Google Cloud for GlossaFlow"
echo "========================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if logged in to gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
    print_error "Not logged in to Google Cloud. Please run: gcloud auth login"
    exit 1
fi

# Get current project
GCP_PROJECT=$(gcloud config get-value project)
print_status "Using Google Cloud project: $GCP_PROJECT"

# Enable required APIs
print_info "Enabling required Google Cloud APIs..."
gcloud services enable oauth2.googleapis.com
gcloud services enable iamcredentials.googleapis.com
gcloud services enable storage.googleapis.com

print_status "APIs enabled"

# Setup OAuth (manual steps)
echo
print_warning "OAuth Consent Screen Setup Required:"
echo "1. Go to: https://console.cloud.google.com/apis/credentials/consent?project=$GCP_PROJECT"
echo "2. Choose 'External' user type"
echo "3. Fill in app details:"
echo "   - App name: GlossaFlow"
echo "   - User support email: your email"
echo "   - Developer contact: your email"
echo "4. Add authorized domain: localhost"
echo "5. Save and continue"
echo
read -p "Press Enter when OAuth consent screen is configured..."

echo
print_warning "OAuth Client Creation Required:"
echo "1. Go to: https://console.cloud.google.com/apis/credentials?project=$GCP_PROJECT"
echo "2. Click 'Create Credentials' → 'OAuth 2.0 Client IDs'"
echo "3. Application type: Web application"
echo "4. Name: GlossaFlow Web Client"
echo "5. Authorized redirect URIs:"
echo "   - http://localhost:4000/api/auth/callback/google"
echo "   - https://YOUR_SUPABASE_REF.supabase.co/auth/v1/callback"
echo "6. Copy the Client ID and Client Secret"
echo
read -p "Enter Google OAuth Client ID: " GOOGLE_CLIENT_ID
read -p "Enter Google OAuth Client Secret: " GOOGLE_CLIENT_SECRET

# Setup Cloud Storage
echo
print_info "Setting up Google Cloud Storage..."

# Create unique bucket name
BUCKET_NAME="glossaflow-storage-$(date +%s)"
print_info "Creating storage bucket: $BUCKET_NAME"

# Create bucket
gsutil mb -p "$GCP_PROJECT" -l us-east1 "gs://$BUCKET_NAME"

# Set bucket permissions for public read
print_info "Configuring bucket permissions..."
gsutil iam ch allUsers:objectViewer "gs://$BUCKET_NAME"

# Configure CORS
print_info "Setting up CORS configuration..."
cat > cors.json << EOF
[
  {
    "origin": ["http://localhost:4000", "https://*.supabase.co"],
    "method": ["GET", "POST", "PUT", "DELETE", "HEAD"],
    "responseHeader": ["Content-Type", "Authorization"],
    "maxAgeSeconds": 3600
  }
]
EOF

gsutil cors set cors.json "gs://$BUCKET_NAME"
rm cors.json

# Create service account
print_info "Creating service account..."
SERVICE_ACCOUNT_NAME="glossaflow-storage"
SERVICE_ACCOUNT_EMAIL="$SERVICE_ACCOUNT_NAME@$GCP_PROJECT.iam.gserviceaccount.com"

gcloud iam service-accounts create "$SERVICE_ACCOUNT_NAME" \
    --display-name="GlossaFlow Storage Service Account" \
    --description="Service account for GlossaFlow file storage operations"

# Grant storage admin role
print_info "Granting storage permissions..."
gcloud projects add-iam-policy-binding "$GCP_PROJECT" \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/storage.admin"

# Create service account key
print_info "Creating service account key..."
gcloud iam service-accounts keys create "./storage-service-account.json" \
    --iam-account="$SERVICE_ACCOUNT_EMAIL"

# Update environment variables
print_info "Updating environment variables..."

# Update Google OAuth credentials
sed -i '' "s|GOOGLE_CLIENT_ID=.*|GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID|" .env.local
sed -i '' "s|GOOGLE_CLIENT_SECRET=.*|GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET|" .env.local

# Add Google Cloud Storage configuration
cat >> .env.local << EOF

# Google Cloud Storage Configuration (Generated)
GCS_BUCKET_NAME=$BUCKET_NAME
GCS_PROJECT_ID=$GCP_PROJECT
GCS_SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL
GCS_SERVICE_ACCOUNT_KEY_PATH=./storage-service-account.json
EOF

print_status "Google Cloud setup complete!"
echo
echo "Configuration Summary:"
echo "- OAuth Client ID: $GOOGLE_CLIENT_ID"
echo "- Storage Bucket: $BUCKET_NAME"
echo "- Service Account: $SERVICE_ACCOUNT_EMAIL"
echo "- Service Account Key: ./storage-service-account.json"
echo
print_warning "Important Security Notes:"
echo "1. Keep storage-service-account.json secure"
echo "2. Add storage-service-account.json to .gitignore"
echo "3. Never commit service account keys to version control"
echo
print_warning "Next Steps:"
echo "1. Configure Google OAuth in Supabase dashboard"
echo "2. Test authentication flow"
echo "3. Test file upload functionality"
