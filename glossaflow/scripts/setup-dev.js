#!/usr/bin/env node

/**
 * GlossaFlow Development Environment Setup Script
 * 
 * This script helps set up the development environment by:
 * 1. Checking for required environment variables
 * 2. Validating API connections
 * 3. Setting up initial configuration
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const ENV_FILE = path.join(process.cwd(), '.env.local');
const ENV_EXAMPLE = path.join(process.cwd(), '.env.local.example');

console.log('🚀 GlossaFlow Development Environment Setup\n');

// Check if .env.local exists
if (!fs.existsSync(ENV_FILE)) {
  console.log('📝 Creating .env.local file...');
  
  if (fs.existsSync(ENV_EXAMPLE)) {
    // Copy from example
    fs.copyFileSync(ENV_EXAMPLE, ENV_FILE);
    console.log('✅ Created .env.local from .env.local.example');
  } else {
    // Create basic template
    const envTemplate = `# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=${crypto.randomBytes(32).toString('base64')}

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Redis Configuration (Optional)
UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Email Configuration (Optional)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
`;
    
    fs.writeFileSync(ENV_FILE, envTemplate);
    console.log('✅ Created .env.local with template');
  }
  
  console.log('\n⚠️  Please edit .env.local with your actual values before continuing.\n');
}

// Load environment variables
require('dotenv').config({ path: ENV_FILE });

// Required environment variables
const requiredVars = [
  'NEXTAUTH_SECRET',
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
];

// Optional but recommended variables
const recommendedVars = [
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'GITHUB_CLIENT_ID',
  'GITHUB_CLIENT_SECRET'
];

console.log('🔍 Checking environment variables...\n');

let missingRequired = [];
let missingRecommended = [];

// Check required variables
requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (!value || value.startsWith('your-') || value === 'sk_test_...' || value === 'pk_test_...') {
    missingRequired.push(varName);
    console.log(`❌ ${varName}: Missing or placeholder value`);
  } else {
    console.log(`✅ ${varName}: Configured`);
  }
});

// Check recommended variables
recommendedVars.forEach(varName => {
  const value = process.env[varName];
  if (!value || value.startsWith('your-') || value === 'sk_test_...' || value === 'pk_test_...') {
    missingRecommended.push(varName);
    console.log(`⚠️  ${varName}: Missing or placeholder value (recommended)`);
  } else {
    console.log(`✅ ${varName}: Configured`);
  }
});

console.log('\n📋 Setup Summary:\n');

if (missingRequired.length === 0) {
  console.log('✅ All required environment variables are configured!');
} else {
  console.log('❌ Missing required environment variables:');
  missingRequired.forEach(varName => {
    console.log(`   - ${varName}`);
  });
}

if (missingRecommended.length > 0) {
  console.log('\n⚠️  Missing recommended environment variables:');
  missingRecommended.forEach(varName => {
    console.log(`   - ${varName}`);
  });
}

// Provide next steps
console.log('\n📚 Next Steps:\n');

if (missingRequired.length > 0) {
  console.log('1. 🔧 Configure missing required environment variables in .env.local');
  console.log('2. 📖 Follow the setup guide: docs/SETUP_GUIDE.md');
  console.log('3. 🔄 Run this script again to verify configuration');
} else {
  console.log('1. 🗄️  Set up the database schema: npm run db:setup');
  console.log('2. 🚀 Start the development server: npm run dev');
  console.log('3. 🌐 Open http://localhost:3000 in your browser');
}

if (missingRecommended.length > 0) {
  console.log('\n💡 For full functionality, configure the recommended variables:');
  console.log('   - Stripe keys for payment processing');
  console.log('   - OAuth providers for social authentication');
}

console.log('\n📖 For detailed setup instructions, see: docs/SETUP_GUIDE.md');
console.log('🆘 Need help? Check the troubleshooting section in the setup guide.\n');

// Generate NextAuth secret if missing
if (!process.env.NEXTAUTH_SECRET || process.env.NEXTAUTH_SECRET === 'your-secret-key-here') {
  const newSecret = crypto.randomBytes(32).toString('base64');
  console.log('🔐 Generated new NextAuth secret:');
  console.log(`   NEXTAUTH_SECRET=${newSecret}`);
  console.log('   Add this to your .env.local file\n');
}

process.exit(missingRequired.length > 0 ? 1 : 0);
