#!/bin/bash

# GlossaFlow Backend Infrastructure Setup Script
# This script sets up Supabase, Google Cloud OAuth, and Google Cloud Storage

set -e  # Exit on any error

echo "🚀 GlossaFlow Backend Infrastructure Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if required CLI tools are installed
check_prerequisites() {
    echo
    print_info "Checking prerequisites..."
    
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI is not installed. Please install it first:"
        echo "npm install -g supabase"
        exit 1
    fi
    
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud CLI is not installed. Please install it first:"
        echo "https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    print_status "All prerequisites are installed"
}

# Setup Supabase project
setup_supabase() {
    echo
    print_info "Setting up Supabase project..."
    
    # Check if already logged in
    if ! supabase projects list &> /dev/null; then
        print_warning "Please log in to Supabase first:"
        echo "supabase login"
        echo "Then run this script again."
        exit 1
    fi
    
    # Create new project
    read -p "Enter your Supabase project name (e.g., glossaflow-prod): " PROJECT_NAME
    read -p "Enter your database password (min 8 characters): " -s DB_PASSWORD
    echo
    
    print_info "Creating Supabase project: $PROJECT_NAME"
    PROJECT_REF=$(supabase projects create "$PROJECT_NAME" --db-password "$DB_PASSWORD" --region us-east-1 | grep "Created project" | awk '{print $3}')
    
    if [ -z "$PROJECT_REF" ]; then
        print_error "Failed to create Supabase project"
        exit 1
    fi
    
    print_status "Created Supabase project with reference: $PROJECT_REF"
    
    # Initialize local Supabase
    print_info "Initializing local Supabase configuration..."
    supabase init
    
    # Link to the created project
    print_info "Linking to Supabase project..."
    supabase link --project-ref "$PROJECT_REF"
    
    # Deploy migrations
    print_info "Deploying database migrations..."
    supabase db push
    
    # Get project details
    PROJECT_URL="https://$PROJECT_REF.supabase.co"
    ANON_KEY=$(supabase projects api-keys --project-ref "$PROJECT_REF" | grep "anon" | awk '{print $2}')
    SERVICE_KEY=$(supabase projects api-keys --project-ref "$PROJECT_REF" | grep "service_role" | awk '{print $2}')
    
    print_status "Supabase project setup complete!"
    echo "Project URL: $PROJECT_URL"
    echo "Project Reference: $PROJECT_REF"
    
    # Save to environment file
    cat >> .env.local << EOF

# Supabase Configuration (Generated)
NEXT_PUBLIC_SUPABASE_URL=$PROJECT_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=$ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=$SERVICE_KEY
EOF
}

# Setup Google Cloud OAuth
setup_google_oauth() {
    echo
    print_info "Setting up Google Cloud OAuth..."
    
    # Get current project
    GCP_PROJECT=$(gcloud config get-value project)
    print_info "Using Google Cloud project: $GCP_PROJECT"
    
    # Enable required APIs
    print_info "Enabling required Google Cloud APIs..."
    gcloud services enable iamcredentials.googleapis.com
    gcloud services enable oauth2.googleapis.com
    
    # Create OAuth consent screen (this needs to be done manually)
    print_warning "OAuth consent screen setup required:"
    echo "1. Go to: https://console.cloud.google.com/apis/credentials/consent?project=$GCP_PROJECT"
    echo "2. Configure OAuth consent screen with your app details"
    echo "3. Add authorized domains: localhost"
    echo "4. Press Enter when done..."
    read -p ""
    
    # Create OAuth 2.0 client
    print_info "Creating OAuth 2.0 client credentials..."
    
    # Create the OAuth client
    OAUTH_CLIENT=$(gcloud auth application-default print-access-token | \
        curl -s -X POST \
        -H "Authorization: Bearer $(gcloud auth application-default print-access-token)" \
        -H "Content-Type: application/json" \
        -d '{
            "client_id": "glossaflow-'$(date +%s)'",
            "client_secret": "",
            "redirect_uris": [
                "http://localhost:4000/api/auth/callback/google",
                "https://'$PROJECT_REF'.supabase.co/auth/v1/callback"
            ],
            "javascript_origins": [
                "http://localhost:4000"
            ]
        }' \
        "https://oauth2.googleapis.com/v1/projects/$GCP_PROJECT/clients")
    
    print_warning "Manual OAuth setup required:"
    echo "1. Go to: https://console.cloud.google.com/apis/credentials?project=$GCP_PROJECT"
    echo "2. Click 'Create Credentials' > 'OAuth 2.0 Client IDs'"
    echo "3. Application type: Web application"
    echo "4. Name: GlossaFlow"
    echo "5. Authorized redirect URIs:"
    echo "   - http://localhost:4000/api/auth/callback/google"
    echo "   - https://$PROJECT_REF.supabase.co/auth/v1/callback"
    echo "6. Copy the Client ID and Client Secret"
    echo
    
    read -p "Enter Google OAuth Client ID: " GOOGLE_CLIENT_ID
    read -p "Enter Google OAuth Client Secret: " GOOGLE_CLIENT_SECRET
    
    # Save to environment file
    cat >> .env.local << EOF

# Google OAuth Configuration (Generated)
GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET
EOF
    
    print_status "Google OAuth setup complete!"
}

# Setup Google Cloud Storage
setup_google_storage() {
    echo
    print_info "Setting up Google Cloud Storage..."
    
    GCP_PROJECT=$(gcloud config get-value project)
    BUCKET_NAME="glossaflow-storage-$(date +%s)"
    
    # Create storage bucket
    print_info "Creating storage bucket: $BUCKET_NAME"
    gsutil mb -p "$GCP_PROJECT" -l us-east1 "gs://$BUCKET_NAME"
    
    # Set bucket permissions
    print_info "Configuring bucket permissions..."
    gsutil iam ch allUsers:objectViewer "gs://$BUCKET_NAME"
    
    # Create service account
    print_info "Creating service account for storage access..."
    SERVICE_ACCOUNT_NAME="glossaflow-storage"
    SERVICE_ACCOUNT_EMAIL="$SERVICE_ACCOUNT_NAME@$GCP_PROJECT.iam.gserviceaccount.com"
    
    gcloud iam service-accounts create "$SERVICE_ACCOUNT_NAME" \
        --display-name="GlossaFlow Storage Service Account"
    
    # Grant storage permissions
    gcloud projects add-iam-policy-binding "$GCP_PROJECT" \
        --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
        --role="roles/storage.admin"
    
    # Create and download service account key
    print_info "Creating service account key..."
    gcloud iam service-accounts keys create "./storage-service-account.json" \
        --iam-account="$SERVICE_ACCOUNT_EMAIL"
    
    # Save to environment file
    cat >> .env.local << EOF

# Google Cloud Storage Configuration (Generated)
GCS_BUCKET_NAME=$BUCKET_NAME
GCS_PROJECT_ID=$GCP_PROJECT
GCS_SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL
GCS_SERVICE_ACCOUNT_KEY_PATH=./storage-service-account.json
EOF
    
    print_status "Google Cloud Storage setup complete!"
    print_warning "Service account key saved to: ./storage-service-account.json"
    print_warning "Keep this file secure and do not commit it to version control!"
}

# Configure Supabase authentication
configure_supabase_auth() {
    echo
    print_info "Configuring Supabase authentication..."
    
    print_warning "Manual Supabase Auth configuration required:"
    echo "1. Go to: https://supabase.com/dashboard/project/$PROJECT_REF/auth/providers"
    echo "2. Enable Google provider with:"
    echo "   - Client ID: $GOOGLE_CLIENT_ID"
    echo "   - Client Secret: $GOOGLE_CLIENT_SECRET"
    echo "3. Go to: https://supabase.com/dashboard/project/$PROJECT_REF/auth/url-configuration"
    echo "4. Set Site URL: http://localhost:4000"
    echo "5. Add Redirect URLs:"
    echo "   - http://localhost:4000/api/auth/callback/google"
    echo "   - http://localhost:4000/auth/callback"
    echo
    read -p "Press Enter when configuration is complete..."
    
    print_status "Supabase authentication configured!"
}

# Generate NextAuth secret
generate_nextauth_secret() {
    echo
    print_info "Generating NextAuth secret..."
    
    NEXTAUTH_SECRET=$(openssl rand -base64 32)
    
    # Update environment file
    sed -i '' 's/NEXTAUTH_SECRET=your-secret-key-here/NEXTAUTH_SECRET='$NEXTAUTH_SECRET'/' .env.local
    
    print_status "NextAuth secret generated and saved!"
}

# Main execution
main() {
    check_prerequisites
    setup_supabase
    setup_google_oauth
    setup_google_storage
    configure_supabase_auth
    generate_nextauth_secret
    
    echo
    print_status "🎉 Backend infrastructure setup complete!"
    echo
    print_info "Next steps:"
    echo "1. Review the generated .env.local file"
    echo "2. Test the authentication flow: npm run dev"
    echo "3. Visit http://localhost:4000 to test the application"
    echo
    print_warning "Important files created:"
    echo "- .env.local (updated with real credentials)"
    echo "- storage-service-account.json (keep secure!)"
    echo "- supabase/ directory (local Supabase configuration)"
}

# Run main function
main "$@"
