#!/bin/bash

# GlossaFlow Backend Status Checker
# Verifies the current state of backend infrastructure

echo "🔍 GlossaFlow Backend Infrastructure Status"
echo "==========================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_check() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check CLI tools
echo
print_info "Checking CLI tools..."
command -v supabase &> /dev/null
print_check $? "Supabase CLI installed"

command -v gcloud &> /dev/null
print_check $? "Google Cloud CLI installed"

command -v gsutil &> /dev/null
print_check $? "gsutil available"

# Check authentication
echo
print_info "Checking authentication status..."

supabase projects list &> /dev/null
print_check $? "Supabase CLI authenticated"

gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null
print_check $? "Google Cloud CLI authenticated"

# Check environment variables
echo
print_info "Checking environment variables..."

if [ -f .env.local ]; then
    print_check 0 ".env.local file exists"
    
    # Check for placeholder values
    if grep -q "your-supabase-url" .env.local; then
        print_check 1 "Supabase URL configured (still placeholder)"
    else
        print_check 0 "Supabase URL configured"
    fi
    
    if grep -q "your-secret-key-here" .env.local; then
        print_check 1 "NextAuth secret configured (still placeholder)"
    else
        print_check 0 "NextAuth secret configured"
    fi
    
    if grep -q "your-google-client-id" .env.local; then
        print_check 1 "Google OAuth configured (still placeholder)"
    else
        print_check 0 "Google OAuth configured"
    fi
else
    print_check 1 ".env.local file exists"
fi

# Check project files
echo
print_info "Checking project structure..."

[ -d "supabase" ]
print_check $? "Supabase directory exists"

[ -f "supabase/migrations/001_initial_schema.sql" ]
print_check $? "Database migrations available"

[ -f "storage-service-account.json" ]
print_check $? "Google Cloud service account key exists"

# Check if Supabase project is linked
echo
print_info "Checking Supabase project status..."

if [ -f "supabase/config.toml" ]; then
    PROJECT_REF=$(grep "project_id" supabase/config.toml | cut -d'"' -f2)
    if [ ! -z "$PROJECT_REF" ]; then
        print_check 0 "Supabase project linked ($PROJECT_REF)"
        
        # Check if project exists
        if supabase projects list | grep -q "$PROJECT_REF"; then
            print_check 0 "Supabase project exists and accessible"
        else
            print_check 1 "Supabase project exists and accessible"
        fi
    else
        print_check 1 "Supabase project linked"
    fi
else
    print_check 1 "Supabase project linked"
fi

# Check Google Cloud project
echo
print_info "Checking Google Cloud project status..."

GCP_PROJECT=$(gcloud config get-value project 2>/dev/null)
if [ ! -z "$GCP_PROJECT" ]; then
    print_check 0 "Google Cloud project set ($GCP_PROJECT)"
    
    # Check if required APIs are enabled
    if gcloud services list --enabled --filter="name:oauth2.googleapis.com" --format="value(name)" | grep -q oauth2; then
        print_check 0 "OAuth2 API enabled"
    else
        print_check 1 "OAuth2 API enabled"
    fi
    
    if gcloud services list --enabled --filter="name:storage.googleapis.com" --format="value(name)" | grep -q storage; then
        print_check 0 "Cloud Storage API enabled"
    else
        print_check 1 "Cloud Storage API enabled"
    fi
else
    print_check 1 "Google Cloud project set"
fi

# Summary and next steps
echo
print_info "Setup Status Summary:"

if [ -f .env.local ] && [ -d supabase ] && [ -f storage-service-account.json ]; then
    if ! grep -q "your-supabase-url\|your-secret-key-here\|your-google-client-id" .env.local; then
        echo -e "${GREEN}🎉 Backend infrastructure appears to be fully configured!${NC}"
        echo
        print_info "Ready to test:"
        echo "1. npm run dev"
        echo "2. Visit http://localhost:4000"
        echo "3. Test Google OAuth login"
    else
        echo -e "${YELLOW}⚠️  Backend infrastructure partially configured${NC}"
        echo
        print_info "Next steps:"
        echo "1. Complete Supabase setup: ./scripts/setup-supabase.sh"
        echo "2. Complete Google Cloud setup: ./scripts/setup-google-cloud.sh"
        echo "3. Or run complete setup: ./scripts/setup-complete-backend.sh"
    fi
else
    echo -e "${RED}❌ Backend infrastructure not configured${NC}"
    echo
    print_info "To set up the complete backend:"
    echo "./scripts/setup-complete-backend.sh"
fi

echo
print_info "For detailed setup instructions, see: BACKEND_SETUP_GUIDE.md"
