# GlossaFlow SaaS Platform - Development Blueprint

## Executive Summary

GlossaFlow is a professional-grade translation project management workspace designed for agencies, publishing houses, and professional translators managing complex localization projects. The platform emphasizes systematic terminology management, collaborative review workflows, and cultural context preservation.

**Technology Stack:**
- Frontend: Next.js 14+ (App Router), Tailwind CSS, Shadcn/ui
- Backend: Supabase (PostgreSQL, Storage, Auth, Realtime)
- Authentication: NextAuth.js v5 with Supabase adapter
- State Management: Redux Toolkit with RTK Query
- Payments: Stripe with webhooks
- File Storage: Supabase Storage

---

## 1. System Architecture & Data Flow

### 1.1 High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js App Router]
        B[Redux Store + RTK Query]
        C[Shadcn/ui Components]
    end

    subgraph "Authentication Layer"
        D[NextAuth.js v5]
        E[Supabase Auth]
    end

    subgraph "Backend Services"
        F[Supabase Database]
        G[Supabase Storage]
        H[Supabase Realtime]
        I[Stripe API]
    end

    subgraph "External Services"
        J[Email Provider]
        K[Translation APIs]
    end

    A --> B
    A --> C
    A --> D
    D --> E
    B --> F
    B --> G
    B --> H
    B --> I
    F --> J
    B --> K
```

### 1.2 Component Interaction Strategy

**Server Components (RSC):**
- Page layouts and static content
- Initial data fetching for SEO
- Authentication state verification
- Project overview dashboards

**Client Components:**
- Interactive translation editor
- Real-time collaboration features
- Form handling and validation
- Dynamic UI state management

### 1.3 Core Data Flow: Translation Segment Processing

```
1. Translator opens segment in editor
   ↓
2. Client fetches segment data + terminology via RTK Query
   ↓
3. Real-time subscription established for collaborative editing
   ↓
4. Terminology engine highlights relevant terms
   ↓
5. Translator inputs translation with live validation
   ↓
6. Auto-save triggers every 3 seconds via optimistic updates
   ↓
7. Terminology consistency checks run in background
   ↓
8. Review notifications sent via Supabase Realtime
   ↓
9. Progress metrics updated across all connected clients
```

### 1.4 Real-time Collaboration Architecture

**Supabase Realtime Channels:**
- `project:{projectId}` - Project-level updates
- `segment:{segmentId}` - Segment-level editing
- `terminology:{projectId}` - Term additions/modifications
- `reviews:{projectId}` - Review status changes

**Conflict Resolution Strategy:**
- Last-write-wins for terminology entries
- Operational transformation for segment text
- Version history preservation for audit trails
- Real-time cursor position sharing

---

## 2. Feature Specifications

### 2.1 Authentication & Onboarding

#### Multi-Provider Authentication
- **Email/Password**: Custom registration with email verification
- **OAuth Providers**: Google, GitHub integration via NextAuth.js
- **Magic Links**: Passwordless authentication option
- **SSO**: Enterprise SAML integration (Phase 8)

#### Organization Workspace Creation
```typescript
interface OrganizationOnboarding {
  step1: {
    organizationName: string;
    industry: 'publishing' | 'gaming' | 'technical' | 'marketing' | 'other';
    teamSize: '1-5' | '6-20' | '21-50' | '50+';
  };
  step2: {
    primaryLanguages: LanguagePair[];
    projectTypes: ProjectType[];
    workflowPreferences: WorkflowConfig;
  };
  step3: {
    subscriptionTier: 'starter' | 'professional' | 'enterprise';
    billingInfo: StripeCustomerData;
  };
}
```

#### Role-Based Access Control
- **Organization Admin**: Full access, billing management
- **Project Manager**: Project creation, team assignment, review oversight
- **Senior Translator**: Translation + terminology management
- **Translator**: Translation only
- **Reviewer**: Review and approval workflows
- **Client**: Read-only access to approved translations

### 2.2 Project Management Dashboard

#### Project Overview Interface
- **Progress Visualization**: Segment completion rates, review status
- **Team Activity**: Real-time translator activity, recent changes
- **Quality Metrics**: Terminology consistency scores, review feedback
- **Deadline Tracking**: Milestone progress, critical path analysis

#### Project Creation Wizard
```typescript
interface ProjectCreationFlow {
  basicInfo: {
    name: string;
    description: string;
    sourceLanguage: Language;
    targetLanguages: Language[];
    deadline: Date;
    priority: 'low' | 'medium' | 'high' | 'urgent';
  };
  content: {
    sourceDocuments: File[];
    documentType: 'novel' | 'game' | 'technical' | 'marketing';
    segmentationRules: SegmentationConfig;
  };
  team: {
    projectManager: UserId;
    translators: TeamAssignment[];
    reviewers: TeamAssignment[];
    clientAccess: boolean;
  };
  configuration: {
    terminologyDatabase: TerminologyConfig;
    styleGuide: StyleGuideConfig;
    reviewWorkflow: ReviewWorkflowConfig;
    qualityChecks: QualityCheckConfig;
  };
}
```

### 2.3 Translation Workspace (Core Feature)

#### Terminology Management System

**Term Entry Structure:**
```typescript
interface TerminologyEntry {
  id: string;
  projectId: string;
  sourceTerm: string;
  targetTerm: string;
  category: 'character' | 'location' | 'concept' | 'technical' | 'custom';
  context: string;
  usageNotes: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  createdBy: UserId;
  reviewedBy?: UserId;
  usageExamples: TermUsageExample[];
  relatedTerms: string[];
  frequency: number;
  lastUsed: Date;
}
```

**Bulk Import/Export:**
- CSV format support with validation
- TMX (Translation Memory eXchange) compatibility
- Excel template with predefined columns
- API integration for external terminology databases

#### Project Configuration System

**Style Guide Definition:**
```typescript
interface StyleGuideConfig {
  characterVoices: {
    characterName: string;
    pronouns: PronounSet;
    speechPattern: string;
    formalityLevel: 'casual' | 'formal' | 'archaic';
    consistencyRules: string[];
  }[];
  translationGuidelines: {
    culturalAdaptation: 'literal' | 'localized' | 'creative';
    honorifics: HonorificConfig;
    nameTranslation: 'transliterate' | 'translate' | 'preserve';
    numbersAndDates: LocalizationConfig;
  };
  qualityStandards: {
    maxSegmentLength: number;
    requiredTerminologyUsage: boolean;
    consistencyThreshold: number;
    reviewRequirements: ReviewConfig;
  };
}
```

#### Translation Editor Interface

**Split-Pane Editor Features:**
- Source text with highlighting for terminology
- Target editor with real-time validation
- Terminology sidebar with contextual suggestions
- Translation memory matches with fuzzy scoring
- Character/word count with project limits
- Keyboard shortcuts for efficient navigation

**Real-time Features:**
- Live terminology highlighting as user types
- Instant consistency validation
- Collaborative cursor positions
- Auto-save with conflict resolution
- Undo/redo with granular history

#### Review & Collaboration Workflow

**Multi-Stage Review Process:**
```typescript
interface ReviewWorkflow {
  stages: {
    name: string;
    reviewerRole: UserRole;
    requiredApprovals: number;
    autoAdvance: boolean;
    qualityChecks: QualityCheck[];
  }[];
  escalationRules: {
    condition: string;
    action: 'reassign' | 'notify' | 'escalate';
    target: UserId | UserRole;
  }[];
}
```

**Segment-Level Collaboration:**
- Threaded comments with @mentions
- Change tracking with visual diff
- Approval/rejection with required feedback
- Quality score calculation
- Revision history with rollback capability

### 2.4 Billing & Subscription Management

#### Tiered Pricing Model
```typescript
interface SubscriptionTiers {
  starter: {
    monthlyPrice: 29;
    features: {
      projects: 3;
      teamMembers: 5;
      storageGB: 10;
      terminologyEntries: 1000;
      apiCalls: 1000;
    };
  };
  professional: {
    monthlyPrice: 99;
    features: {
      projects: 25;
      teamMembers: 25;
      storageGB: 100;
      terminologyEntries: 10000;
      apiCalls: 10000;
      advancedReports: true;
      customWorkflows: true;
    };
  };
  enterprise: {
    monthlyPrice: 299;
    features: {
      projects: 'unlimited';
      teamMembers: 'unlimited';
      storageGB: 1000;
      terminologyEntries: 'unlimited';
      apiCalls: 50000;
      ssoIntegration: true;
      customIntegrations: true;
      dedicatedSupport: true;
    };
  };
}
```

---

## 3. Database Schema Design

### 3.1 Core Tables Schema

```sql
-- Organizations and Users
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    industry VARCHAR(50),
    team_size VARCHAR(20),
    subscription_tier VARCHAR(20) DEFAULT 'starter',
    stripe_customer_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    avatar_url TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'project_manager', 'senior_translator', 'translator', 'reviewer', 'client')),
    invited_by UUID REFERENCES users(id),
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    joined_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'suspended')),
    UNIQUE(organization_id, user_id)
);

-- Projects and Content
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    source_language VARCHAR(10) NOT NULL,
    target_languages VARCHAR(10)[] NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'archived')),
    created_by UUID REFERENCES users(id),
    project_manager_id UUID REFERENCES users(id),
    style_guide JSONB,
    workflow_config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE source_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    upload_status VARCHAR(20) DEFAULT 'processing',
    segment_count INTEGER DEFAULT 0,
    uploaded_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE translation_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    document_id UUID REFERENCES source_documents(id) ON DELETE CASCADE,
    segment_number INTEGER NOT NULL,
    source_text TEXT NOT NULL,
    target_text TEXT,
    target_language VARCHAR(10) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'translated', 'reviewed', 'approved', 'rejected')),
    assigned_translator_id UUID REFERENCES users(id),
    assigned_reviewer_id UUID REFERENCES users(id),
    word_count INTEGER,
    character_count INTEGER,
    translation_started_at TIMESTAMP WITH TIME ZONE,
    translation_completed_at TIMESTAMP WITH TIME ZONE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, document_id, segment_number, target_language)
);

-- Terminology Management
CREATE TABLE terminology_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    source_term VARCHAR(255) NOT NULL,
    target_term VARCHAR(255) NOT NULL,
    target_language VARCHAR(10) NOT NULL,
    category VARCHAR(50) DEFAULT 'general',
    context TEXT,
    usage_notes TEXT,
    approval_status VARCHAR(20) DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    frequency INTEGER DEFAULT 0,
    created_by UUID REFERENCES users(id),
    reviewed_by UUID REFERENCES users(id),
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, source_term, target_language)
);

CREATE TABLE term_usage_examples (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    terminology_entry_id UUID REFERENCES terminology_entries(id) ON DELETE CASCADE,
    source_context TEXT NOT NULL,
    target_context TEXT NOT NULL,
    segment_id UUID REFERENCES translation_segments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Collaboration and Reviews
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    segment_id UUID REFERENCES translation_segments(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    comment_type VARCHAR(20) DEFAULT 'general' CHECK (comment_type IN ('general', 'suggestion', 'issue', 'approval')),
    parent_comment_id UUID REFERENCES comments(id),
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    segment_id UUID REFERENCES translation_segments(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    review_type VARCHAR(20) NOT NULL CHECK (review_type IN ('linguistic', 'cultural', 'technical', 'final')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'needs_revision')),
    feedback TEXT,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    reviewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSONB,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Billing and Usage
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    stripe_subscription_id VARCHAR(255) UNIQUE,
    status VARCHAR(20) NOT NULL,
    tier VARCHAR(20) NOT NULL,
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    metric_type VARCHAR(50) NOT NULL,
    metric_value INTEGER NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3.2 Indexes for Performance

```sql
-- User and organization lookups
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_org_members_org_user ON organization_members(organization_id, user_id);
CREATE INDEX idx_org_members_user_role ON organization_members(user_id, role);

-- Project and content queries
CREATE INDEX idx_projects_org_status ON projects(organization_id, status);
CREATE INDEX idx_projects_deadline ON projects(deadline) WHERE deadline IS NOT NULL;
CREATE INDEX idx_segments_project_status ON translation_segments(project_id, status);
CREATE INDEX idx_segments_translator ON translation_segments(assigned_translator_id) WHERE assigned_translator_id IS NOT NULL;
CREATE INDEX idx_segments_reviewer ON translation_segments(assigned_reviewer_id) WHERE assigned_reviewer_id IS NOT NULL;

-- Terminology searches
CREATE INDEX idx_terminology_project_lang ON terminology_entries(project_id, target_language);
CREATE INDEX idx_terminology_source_term ON terminology_entries USING gin(to_tsvector('english', source_term));
CREATE INDEX idx_terminology_target_term ON terminology_entries USING gin(to_tsvector('english', target_term));

-- Collaboration features
CREATE INDEX idx_comments_segment ON comments(segment_id);
CREATE INDEX idx_reviews_segment_reviewer ON reviews(segment_id, reviewer_id);
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, read, created_at);
```

### 3.3 Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE source_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE terminology_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Organization access policies
CREATE POLICY "Users can view organizations they belong to" ON organizations
    FOR SELECT USING (
        id IN (
            SELECT organization_id FROM organization_members
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Organization admins can update their organization" ON organizations
    FOR UPDATE USING (
        id IN (
            SELECT organization_id FROM organization_members
            WHERE user_id = auth.uid() AND role = 'admin' AND status = 'active'
        )
    );

-- Project access policies
CREATE POLICY "Users can view projects in their organization" ON projects
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Project managers can manage projects" ON projects
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM organization_members
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'project_manager')
            AND status = 'active'
        )
    );

-- Translation segment policies
CREATE POLICY "Users can view segments in accessible projects" ON translation_segments
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Translators can update assigned segments" ON translation_segments
    FOR UPDATE USING (
        assigned_translator_id = auth.uid() OR
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid()
            AND om.role IN ('admin', 'project_manager')
            AND om.status = 'active'
        )
    );

-- Terminology access policies
CREATE POLICY "Users can view terminology in accessible projects" ON terminology_entries
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid() AND om.status = 'active'
        )
    );

CREATE POLICY "Translators can create terminology entries" ON terminology_entries
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT p.id FROM projects p
            JOIN organization_members om ON p.organization_id = om.organization_id
            WHERE om.user_id = auth.uid()
            AND om.role IN ('admin', 'project_manager', 'senior_translator', 'translator')
            AND om.status = 'active'
        )
    );
```

### 3.4 Database Triggers and Functions

```sql
-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_segments_updated_at BEFORE UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_terminology_updated_at BEFORE UPDATE ON terminology_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update terminology frequency on usage
CREATE OR REPLACE FUNCTION update_terminology_frequency()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.target_text IS NOT NULL AND NEW.target_text != OLD.target_text THEN
        UPDATE terminology_entries
        SET frequency = frequency + 1, last_used_at = NOW()
        WHERE project_id = NEW.project_id
        AND target_language = NEW.target_language
        AND NEW.target_text ILIKE '%' || target_term || '%';
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_terminology_frequency_trigger
    AFTER UPDATE ON translation_segments
    FOR EACH ROW EXECUTE FUNCTION update_terminology_frequency();

-- Notification triggers
CREATE OR REPLACE FUNCTION create_review_notification()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO notifications (user_id, type, title, message, data)
    VALUES (
        NEW.assigned_reviewer_id,
        'review_requested',
        'New Review Request',
        'A translation segment has been submitted for your review',
        jsonb_build_object('segment_id', NEW.id, 'project_id', NEW.project_id)
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER review_notification_trigger
    AFTER UPDATE ON translation_segments
    FOR EACH ROW
    WHEN (OLD.status != 'translated' AND NEW.status = 'translated' AND NEW.assigned_reviewer_id IS NOT NULL)
    EXECUTE FUNCTION create_review_notification();
```

---

## 4. API Architecture

### 4.1 RESTful API Endpoints

#### Authentication Endpoints
```typescript
// NextAuth.js handles these automatically
POST /api/auth/signin
POST /api/auth/signout
GET  /api/auth/session
GET  /api/auth/providers
POST /api/auth/callback/[provider]
```

#### Organization Management
```typescript
// GET /api/organizations
interface GetOrganizationsResponse {
  organizations: {
    id: string;
    name: string;
    slug: string;
    role: UserRole;
    memberCount: number;
    projectCount: number;
  }[];
}

// POST /api/organizations
interface CreateOrganizationRequest {
  name: string;
  slug: string;
  industry?: string;
  teamSize?: string;
}

// GET /api/organizations/[orgId]
interface GetOrganizationResponse {
  organization: Organization;
  members: OrganizationMember[];
  projects: ProjectSummary[];
  subscription: SubscriptionInfo;
}

// PUT /api/organizations/[orgId]
interface UpdateOrganizationRequest {
  name?: string;
  industry?: string;
  settings?: OrganizationSettings;
}

// POST /api/organizations/[orgId]/invite
interface InviteUserRequest {
  email: string;
  role: UserRole;
  message?: string;
}
```

#### Project Management
```typescript
// GET /api/projects?orgId=xxx&status=active&page=1&limit=20
interface GetProjectsResponse {
  projects: ProjectSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// POST /api/projects
interface CreateProjectRequest {
  organizationId: string;
  name: string;
  description?: string;
  sourceLanguage: string;
  targetLanguages: string[];
  documentType: DocumentType;
  priority: Priority;
  deadline?: string;
  teamAssignments: TeamAssignment[];
  styleGuide?: StyleGuideConfig;
  workflowConfig?: WorkflowConfig;
}

// GET /api/projects/[projectId]
interface GetProjectResponse {
  project: Project;
  documents: SourceDocument[];
  team: ProjectTeamMember[];
  progress: ProjectProgress;
  terminology: TerminologyEntry[];
}

// PUT /api/projects/[projectId]
interface UpdateProjectRequest {
  name?: string;
  description?: string;
  priority?: Priority;
  deadline?: string;
  status?: ProjectStatus;
  styleGuide?: StyleGuideConfig;
}

// POST /api/projects/[projectId]/documents
interface UploadDocumentRequest {
  files: File[];
  segmentationRules?: SegmentationConfig;
}

// GET /api/projects/[projectId]/segments?page=1&limit=50&status=pending
interface GetSegmentsResponse {
  segments: TranslationSegment[];
  pagination: PaginationInfo;
  filters: {
    statuses: SegmentStatus[];
    translators: User[];
    reviewers: User[];
  };
}
```

#### Translation Workspace
```typescript
// GET /api/segments/[segmentId]
interface GetSegmentResponse {
  segment: TranslationSegment;
  terminology: TerminologyMatch[];
  translationMemory: TMMatch[];
  comments: Comment[];
  reviews: Review[];
  context: {
    previousSegments: TranslationSegment[];
    nextSegments: TranslationSegment[];
  };
}

// PUT /api/segments/[segmentId]
interface UpdateSegmentRequest {
  targetText?: string;
  status?: SegmentStatus;
  translationStartedAt?: string;
  translationCompletedAt?: string;
}

// POST /api/segments/[segmentId]/comments
interface CreateCommentRequest {
  content: string;
  commentType: CommentType;
  parentCommentId?: string;
}

// POST /api/segments/[segmentId]/reviews
interface CreateReviewRequest {
  reviewType: ReviewType;
  status: ReviewStatus;
  feedback?: string;
  qualityScore?: number;
}
```

#### Terminology Management
```typescript
// GET /api/projects/[projectId]/terminology?search=term&category=character&page=1
interface GetTerminologyResponse {
  entries: TerminologyEntry[];
  pagination: PaginationInfo;
  categories: string[];
  statistics: {
    totalEntries: number;
    approvedEntries: number;
    pendingEntries: number;
  };
}

// POST /api/projects/[projectId]/terminology
interface CreateTerminologyRequest {
  sourceTerm: string;
  targetTerm: string;
  targetLanguage: string;
  category?: string;
  context?: string;
  usageNotes?: string;
}

// PUT /api/terminology/[entryId]
interface UpdateTerminologyRequest {
  targetTerm?: string;
  category?: string;
  context?: string;
  usageNotes?: string;
  approvalStatus?: ApprovalStatus;
}

// POST /api/projects/[projectId]/terminology/import
interface ImportTerminologyRequest {
  file: File;
  format: 'csv' | 'tmx' | 'excel';
  mapping: FieldMapping;
  overwriteExisting: boolean;
}

// GET /api/projects/[projectId]/terminology/export?format=csv&language=ja
interface ExportTerminologyResponse {
  downloadUrl: string;
  expiresAt: string;
}
```

### 4.2 RTK Query Service Definitions

```typescript
// src/store/api/organizationsApi.ts
export const organizationsApi = createApi({
  reducerPath: 'organizationsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/organizations',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Organization', 'Member', 'Subscription'],
  endpoints: (builder) => ({
    getOrganizations: builder.query<GetOrganizationsResponse, void>({
      query: () => '',
      providesTags: ['Organization'],
    }),
    createOrganization: builder.mutation<Organization, CreateOrganizationRequest>({
      query: (body) => ({
        url: '',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Organization'],
    }),
    getOrganization: builder.query<GetOrganizationResponse, string>({
      query: (orgId) => `/${orgId}`,
      providesTags: (result, error, orgId) => [
        { type: 'Organization', id: orgId },
        'Member',
        'Subscription',
      ],
    }),
    updateOrganization: builder.mutation<Organization, { orgId: string; data: UpdateOrganizationRequest }>({
      query: ({ orgId, data }) => ({
        url: `/${orgId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { orgId }) => [
        { type: 'Organization', id: orgId },
      ],
    }),
    inviteUser: builder.mutation<void, { orgId: string; data: InviteUserRequest }>({
      query: ({ orgId, data }) => ({
        url: `/${orgId}/invite`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Member'],
    }),
  }),
});

export const {
  useGetOrganizationsQuery,
  useCreateOrganizationMutation,
  useGetOrganizationQuery,
  useUpdateOrganizationMutation,
  useInviteUserMutation,
} = organizationsApi;

// src/store/api/projectsApi.ts
export const projectsApi = createApi({
  reducerPath: 'projectsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/projects',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Project', 'Segment', 'Document', 'Terminology'],
  endpoints: (builder) => ({
    getProjects: builder.query<GetProjectsResponse, GetProjectsParams>({
      query: (params) => ({
        url: '',
        params,
      }),
      providesTags: ['Project'],
    }),
    createProject: builder.mutation<Project, CreateProjectRequest>({
      query: (body) => ({
        url: '',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Project'],
    }),
    getProject: builder.query<GetProjectResponse, string>({
      query: (projectId) => `/${projectId}`,
      providesTags: (result, error, projectId) => [
        { type: 'Project', id: projectId },
        'Document',
        'Terminology',
      ],
    }),
    updateProject: builder.mutation<Project, { projectId: string; data: UpdateProjectRequest }>({
      query: ({ projectId, data }) => ({
        url: `/${projectId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { projectId }) => [
        { type: 'Project', id: projectId },
      ],
    }),
    uploadDocuments: builder.mutation<SourceDocument[], { projectId: string; data: FormData }>({
      query: ({ projectId, data }) => ({
        url: `/${projectId}/documents`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Document', 'Segment'],
    }),
    getSegments: builder.query<GetSegmentsResponse, GetSegmentsParams>({
      query: ({ projectId, ...params }) => ({
        url: `/${projectId}/segments`,
        params,
      }),
      providesTags: ['Segment'],
    }),
  }),
});

export const {
  useGetProjectsQuery,
  useCreateProjectMutation,
  useGetProjectQuery,
  useUpdateProjectMutation,
  useUploadDocumentsMutation,
  useGetSegmentsQuery,
} = projectsApi;

// src/store/api/translationApi.ts
export const translationApi = createApi({
  reducerPath: 'translationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/segments',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Segment', 'Comment', 'Review'],
  endpoints: (builder) => ({
    getSegment: builder.query<GetSegmentResponse, string>({
      query: (segmentId) => `/${segmentId}`,
      providesTags: (result, error, segmentId) => [
        { type: 'Segment', id: segmentId },
        'Comment',
        'Review',
      ],
    }),
    updateSegment: builder.mutation<TranslationSegment, { segmentId: string; data: UpdateSegmentRequest }>({
      query: ({ segmentId, data }) => ({
        url: `/${segmentId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { segmentId }) => [
        { type: 'Segment', id: segmentId },
      ],
      // Optimistic update for better UX
      async onQueryStarted({ segmentId, data }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          translationApi.util.updateQueryData('getSegment', segmentId, (draft) => {
            Object.assign(draft.segment, data);
          })
        );
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
    }),
    createComment: builder.mutation<Comment, { segmentId: string; data: CreateCommentRequest }>({
      query: ({ segmentId, data }) => ({
        url: `/${segmentId}/comments`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Comment'],
    }),
    createReview: builder.mutation<Review, { segmentId: string; data: CreateReviewRequest }>({
      query: ({ segmentId, data }) => ({
        url: `/${segmentId}/reviews`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Review'],
    }),
  }),
});

export const {
  useGetSegmentQuery,
  useUpdateSegmentMutation,
  useCreateCommentMutation,
  useCreateReviewMutation,
} = translationApi;
```

### 4.3 Real-time Subscriptions with Supabase

```typescript
// src/hooks/useRealtimeSubscription.ts
import { useEffect } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useAppDispatch } from '@/store/hooks';

export function useProjectRealtime(projectId: string) {
  const supabase = useSupabaseClient();
  const dispatch = useAppDispatch();

  useEffect(() => {
    const channel = supabase
      .channel(`project:${projectId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'translation_segments',
          filter: `project_id=eq.${projectId}`,
        },
        (payload) => {
          // Update RTK Query cache with real-time changes
          dispatch(
            translationApi.util.updateQueryData('getSegments', { projectId }, (draft) => {
              if (payload.eventType === 'INSERT') {
                draft.segments.push(payload.new as TranslationSegment);
              } else if (payload.eventType === 'UPDATE') {
                const index = draft.segments.findIndex(s => s.id === payload.new.id);
                if (index !== -1) {
                  draft.segments[index] = payload.new as TranslationSegment;
                }
              } else if (payload.eventType === 'DELETE') {
                draft.segments = draft.segments.filter(s => s.id !== payload.old.id);
              }
            })
          );
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'terminology_entries',
          filter: `project_id=eq.${projectId}`,
        },
        (payload) => {
          // Update terminology cache
          dispatch(
            terminologyApi.util.updateQueryData('getTerminology', { projectId }, (draft) => {
              if (payload.eventType === 'INSERT') {
                draft.entries.push(payload.new as TerminologyEntry);
              } else if (payload.eventType === 'UPDATE') {
                const index = draft.entries.findIndex(e => e.id === payload.new.id);
                if (index !== -1) {
                  draft.entries[index] = payload.new as TerminologyEntry;
                }
              }
            })
          );
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [projectId, supabase, dispatch]);
}

// src/hooks/useSegmentCollaboration.ts
export function useSegmentCollaboration(segmentId: string) {
  const supabase = useSupabaseClient();
  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([]);
  const [cursorPositions, setCursorPositions] = useState<CursorPosition[]>([]);

  useEffect(() => {
    const channel = supabase
      .channel(`segment:${segmentId}`)
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const users = Object.values(state).flat() as CollaborationUser[];
        setActiveUsers(users);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', leftPresences);
      })
      .on('broadcast', { event: 'cursor_move' }, ({ payload }) => {
        setCursorPositions(prev => {
          const filtered = prev.filter(p => p.userId !== payload.userId);
          return [...filtered, payload];
        });
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          await channel.track({
            userId: user?.id,
            userName: user?.name,
            avatarUrl: user?.avatar_url,
            timestamp: new Date().toISOString(),
          });
        }
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [segmentId, supabase]);

  const broadcastCursorPosition = useCallback((position: number, selection: [number, number]) => {
    channel?.send({
      type: 'broadcast',
      event: 'cursor_move',
      payload: {
        userId: user?.id,
        position,
        selection,
        timestamp: new Date().toISOString(),
      },
    });
  }, [channel, user]);

  return {
    activeUsers,
    cursorPositions,
    broadcastCursorPosition,
  };
}
```

### 4.4 Rate Limiting and Caching

```typescript
// src/middleware/rateLimit.ts
import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

interface RateLimitConfig {
  requests: number;
  window: number; // seconds
}

const rateLimits: Record<string, RateLimitConfig> = {
  '/api/segments': { requests: 100, window: 60 }, // 100 requests per minute
  '/api/terminology': { requests: 50, window: 60 }, // 50 requests per minute
  '/api/projects': { requests: 30, window: 60 }, // 30 requests per minute
};

export async function rateLimit(request: NextRequest, endpoint: string) {
  const ip = request.ip ?? '127.0.0.1';
  const config = rateLimits[endpoint] || { requests: 10, window: 60 };

  const key = `rate_limit:${endpoint}:${ip}`;
  const current = await redis.get(key) as number | null;

  if (current === null) {
    await redis.setex(key, config.window, 1);
    return { success: true, remaining: config.requests - 1 };
  }

  if (current >= config.requests) {
    return { success: false, remaining: 0 };
  }

  await redis.incr(key);
  return { success: true, remaining: config.requests - current - 1 };
}

// src/middleware/cache.ts
export function withCache(handler: any, ttl: number = 300) {
  return async (req: NextRequest) => {
    const cacheKey = `cache:${req.url}`;

    // Try to get from cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      return NextResponse.json(cached);
    }

    // Execute handler and cache result
    const response = await handler(req);
    const data = await response.json();

    await redis.setex(cacheKey, ttl, data);
    return NextResponse.json(data);
  };
}
```

---

## 5. Implementation Roadmap

### Phase 1: Foundation Setup (Week 1-2)

**Deliverables:**
- Next.js 14 project with App Router configuration
- Supabase project setup with initial database schema
- Stripe account configuration with webhook endpoints
- Basic authentication flow with NextAuth.js v5

**Technical Tasks:**
```bash
# Project initialization
npx create-next-app@latest glossaflow --typescript --tailwind --eslint --app
cd glossaflow

# Install core dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs @supabase/auth-helpers-react
npm install next-auth@beta @auth/supabase-adapter
npm install @reduxjs/toolkit react-redux
npm install stripe @stripe/stripe-js
npm install @radix-ui/react-* class-variance-authority clsx tailwind-merge
npm install lucide-react @hookform/resolvers zod react-hook-form

# Development dependencies
npm install -D @types/node prisma
```

**Environment Configuration:**
```env
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token
```

**Database Migration:**
```sql
-- Run the complete schema from Section 3.1
-- Apply indexes from Section 3.2
-- Enable RLS policies from Section 3.3
-- Create triggers from Section 3.4
```

### Phase 2: Authentication & Core Models (Week 3-4)

**Deliverables:**
- Complete authentication system with multiple providers
- User registration and organization creation flows
- Basic dashboard with organization switching
- Core data models and TypeScript interfaces

**Key Components:**
```typescript
// src/app/auth/signin/page.tsx
export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Sign in to GlossaFlow</CardTitle>
        </CardHeader>
        <CardContent>
          <SignInForm />
        </CardContent>
      </Card>
    </div>
  );
}

// src/components/auth/SignInForm.tsx
export function SignInForm() {
  const [isLoading, setIsLoading] = useState(false);

  const handleEmailSignIn = async (email: string, password: string) => {
    setIsLoading(true);
    const result = await signIn('credentials', {
      email,
      password,
      redirect: false,
    });
    setIsLoading(false);

    if (result?.ok) {
      router.push('/dashboard');
    }
  };

  const handleOAuthSignIn = (provider: string) => {
    signIn(provider, { callbackUrl: '/dashboard' });
  };

  return (
    <div className="space-y-4">
      <EmailPasswordForm onSubmit={handleEmailSignIn} loading={isLoading} />
      <Separator />
      <OAuthButtons onSignIn={handleOAuthSignIn} />
    </div>
  );
}
```

**Organization Creation Flow:**
```typescript
// src/app/onboarding/page.tsx
export default function OnboardingPage() {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<OrganizationOnboarding>({});

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto pt-16">
        <OnboardingProgress currentStep={step} totalSteps={3} />

        {step === 1 && (
          <OrganizationBasicInfo
            data={formData.step1}
            onNext={(data) => {
              setFormData(prev => ({ ...prev, step1: data }));
              setStep(2);
            }}
          />
        )}

        {step === 2 && (
          <ProjectPreferences
            data={formData.step2}
            onNext={(data) => {
              setFormData(prev => ({ ...prev, step2: data }));
              setStep(3);
            }}
            onBack={() => setStep(1)}
          />
        )}

        {step === 3 && (
          <SubscriptionSelection
            data={formData.step3}
            onComplete={handleCreateOrganization}
            onBack={() => setStep(2)}
          />
        )}
      </div>
    </div>
  );
}
```

### Phase 3: Project Management (Week 5-6)

**Deliverables:**
- Project CRUD operations with team management
- Document upload and processing pipeline
- Basic project dashboard with progress tracking
- Team member invitation system

**Project Creation Wizard:**
```typescript
// src/components/projects/ProjectCreationWizard.tsx
export function ProjectCreationWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [projectData, setProjectData] = useState<CreateProjectRequest>({});

  const steps = [
    { title: 'Basic Information', component: BasicInfoStep },
    { title: 'Content Upload', component: ContentUploadStep },
    { title: 'Team Assignment', component: TeamAssignmentStep },
    { title: 'Configuration', component: ConfigurationStep },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <ProjectCreationProgress currentStep={currentStep} steps={steps} />
        </DialogHeader>

        <div className="mt-6">
          {React.createElement(steps[currentStep].component, {
            data: projectData,
            onNext: (data: any) => {
              setProjectData(prev => ({ ...prev, ...data }));
              if (currentStep < steps.length - 1) {
                setCurrentStep(prev => prev + 1);
              } else {
                handleCreateProject(projectData);
              }
            },
            onBack: () => setCurrentStep(prev => Math.max(0, prev - 1)),
          })}
        </div>
      </DialogContent>
    </Dialog>
  );
}
```

**Document Processing Pipeline:**
```typescript
// src/lib/documentProcessor.ts
export class DocumentProcessor {
  async processDocument(file: File, projectId: string, config: SegmentationConfig) {
    // 1. Upload to Supabase Storage
    const filePath = await this.uploadFile(file, projectId);

    // 2. Extract text content
    const content = await this.extractText(file);

    // 3. Segment content based on rules
    const segments = await this.segmentContent(content, config);

    // 4. Create database records
    const document = await this.createDocument({
      projectId,
      filename: file.name,
      filePath,
      segmentCount: segments.length,
    });

    // 5. Create segment records
    await this.createSegments(document.id, segments);

    return document;
  }

  private async segmentContent(content: string, config: SegmentationConfig) {
    const segments: string[] = [];

    switch (config.type) {
      case 'paragraph':
        return content.split(/\n\s*\n/).filter(s => s.trim());
      case 'sentence':
        return content.split(/[.!?]+/).filter(s => s.trim());
      case 'custom':
        return content.split(new RegExp(config.delimiter)).filter(s => s.trim());
      default:
        return [content];
    }
  }
}
```

### Phase 4: Terminology System (Week 7-8)

**Deliverables:**
- Terminology database with CRUD operations
- Import/export functionality for CSV and TMX formats
- Term validation and conflict detection
- Terminology search and filtering

**Terminology Management Interface:**
```typescript
// src/components/terminology/TerminologyManager.tsx
export function TerminologyManager({ projectId }: { projectId: string }) {
  const { data: terminology, isLoading } = useGetTerminologyQuery({ projectId });
  const [createTerm] = useCreateTerminologyMutation();
  const [importTerms] = useImportTerminologyMutation();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Terminology Management</h2>
        <div className="flex gap-2">
          <ImportTerminologyDialog onImport={handleImport} />
          <ExportTerminologyButton projectId={projectId} />
          <CreateTermDialog onSubmit={handleCreateTerm} />
        </div>
      </div>

      <TerminologyFilters
        categories={terminology?.categories || []}
        onFilterChange={setFilters}
      />

      <TerminologyTable
        entries={terminology?.entries || []}
        onEdit={handleEditTerm}
        onDelete={handleDeleteTerm}
        onApprove={handleApproveTerm}
      />
    </div>
  );
}
```

**Import/Export System:**
```typescript
// src/lib/terminologyImporter.ts
export class TerminologyImporter {
  async importFromCSV(file: File, projectId: string, mapping: FieldMapping) {
    const content = await file.text();
    const rows = this.parseCSV(content);

    const entries: CreateTerminologyRequest[] = rows.map(row => ({
      projectId,
      sourceTerm: row[mapping.sourceTerm],
      targetTerm: row[mapping.targetTerm],
      targetLanguage: row[mapping.targetLanguage],
      category: row[mapping.category] || 'general',
      context: row[mapping.context] || '',
      usageNotes: row[mapping.usageNotes] || '',
    }));

    // Validate entries
    const validationResults = await this.validateEntries(entries);

    // Import valid entries
    const importResults = await this.batchCreateTerms(validationResults.valid);

    return {
      imported: importResults.length,
      errors: validationResults.errors,
      duplicates: validationResults.duplicates,
    };
  }

  async exportToCSV(projectId: string, language: string) {
    const entries = await this.getTerminologyEntries(projectId, language);

    const csvContent = this.generateCSV(entries, [
      'sourceTerm',
      'targetTerm',
      'category',
      'context',
      'usageNotes',
      'approvalStatus',
      'frequency',
    ]);

    return csvContent;
  }
}
```

### Phase 5: Translation Editor MVP (Week 9-10)

**Deliverables:**
- Split-pane translation editor interface
- Terminology highlighting and suggestions
- Translation memory integration
- Auto-save functionality with optimistic updates

**Translation Editor Component:**
```typescript
// src/components/translation/TranslationEditor.tsx
export function TranslationEditor({ segmentId }: { segmentId: string }) {
  const { data: segment, isLoading } = useGetSegmentQuery(segmentId);
  const [updateSegment] = useUpdateSegmentMutation();
  const [targetText, setTargetText] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // Real-time collaboration
  const { activeUsers, cursorPositions, broadcastCursorPosition } =
    useSegmentCollaboration(segmentId);

  // Auto-save with debouncing
  const debouncedSave = useMemo(
    () => debounce((text: string) => {
      updateSegment({
        segmentId,
        data: { targetText: text, status: 'in_progress' }
      });
    }, 3000),
    [segmentId, updateSegment]
  );

  useEffect(() => {
    if (targetText && isEditing) {
      debouncedSave(targetText);
    }
  }, [targetText, isEditing, debouncedSave]);

  return (
    <div className="grid grid-cols-2 gap-6 h-full">
      {/* Source Text Panel */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Source Text</h3>
          <LanguageBadge language={segment?.project.sourceLanguage} />
        </div>

        <Card className="p-4">
          <SourceTextDisplay
            text={segment?.sourceText || ''}
            terminology={segment?.terminology || []}
          />
        </Card>

        <ContextPanel
          previousSegments={segment?.context.previousSegments || []}
          nextSegments={segment?.context.nextSegments || []}
        />
      </div>

      {/* Target Text Panel */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Translation</h3>
          <div className="flex items-center gap-2">
            <LanguageBadge language={segment?.targetLanguage} />
            <CollaborationIndicator users={activeUsers} />
          </div>
        </div>

        <Card className="p-4">
          <TranslationTextArea
            value={targetText}
            onChange={(value) => {
              setTargetText(value);
              setIsEditing(true);
            }}
            onCursorChange={broadcastCursorPosition}
            terminology={segment?.terminology || []}
            translationMemory={segment?.translationMemory || []}
            cursorPositions={cursorPositions}
          />
        </Card>

        <div className="grid grid-cols-2 gap-4">
          <TerminologyPanel
            entries={segment?.terminology || []}
            onInsertTerm={(term) => setTargetText(prev => prev + term)}
          />
          <TranslationMemoryPanel
            matches={segment?.translationMemory || []}
            onApplyMatch={(match) => setTargetText(match.targetText)}
          />
        </div>
      </div>
    </div>
  );
}
```

---

## 6. Code Examples & Documentation

### 6.1 Critical Component Examples

#### NextAuth.js Configuration
```typescript
// src/app/api/auth/[...nextauth]/route.ts
import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import CredentialsProvider from 'next-auth/providers/credentials';
import { SupabaseAdapter } from '@auth/supabase-adapter';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const handler = NextAuth({
  adapter: SupabaseAdapter({
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  }),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null;

        const { data, error } = await supabase.auth.signInWithPassword({
          email: credentials.email,
          password: credentials.password,
        });

        if (error || !data.user) return null;

        return {
          id: data.user.id,
          email: data.user.email!,
          name: data.user.user_metadata?.name,
          image: data.user.user_metadata?.avatar_url,
        };
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub!;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.sub = user.id;
      }
      return token;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
});

export { handler as GET, handler as POST };
```

#### Redux Store Configuration
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { organizationsApi } from './api/organizationsApi';
import { projectsApi } from './api/projectsApi';
import { translationApi } from './api/translationApi';
import { terminologyApi } from './api/terminologyApi';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    [organizationsApi.reducerPath]: organizationsApi.reducer,
    [projectsApi.reducerPath]: projectsApi.reducer,
    [translationApi.reducerPath]: translationApi.reducer,
    [terminologyApi.reducerPath]: terminologyApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    })
      .concat(organizationsApi.middleware)
      .concat(projectsApi.middleware)
      .concat(translationApi.middleware)
      .concat(terminologyApi.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

#### Supabase Client Configuration
```typescript
// src/lib/supabase.ts
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export const createClient = () => createClientComponentClient();

export const createServerClient = () => createServerComponentClient({ cookies });

// Database types
export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string;
          name: string;
          slug: string;
          industry: string | null;
          team_size: string | null;
          subscription_tier: string;
          stripe_customer_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          industry?: string | null;
          team_size?: string | null;
          subscription_tier?: string;
          stripe_customer_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          industry?: string | null;
          team_size?: string | null;
          subscription_tier?: string;
          stripe_customer_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      // ... other table types
    };
  };
}
```

### 6.2 Migration Scripts

#### Initial Database Setup
```sql
-- migrations/001_initial_schema.sql
-- Run the complete schema from Section 3.1
-- This includes all tables, constraints, and relationships

-- migrations/002_indexes.sql
-- Run all indexes from Section 3.2

-- migrations/003_rls_policies.sql
-- Enable RLS and create policies from Section 3.3

-- migrations/004_triggers_functions.sql
-- Create triggers and functions from Section 3.4

-- migrations/005_seed_data.sql
INSERT INTO organizations (name, slug, subscription_tier) VALUES
('Demo Organization', 'demo-org', 'starter');

INSERT INTO users (email, name) VALUES
('<EMAIL>', 'Demo Admin');

INSERT INTO organization_members (organization_id, user_id, role, status) VALUES
((SELECT id FROM organizations WHERE slug = 'demo-org'),
 (SELECT id FROM users WHERE email = '<EMAIL>'),
 'admin', 'active');
```

### 6.3 Deployment Configuration

#### Vercel Deployment
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "NEXTAUTH_URL": "@nextauth_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase_url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key",
    "STRIPE_SECRET_KEY": "@stripe_secret_key",
    "STRIPE_WEBHOOK_SECRET": "@stripe_webhook_secret"
  }
}
```

#### Docker Configuration
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 6.4 Testing Strategy

#### Unit Testing Setup
```typescript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
};

module.exports = createJestConfig(customJestConfig);
```

```typescript
// __tests__/components/TranslationEditor.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '@/store';
import { TranslationEditor } from '@/components/translation/TranslationEditor';

const MockedTranslationEditor = ({ segmentId }: { segmentId: string }) => (
  <Provider store={store}>
    <TranslationEditor segmentId={segmentId} />
  </Provider>
);

describe('TranslationEditor', () => {
  it('renders source and target text areas', () => {
    render(<MockedTranslationEditor segmentId="test-segment" />);

    expect(screen.getByText('Source Text')).toBeInTheDocument();
    expect(screen.getByText('Translation')).toBeInTheDocument();
  });

  it('auto-saves translation after typing', async () => {
    render(<MockedTranslationEditor segmentId="test-segment" />);

    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Test translation' } });

    await waitFor(() => {
      // Verify auto-save was triggered
      expect(mockUpdateSegment).toHaveBeenCalledWith({
        segmentId: 'test-segment',
        data: { targetText: 'Test translation', status: 'in_progress' }
      });
    }, { timeout: 4000 });
  });
});
```

#### Integration Testing
```typescript
// __tests__/api/projects.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/app/api/projects/route';

describe('/api/projects', () => {
  it('creates a new project', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        organizationId: 'test-org',
        name: 'Test Project',
        sourceLanguage: 'en',
        targetLanguages: ['ja'],
        documentType: 'novel',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(201);
    const data = JSON.parse(res._getData());
    expect(data.project.name).toBe('Test Project');
  });
});
```

### 6.5 Quality Assurance Guidelines

#### Code Quality Standards
- **TypeScript**: Strict mode enabled, no `any` types
- **ESLint**: Airbnb configuration with custom rules
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for linting and testing
- **Conventional Commits**: Standardized commit messages

#### Performance Benchmarks
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **Time to Interactive**: < 3.5s
- **API Response Time**: < 200ms (95th percentile)

#### Security Checklist
- [ ] All API endpoints have proper authentication
- [ ] RLS policies are enabled and tested
- [ ] Input validation on all forms
- [ ] SQL injection prevention
- [ ] XSS protection with proper sanitization
- [ ] CSRF protection enabled
- [ ] Rate limiting implemented
- [ ] Sensitive data encrypted at rest
- [ ] HTTPS enforced in production
- [ ] Security headers configured

---

## Conclusion

This comprehensive blueprint provides a complete roadmap for building GlossaFlow, a professional translation project management platform. The architecture leverages modern technologies and best practices to create a scalable, maintainable, and user-friendly application.

**Key Success Factors:**
1. **Systematic Development**: Following the 8-phase roadmap ensures steady progress
2. **Quality Focus**: Comprehensive testing and quality assurance throughout
3. **User-Centric Design**: Features designed around real translator workflows
4. **Scalable Architecture**: Built to handle growth in users and data
5. **Professional Standards**: Enterprise-grade security and performance

**Next Steps:**
1. Set up development environment and initialize project
2. Configure Supabase and Stripe accounts
3. Begin Phase 1 implementation
4. Establish CI/CD pipeline
5. Start user testing with Phase 5 MVP

The platform is designed to revolutionize how translation teams collaborate, ensuring consistency, quality, and efficiency in professional translation workflows.